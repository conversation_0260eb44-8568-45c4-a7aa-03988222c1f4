using System;
using UnityEditor;
using UnityEngine;

namespace com.luxza.unitygraphqlclient.editor
{
    public static class OperationMenu
    {
        private const string MENU_GENERATE_PREFERENCE = "Gran/UnityGraphQLClient/Generate Preference";
        private const string MENU_GENERATE_QUERY_ASSETS = "Gran/UnityGraphQLClient/Generate query assets";

        private const string MENU_ADJUST_ENTITIES_GEN = "Gran/UnityGraphQLClient/Adjust Entities.gen.cs";

        [MenuItem(MENU_GENERATE_PREFERENCE)]
        public static void GeneratePreference()
        {
            var m = new AddressablesGroupManager();
            if (!m.HasAddressableSettings)
            {
                throw new Exception("You need setup Addressables!");
            }

            if (Preference.Instance == null)
            {
                var obj = ScriptableObject.CreateInstance<Preference>();
                AssetDatabase.CreateAsset(obj, "Assets/Preference.asset");
                AssetDatabase.Refresh();
                var path = AssetDatabase.GetAssetPath(obj);
                var guid = AssetDatabase.AssetPathToGUID(path);
                m.AddAddressToGroup(guid, Preference.AssetAddress);

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }

            Selection.activeObject = Preference.Instance;
        }

        [MenuItem(MENU_GENERATE_QUERY_ASSETS)]
        public static void GenerateQueryAssets()
        {
            var g = new APIClassGenerator();
            g.GenerateQueryAssets();
        }

        [MenuItem(MENU_ADJUST_ENTITIES_GEN)]
        public static void AdjustEntitiesGen()
        {
            EntitiesGenAdjuster.UpdateAllInterfacesToUnionType();
        }
    }
}