using NUnit.Framework;
using Moq;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.domains.player;
using System.Globalization;
using System.Threading;
using Cysharp.Threading.Tasks;
using com.luxza.grandarts.domains.exceptions;
using System.Threading.Tasks;

namespace com.luxza.grandarts.tests.usecases.player
{
    [TestFixture]
    public class GetPlayerUsecaseTest
    {
        private Mock<IPlayerRepository> _mockPlayerServiceAPIClient;
        private GetPlayerUseCase _getPlayerUsecase;

        [SetUp]
        public void SetUp()
        {
            _mockPlayerServiceAPIClient = new Mock<IPlayerRepository>();
            _getPlayerUsecase = new GetPlayerUseCase(_mockPlayerServiceAPIClient.Object);
        }

        [Test]
        public async Task ExecuteAssync_ShouldReturnPlayer_WhenPlayerExists()
        {
            // Arrange
            var playerId = new PlayerId(1);
            var granId = new GranId("GranId");
            var name = "PlayerName";
            var iconPath = "icon.png";
            var boardSize = BoardSize.Inch155;
            var region = RegionInfo.CurrentRegion;
            var cancellationToken = new CancellationToken();
            IPlayer player1 = new Player(playerId, granId, name, iconPath, boardSize, region);
            _mockPlayerServiceAPIClient.Setup(client => client.GetPlayerAsync(playerId, cancellationToken))
                .Returns(UniTask.FromResult(player1));

            // Act
            var result = await _getPlayerUsecase.GetPlayerAsync(playerId, cancellationToken);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(player1.Id, result.Id);
            Assert.AreEqual(player1.Name, result.Name);
        }

        [Test]
        public void ExecuteAsync_ShouldThrowPlayerNotFound_WhenPlayerDoesNotExist()
        {
            // Arrange
            var playerId = new PlayerId(1);
            var granId = new GranId("GranId");
            var name = "PlayerName";
            var iconPath = "icon.png";
            var boardSize = BoardSize.Inch155;
            var region = RegionInfo.CurrentRegion;
            var cancellationToken = new CancellationToken();
            IPlayer player1 = new Player(playerId, granId, name, iconPath, boardSize, region);
            _mockPlayerServiceAPIClient.Setup(client => client.GetPlayerAsync(playerId, cancellationToken))
                .Throws(new PlayerNotFound());

            // Act & Assert
            Assert.ThrowsAsync<PlayerNotFound>(async () => await _getPlayerUsecase.GetPlayerAsync(playerId, cancellationToken));
        }
    }
}