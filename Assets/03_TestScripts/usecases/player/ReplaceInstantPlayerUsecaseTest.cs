using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.player;
using Cysharp.Threading.Tasks;
using Moq;
using NUnit.Framework;

namespace com.luxza.grandarts.tests.usecases.player
{
    public class ReplaceInstantPlayerUsecaseTest
    {
        private Mock<IInstantPlayerRepository> _mockInstantPlayerRepository;
        private ReplaceInstantPlayerUsecase _replaceInstantPlayerUsecase;

        [SetUp]
        public void SetUp()
        {
            _mockInstantPlayerRepository = new Mock<IInstantPlayerRepository>();
            _replaceInstantPlayerUsecase = new ReplaceInstantPlayerUsecase(_mockInstantPlayerRepository.Object);
        }

        [Test]
        public void ReplaceAllAsync_NotLoggedIn_ThrowsSessionTimeOutException()
        {
            // Arrange
            ApplicationAuth.LoggedInUser = null;

            // Act & Assert
            Assert.ThrowsAsync<SessionTimeOutException>(async () => await _replaceInstantPlayerUsecase.ExecuteAsync(CancellationToken.None));
        }

        [Test]
        public async Task ReplaceAllAsync_LoggedIn_ReplacesInstantPlayers()
        {
            // Arrange
            var user = new User(new UserId(1), new EmailAddress("<EMAIL>"), new UserPassword("password"), true, domains.language.Language.English, new UserSettings());
            var player = new Player(new PlayerId(1), new GranId("GranID"), "test", "", BoardSize.Inch155, RegionInfo.CurrentRegion);
            user.AddPlayer(player);
            user.SwitchPlayer(player.Id);
            ApplicationAuth.LoggedInUser = user;
            IEnumerable<ReadonlyPlayer> mockPlayers = new List<ReadonlyPlayer> {
                new(new PlayerId(1), new GranId("gran1"), "name", "", BoardSize.Inch155, RegionInfo.CurrentRegion),
                new(new PlayerId(2), new GranId("gran2"), "name", "", BoardSize.Inch155, RegionInfo.CurrentRegion) };
            _mockInstantPlayerRepository.Setup(repo => repo.GetAllAsync(player.BoardSize, CancellationToken.None)).Returns(UniTask.FromResult(mockPlayers));

            // Act
            await _replaceInstantPlayerUsecase.ExecuteAsync(CancellationToken.None);

            // Assert
            CollectionAssert.AreEquivalent(mockPlayers, ApplicationAuth.LoggedInUser.CurrentPlayer.InstantPlayers.Items);
        }
    }
}