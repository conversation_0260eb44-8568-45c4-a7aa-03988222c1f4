using System.Threading;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game.round;
using Cysharp.Threading.Tasks;
using Moq;
using NUnit.Framework;

namespace com.luxza.grandarts.tests.usecases.player
{
    [TestFixture]
    public class UpdatePlayerSettingUseCaseTest
    {
        private Mock<IPlayerService> _mockRepository;
        private UpdatePlayerSettingUseCase _useCase;
        private domains.player.Player _player;
        private PlayerSetting _setting;

        [SetUp]
        public void SetUp()
        {
            _mockRepository = new Mock<IPlayerService>();
            _setting = PlayerSetting.DefaultForSteelPlayer(new PlayerId(1));
            _player = new domains.player.Player(new PlayerId(1), new GranId("1"), "test", "", domains.player.BoardSize.Inch155, new System.Globalization.RegionInfo("JP"), _setting);

            _useCase = new UpdatePlayerSettingUseCase(_mockRepository.Object);
        }

        [Test]
        public async Task TestUpdatePlayerSettingAsync()
        {
            bool playCaller = false;
            bool proMode = false;
            RoundInputType inputType = RoundInputType.Segment;
            PlayerSetting expecdPlayerSetting = new PlayerSetting(new PlayerId(1), enableCaller: playCaller, enableProMode: proMode, roundInputType: inputType);
            var cancellationToken = new CancellationToken();
            // Arrange
            _mockRepository.Setup(client => client.UpdatePlayerSettingAsync(_player.Id, It.IsAny<PlayerSetting>(), cancellationToken))
                .Returns(UniTask.FromResult(expecdPlayerSetting));

            // Act
            var result = await _useCase.UpdatePlayerSettingAsync(_player, playCaller, proMode, inputType, InputDevice.NoDevice, cancellationToken);

            // Assert
            Assert.AreEqual(playCaller, result.EnableCaller);
            Assert.AreEqual(proMode, result.EnableProMode);
            Assert.AreEqual(inputType.ToString(), result.RoundInputType.ToString());
            _mockRepository.Verify(p => p.UpdatePlayerSettingAsync(_player.Id, It.IsAny<PlayerSetting>(), cancellationToken), Times.Once);
        }
    }
}