
using NUnit.Framework;
using Moq;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.player;
using Cysharp.Threading.Tasks;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.user;
using System.Globalization;
using System.Threading;

namespace com.luxza.grandarts.tests.usecases.player
{
    public class AddInstantPlayerUsecaseTest
    {
        private Mock<IInstantPlayerRepository> _mockRepository;
        private AddInstantPlayerUsecase _usecase;

        [SetUp]
        public void Setup()
        {
            _mockRepository = new Mock<IInstantPlayerRepository>();
            _usecase = new AddInstantPlayerUsecase(_mockRepository.Object);
        }

        [Test]
        public void AddAsync_ShouldThrowSessionTimeOutException_WhenUserIsNotLoggedIn()
        {
            // Arrange
            ApplicationAuth.LoggedInUser = null;

            // Act & Assert
            Assert.ThrowsAsync<SessionTimeOutException>(async () => await _usecase.AddAsync("TestPlayer", CancellationToken.None));
        }

        [Test]
        public async Task AddAsync_ShouldAddPlayer_WhenUserIsLoggedIn()
        {
            // Arrange
            var user = new User(new UserId(1), new EmailAddress("<EMAIL>"), new UserPassword("password"), true, domains.language.Language.English, new UserSettings());
            var mockPlayer = new Player(new PlayerId(1), new GranId("GranID"), "test", "", BoardSize.Inch155, RegionInfo.CurrentRegion);
            user.AddPlayer(mockPlayer);
            user.SwitchPlayer(new PlayerId(1));
            ApplicationAuth.LoggedInUser = user;

            // Act
            var result = await _usecase.AddAsync("TestPlayer", CancellationToken.None);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual("TestPlayer", result.Name);
            _mockRepository.Verify(r => r.SaveAsync(result, CancellationToken.None), Times.Once);
        }
    }
}