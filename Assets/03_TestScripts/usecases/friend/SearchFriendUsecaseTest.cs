using System.Threading;
using System.Threading.Tasks;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.utils.dataStructure;
using com.luxza.grandarts.usecases.friend;
using Moq;
using NUnit.Framework;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.tests.usecases.friend
{
    public class SearchFriendUsecaseTest
    {
        private Mock<IFriendServiceAPIClient> _friendServiceAPIClientMock;
        private SearchFriendUsecase _searchFriendUsecase;

        [SetUp]
        public void SetUp()
        {
            _friendServiceAPIClientMock = new Mock<IFriendServiceAPIClient>();
            _searchFriendUsecase = new SearchFriendUsecase(_friendServiceAPIClientMock.Object);
        }

        [Test]
        public async Task ExecuteAsync_ShouldReturnPageableListOfReadonlyPlayer()
        {
            // Arrange
            var filter = new FriendSearchFilter();
            var cancellationToken = CancellationToken.None;
            var expectedResult = new PageableList<ReadonlyPlayer>(10, 100, (no, ct) =>
            {
                return UniTask.FromResult(new PageableListPage<ReadonlyPlayer>(no, new ReadonlyPlayer[] {
                    new Mock<IPlayer>().Object as ReadonlyPlayer,
                    new Mock<IPlayer>().Object as ReadonlyPlayer,
                    new Mock<IPlayer>().Object as ReadonlyPlayer,
                    new Mock<IPlayer>().Object as ReadonlyPlayer,
                    new Mock<IPlayer>().Object as ReadonlyPlayer,
                }));
            });
            _friendServiceAPIClientMock
                .Setup(client => client.SearchFriendAsync(filter, cancellationToken))
                .Returns(UniTask.FromResult(expectedResult));

            // Act
            var result = await _searchFriendUsecase.ExecuteAsync(filter, cancellationToken);

            // Assert
            Assert.AreEqual(expectedResult, result);
            _friendServiceAPIClientMock.Verify(client => client.SearchFriendAsync(filter, cancellationToken), Times.Once);
        }

        [Test]
        public void ExecuteAsync_ShouldThrowException_WhenServiceThrowsException()
        {
            // Arrange
            var filter = new FriendSearchFilter();
            var cancellationToken = CancellationToken.None;
            _friendServiceAPIClientMock
                .Setup(client => client.SearchFriendAsync(filter, cancellationToken))
                .Throws(new System.Exception("Service error"));

            // Act & Assert
            Assert.ThrowsAsync<System.Exception>(async () => await _searchFriendUsecase.ExecuteAsync(filter, cancellationToken));
            _friendServiceAPIClientMock.Verify(client => client.SearchFriendAsync(filter, cancellationToken), Times.Once);
        }
    }
}