using System;
using com.luxza.grandarts.domains.game.cpu;
using com.luxza.grandartslogic.domain.game;
using com.luxza.granlog;
using NUnit.Framework;

namespace com.luxza.grandarts.tests.domains.game.cpu
{
    public class HitCandidatesCombinationsTest
    {
        [Test]
        public void GetAllCombinations_WithMaxThrowCount1_ReturnsCorrectCombinations()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20, SegmentCode.S20_In };
            var maxThrowCount = 1;

            // Act
            var result = HitCandidatesCombinations.GetAllCombinations(codes, maxThrowCount);

            // Assert
            Assert.AreEqual(2, result.Count);
            CollectionAssert.Contains(result, new[] { SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In });
        }

        [Test]
        public void GetAllCombinations_WithMaxThrowCount2_ReturnsCorrectCombinations()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20, SegmentCode.S20_In };
            var maxThrowCount = 2;

            // Act
            var result = HitCandidatesCombinations.GetAllCombinations(codes, maxThrowCount);

            // Assert
            Assert.AreEqual(4, result.Count); // 2*2 = 4 double combinations

            // Double combinations only (length = maxThrowCount = 2)
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.S20_In });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.S20_In });
        }

        [Test]
        public void GetAllCombinations_WithMaxThrowCount3_ReturnsCorrectCombinations()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20, SegmentCode.S20_In };
            var maxThrowCount = 3;

            // Act
            var result = HitCandidatesCombinations.GetAllCombinations(codes, maxThrowCount);

            // Assert
            Assert.AreEqual(8, result.Count); // 2*2*2 = 8 triple combinations

            // Triple combinations only (length = maxThrowCount = 3)
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.T20, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.T20, SegmentCode.S20_In });
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.S20_In, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.T20, SegmentCode.S20_In, SegmentCode.S20_In });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.T20, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.T20, SegmentCode.S20_In });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.S20_In, SegmentCode.T20 });
            CollectionAssert.Contains(result, new[] { SegmentCode.S20_In, SegmentCode.S20_In, SegmentCode.S20_In });
        }

        [Test]
        public void GetAllCombinations_WithSingleCode_ReturnsCorrectCount()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20 };

            // Act & Assert
            var result1 = HitCandidatesCombinations.GetAllCombinations(codes, 1);
            Assert.AreEqual(1, result1.Count); // 1^1 = 1

            var result2 = HitCandidatesCombinations.GetAllCombinations(codes, 2);
            Assert.AreEqual(1, result2.Count); // 1^2 = 1

            var result3 = HitCandidatesCombinations.GetAllCombinations(codes, 3);
            Assert.AreEqual(1, result3.Count); // 1^3 = 1
        }

        [Test]
        public void GetAllCombinations_WithThreeCodes_ReturnsCorrectCount()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20, SegmentCode.S20_In, SegmentCode.D20 };

            // Act & Assert
            var result1 = HitCandidatesCombinations.GetAllCombinations(codes, 1);
            Assert.AreEqual(3, result1.Count); // 3^1 = 3

            var result2 = HitCandidatesCombinations.GetAllCombinations(codes, 2);
            Assert.AreEqual(9, result2.Count); // 3^2 = 9

            var result3 = HitCandidatesCombinations.GetAllCombinations(codes, 3);
            Assert.AreEqual(27, result3.Count); // 3^3 = 27
        }

        [Test]
        public void GetAllCombinations_WithEmptyArray_ReturnsEmptyList()
        {
            // Arrange
            var codes = new SegmentCode[0];

            // Act
            var result = HitCandidatesCombinations.GetAllCombinations(codes, 1);

            // Assert
            Assert.AreEqual(0, result.Count);
        }

        [Test]
        public void GetAllCombinations_WithMaxThrowCountZero_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20 };

            // Act & Assert
            var ex = Assert.Throws<ArgumentOutOfRangeException>(() =>
                HitCandidatesCombinations.GetAllCombinations(codes, 0));
            Assert.AreEqual("maxThrowCount", ex.ParamName);
            Assert.That(ex.Message, Contains.Substring("maxThrowCount must be between 1 and 3"));
        }

        [Test]
        public void GetAllCombinations_WithNegativeMaxThrowCount_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20 };

            // Act & Assert
            var ex = Assert.Throws<ArgumentOutOfRangeException>(() =>
                HitCandidatesCombinations.GetAllCombinations(codes, -1));
            Assert.AreEqual("maxThrowCount", ex.ParamName);
            Assert.That(ex.Message, Contains.Substring("maxThrowCount must be between 1 and 3"));
        }

        [Test]
        public void GetAllCombinations_WithMaxThrowCountGreaterThanThree_ThrowsArgumentOutOfRangeException()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20 };

            // Act & Assert
            var ex = Assert.Throws<ArgumentOutOfRangeException>(() =>
                HitCandidatesCombinations.GetAllCombinations(codes, 4));
            Assert.AreEqual("maxThrowCount", ex.ParamName);
            Assert.That(ex.Message, Contains.Substring("maxThrowCount must be between 1 and 3"));
        }

        [Test]
        public void GetAllCombinations_ReturnsReadOnlyList()
        {
            // Arrange
            var codes = new SegmentCode[] { SegmentCode.T20 };

            // Act
            var result = HitCandidatesCombinations.GetAllCombinations(codes, 1);

            // Assert
            Assert.IsInstanceOf<System.Collections.ObjectModel.ReadOnlyCollection<SegmentCode[]>>(result);
        }

        [Test]
        public void GetAllCombinations_WithNullArray_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                HitCandidatesCombinations.GetAllCombinations(null, 1));
        }

        [Test]
        public void GetAllCombinations_WithMaxNineCodes_PerformanceTest()
        {
            // Arrange - Create 9 different SegmentCodes (maximum expected)
            var codes = new SegmentCode[]
            {
                SegmentCode.T20, SegmentCode.S20_In, SegmentCode.D20,
                SegmentCode.T19, SegmentCode.S19_In, SegmentCode.D19,
                SegmentCode.T18, SegmentCode.S18_In, SegmentCode.D18
            };

            // Act & Assert - Test with maxThrowCount = 1
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result1 = HitCandidatesCombinations.GetAllCombinations(codes, 1);
            stopwatch.Stop();

            Log.d($"Performance test: maxThrowCount=1 took {stopwatch.ElapsedMilliseconds}ms", "HitCandidatesCombinationsTest");

            Assert.AreEqual(9, result1.Count); // 9^1 = 9
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(10),
                "Performance test failed: maxThrowCount=1 took too long");

            // Act & Assert - Test with maxThrowCount = 2
            stopwatch.Restart();
            var result2 = HitCandidatesCombinations.GetAllCombinations(codes, 2);
            stopwatch.Stop();

            Log.d($"Performance test: maxThrowCount=2 took {stopwatch.ElapsedMilliseconds}ms", "HitCandidatesCombinationsTest");

            Assert.AreEqual(81, result2.Count); // 9^2 = 81
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(50),
                "Performance test failed: maxThrowCount=2 took too long");

            // Act & Assert - Test with maxThrowCount = 3 (maximum combinations: 729)
            stopwatch.Restart();
            var result3 = HitCandidatesCombinations.GetAllCombinations(codes, 3);
            stopwatch.Stop();

            Log.d($"Performance test: maxThrowCount=3 took {stopwatch.ElapsedMilliseconds}ms", "HitCandidatesCombinationsTest");

            Assert.AreEqual(729, result3.Count); // 9^3 = 729
            Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(100),
                "Performance test failed: maxThrowCount=3 took too long");
        }

        [Test]
        public void GetAllCombinations_WithMaxNineCodes_MemoryUsageTest()
        {
            // Arrange
            var codes = new SegmentCode[]
            {
                SegmentCode.T20, SegmentCode.S20_In, SegmentCode.D20,
                SegmentCode.T19, SegmentCode.S19_In, SegmentCode.D19,
                SegmentCode.T18, SegmentCode.S18_In, SegmentCode.D18
            };

            // Measure memory before
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            System.GC.Collect();
            var memoryBefore = System.GC.GetTotalMemory(false);

            // Act - Generate maximum combinations
            var result = HitCandidatesCombinations.GetAllCombinations(codes, 3);

            // Measure memory after
            var memoryAfter = System.GC.GetTotalMemory(false);
            var memoryUsed = memoryAfter - memoryBefore;

            // Assert - Memory usage should be reasonable (less than 1MB for 729 combinations)
            Assert.That(memoryUsed, Is.LessThan(1024 * 1024),
                $"Memory usage test failed: Used {memoryUsed} bytes (expected < 1MB)");

            // Verify result count
            Assert.AreEqual(729, result.Count);
        }

        [Test]
        public void GetAllCombinations_WithMaxNineCodes_StressTest()
        {
            // Arrange
            var codes = new SegmentCode[]
            {
                SegmentCode.T20, SegmentCode.S20_In, SegmentCode.D20,
                SegmentCode.T19, SegmentCode.S19_In, SegmentCode.D19,
                SegmentCode.T18, SegmentCode.S18_In, SegmentCode.D18
            };

            // Act & Assert - Run multiple times to ensure consistent performance
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            const int iterations = 100;

            for (int i = 0; i < iterations; i++)
            {
                var result = HitCandidatesCombinations.GetAllCombinations(codes, 3);
                Assert.AreEqual(729, result.Count);
            }

            stopwatch.Stop();
            var averageTime = stopwatch.ElapsedMilliseconds / (double)iterations;

            Assert.That(averageTime, Is.LessThan(10),
                $"Stress test failed: Average time per call was {averageTime:F2}ms (expected < 10ms)");
        }
    }
}