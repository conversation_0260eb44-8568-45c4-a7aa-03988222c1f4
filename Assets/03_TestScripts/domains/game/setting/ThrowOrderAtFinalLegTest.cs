using NUnit.Framework;
using com.luxza.grandarts.domains.game.setting;

namespace com.luxza.grandarts.tests.domains.game.setting
{
    [TestFixture]
    public class ThrowOrderAtFinalLegTest
    {
        [Test]
        public void ThrowOrderAtFinalLeg_Cork_ShouldBeCork()
        {
            Assert.AreEqual(ThrowOrderAtFinalLeg.Cork, ThrowOrderAtFinalLeg.Cork);
        }

        [Test]
        public void ThrowOrderAtFinalLeg_Random_ShouldBeRandom()
        {
            Assert.AreEqual(ThrowOrderAtFinalLeg.Random, ThrowOrderAtFinalLeg.Random);
        }

        [Test]
        public void ThrowOrderAtFinalLeg_LooserFirst_ShouldBeLooserFirst()
        {
            Assert.AreEqual(ThrowOrderAtFinalLeg.LooserFirst, ThrowOrderAtFinalLeg.LooserFirst);
        }

        [Test]
        public void ThrowOrderAtFinalLeg_TakeTurn_ShouldBeTakeTurn()
        {
            Assert.AreEqual(ThrowOrderAtFinalLeg.TakeTurn, ThrowOrderAtFinalLeg.TakeTurn);
        }
    }
}