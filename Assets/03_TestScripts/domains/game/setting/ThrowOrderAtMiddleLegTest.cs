using NUnit.Framework;
using com.luxza.grandarts.domains.game.setting;

namespace com.luxza.grandarts.tests.domains.game.setting
{
    [TestFixture]
    public class ThrowOrderAtMiddleLegTest
    {
        [Test]
        public void ThrowOrderAtMiddleLeg_LooserFirst_ShouldBeLooserFirst()
        {
            Assert.AreEqual(ThrowOrderAtMiddleLeg.LooserFirst, ThrowOrderAtMiddleLeg.LooserFirst);
        }

        [Test]
        public void ThrowOrderAtMiddleLeg_TakeTurn_ShouldBeTakeTurn()
        {
            Assert.AreEqual(ThrowOrderAtMiddleLeg.TakeTurn, ThrowOrderAtMiddleLeg.TakeTurn);
        }

        [Test]
        public void ThrowOrderAtMiddleLeg_EnumValues_ShouldMatchExpected()
        {
            Assert.AreEqual(0, (int)ThrowOrderAtMiddleLeg.LooserFirst);
            Assert.AreEqual(1, (int)ThrowOrderAtMiddleLeg.TakeTurn);
        }
    }
}