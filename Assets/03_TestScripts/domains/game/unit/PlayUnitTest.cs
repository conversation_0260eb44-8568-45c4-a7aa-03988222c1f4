using System;
using System.Linq;
using NUnit.Framework;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using com.luxza.grandarts.domains.game.unit;
using Moq;

namespace com.luxza.grandarts.tests.domains.game.unit
{
    [TestFixture]
    public class PlayUnitTest
    {
        private PlayUnitMember[] CreateMockPlayers(int count, BoardSize boardSize)
        {
            return Enumerable.Range(1, count).Select(i =>
            {
                var mock = new Mock<IPlayer>();
                mock.Setup(p => p.Id).Returns(new PlayerId(i));
                mock.Setup(p => p.GranId).Returns(new GranId($"GranID_{i}"));
                mock.Setup(p => p.OnlineMatchTotal).Returns(i * 10);
                mock.Setup(p => p.BoardSize).Returns(boardSize);
                return new PlayUnitMember(mock.Object, DeviceId.MyDeviceId);
            }).ToArray();
        }

        [Test]
        public void Constructor_ShouldThrowException_WhenMembersAreNull()
        {
            Assert.Throws<ArgumentException>(() => new PlayUnit(null));
        }

        [Test]
        public void Constructor_ShouldThrowException_WhenMembersAreEmpty()
        {
            Assert.Throws<ArgumentException>(() => new PlayUnit(Array.Empty<PlayUnitMember>()));
        }

        [Test]
        public void Constructor_ShouldSetZeroOne80StatsToEmpty_WhenNullPassed()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.AreEqual(Rank.NoData, unit.ZeroOne80Stats.Rank);
        }

        [Test]
        public void Constructor_ShouldSetZeroOne100StatsToEmpty_WhenNullPassed()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.AreEqual(Rank.NoData, unit.ZeroOne100Stats.Rank);
        }

        [Test]
        public void Constructor_ShouldSetCricket80StatsToEmpty_WhenNullPassed()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.AreEqual(Rank.NoData, unit.Cricket80Stats.Rank);
        }

        [Test]
        public void Constructor_ShouldSetCricket100StatsToEmpty_WhenNullPassed()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.AreEqual(Rank.NoData, unit.Cricket100Stats.Rank);
        }

        [Test]
        public void Constructor_ShouldThrowNotSupportedException_WhenMembersAreMoreThanFour()
        {
            Assert.Throws<NotSupportedException>(() => new PlayUnit(CreateMockPlayers(5, BoardSize.Inch155)));
        }

        [Test]
        public void Constructor_ShouldSetHostPlayerIdToFirstMember_WhenHostPlayerIdIsNull()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.AreEqual(players[0].Player.Id, unit.HostPlayerId);
        }

        [Test]
        public void Constructor_ShouldSetHostPlayerId_WhenHostPlayerIdIsProvided()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var hostPlayerId = players[1].Player.Id;
            var unit = new PlayUnit(players, hostPlayerId);
            Assert.AreEqual(hostPlayerId, unit.HostPlayerId);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentException_WhenHostPlayerIdIsNotInMembers()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var invalidHostPlayerId = new PlayerId(999);
            Assert.Throws<ArgumentException>(() => new PlayUnit(players, invalidHostPlayerId));
        }

        [Test]
        public void HostPlayer_ShouldReturnCorrectPlayer_WhenHostPlayerIdIsProvided()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var hostPlayerId = players[1].Player.Id;
            var unit = new PlayUnit(players, hostPlayerId);
            Assert.AreEqual(players[1].Player, unit.HostPlayer);
        }

        [Test]
        public void HostGranId_ShouldReturnCorrectGranId_WhenHostPlayerIdIsProvided()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var hostPlayerId = players[1].Player.Id;
            var unit = new PlayUnit(players, hostPlayerId);
            Assert.AreEqual(players[1].Player.GranId, unit.HostGranId);
        }

        [Test]
        public void HostPlayer_ShouldReturnFirstPlayer_WhenHostPlayerIdIsNull()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.AreEqual(players[0].Player, unit.HostPlayer);
        }

        [Test]
        public void IsSingleUnit_ShouldReturnTrue_WhenOneMember()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.IsTrue(unit.IsSingleUnit);
        }

        [Test]
        public void IsDoublesUnit_ShouldReturnTrue_WhenTwoMembers()
        {
            var unit = new PlayUnit(CreateMockPlayers(2, BoardSize.Inch155));
            Assert.IsTrue(unit.IsDoublesUnit);
        }

        [Test]
        public void IsTriosUnit_ShouldReturnTrue_WhenThreeMembers()
        {
            var unit = new PlayUnit(CreateMockPlayers(3, BoardSize.Inch155));
            Assert.IsTrue(unit.IsTriosUnit);
        }

        [Test]
        public void IsGaronUnit_ShouldReturnTrue_WhenFourMembers()
        {
            var unit = new PlayUnit(CreateMockPlayers(4, BoardSize.Inch155));
            Assert.IsTrue(unit.IsGaronUnit);
        }

        [Test]
        public void MemberCount_ShouldReturnCorrectCount()
        {
            var unit = new PlayUnit(CreateMockPlayers(3, BoardSize.Inch155));
            Assert.AreEqual(3, unit.MemberCount);
        }

        [Test]
        public void OnlineMatchTotal_ShouldReturnSumOfAllMembers()
        {
            var unit = new PlayUnit(CreateMockPlayers(3, BoardSize.Inch155));
            Assert.AreEqual(60, unit.OnlineMatchTotal);
        }

        [Test]
        public void IsSteelUnit_ShouldReturnTrue_WhenBoardSizeIsSteel()
        {
            var players = CreateMockPlayers(1, BoardSize.Steel);
            var unit = new PlayUnit(players);
            Assert.IsTrue(unit.IsSteelUnit);
        }

        [Test]
        public void IsSoftTipUnit_ShouldReturnTrue_WhenBoardSizeIsInch132OrInch155()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.IsTrue(unit.IsSoftTipUnit);

            var unit2 = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch132));
            Assert.IsTrue(unit2.IsSoftTipUnit);
        }

        [Test]
        public void IsMember_ShouldReturnTrue_WhenPlayerIdExists()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.IsTrue(unit.IsMember(players[0].Player.Id));
        }

        [Test]
        public void IsMember_ShouldReturnFalse_WhenPlayerIdDoesNotExist()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.IsFalse(unit.IsMember(new PlayerId(999)));
        }

        [Test]
        public void TryFindMember_ShouldReturnTrueAndMember_WhenPlayerIdExists()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.IsTrue(unit.TryFindMember(players[0].Player.Id, out var member));
            Assert.AreEqual(players[0].Player, member);
        }

        [Test]
        public void TryFindMember_ShouldReturnFalseAndNull_WhenPlayerIdDoesNotExist()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.IsFalse(unit.TryFindMember(new PlayerId(999), out var member));
            Assert.IsNull(member);
        }

        [Test]
        public void Is132Unit_ShouldReturnTrue_WhenBoardSizeIsInch132()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch132));
            Assert.IsTrue(unit.Is132Unit);
            Assert.IsFalse(unit.Is155Unit);
            Assert.IsTrue(unit.IsSoftTipUnit);
            Assert.IsFalse(unit.IsSteelUnit);
        }

        [Test]
        public void Is132Unit_ShouldReturnFalse_WhenBoardSizeIsNotInch132()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.IsFalse(unit.Is132Unit);
            Assert.IsTrue(unit.Is155Unit);
            Assert.IsTrue(unit.IsSoftTipUnit);
            Assert.IsFalse(unit.IsSteelUnit);
        }

        [Test]
        public void Is155Unit_ShouldReturnTrue_WhenBoardSizeIsInch155()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch155));
            Assert.IsTrue(unit.Is155Unit);
            Assert.IsFalse(unit.Is132Unit);
            Assert.IsTrue(unit.IsSoftTipUnit);
            Assert.IsFalse(unit.IsSteelUnit);
        }

        [Test]
        public void Is155Unit_ShouldReturnFalse_WhenBoardSizeIsNotInch155()
        {
            var unit = new PlayUnit(CreateMockPlayers(1, BoardSize.Inch132));
            Assert.IsFalse(unit.Is155Unit);
            Assert.IsTrue(unit.Is132Unit);
            Assert.IsTrue(unit.IsSoftTipUnit);
            Assert.IsFalse(unit.IsSteelUnit);
        }

        [Test]
        public void IsAllMemberJoinedFromSameLocation_ShouldReturnTrue_WhenAllMembersFromSameLocation()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            var unit = new PlayUnit(players);
            Assert.IsTrue(unit.IsAllMemberJoinedFromSameLocation);
        }

        [Test]
        public void IsAllMemberJoinedFromSameLocation_ShouldReturnFalse_WhenMembersFromDifferentLocations()
        {
            var players = CreateMockPlayers(2, BoardSize.Inch155);
            players[1] = new PlayUnitMember(players[1].Player, new DeviceId("DifferentLocation"));
            var unit = new PlayUnit(players);
            Assert.IsFalse(unit.IsAllMemberJoinedFromSameLocation);
        }

        [Test]
        public void ZeroOne80Stats_ShouldBeAverageOfMembers()
        {
            var players = Enumerable.Range(1, 2).Select(i =>
            {
                var mock = new Mock<IPlayer>();
                mock.Setup(p => p.Id).Returns(new PlayerId(i));
                mock.Setup(p => p.GranId).Returns(new GranId($"GranID_{i}"));
                mock.Setup(p => p.OnlineMatchTotal).Returns(i * 10);
                mock.Setup(p => p.BoardSize).Returns(BoardSize.Inch155);
                mock.Setup(p => p.ZeroOne80Stats).Returns(Stats.CreateZeroOne80StatsByRating(i * 50));
                return new PlayUnitMember(mock.Object, DeviceId.MyDeviceId);
            }).ToArray();
            var unit = new PlayUnit(players);
            Assert.AreEqual(75, unit.ZeroOne80Stats.Rating);
        }

        [Test]
        public void ZeroOne100Stats_ShouldBeAverageOfMembers()
        {
            var players = Enumerable.Range(1, 2).Select(i =>
            {
                var mock = new Mock<IPlayer>();
                mock.Setup(p => p.Id).Returns(new PlayerId(i));
                mock.Setup(p => p.GranId).Returns(new GranId($"GranID_{i}"));
                mock.Setup(p => p.OnlineMatchTotal).Returns(i * 10);
                mock.Setup(p => p.BoardSize).Returns(BoardSize.Inch155);
                mock.Setup(p => p.ZeroOne100Stats).Returns(Stats.CreateZeroOne100StatsByRating(i * 60));
                return new PlayUnitMember(mock.Object, DeviceId.MyDeviceId);
            }).ToArray();
            var unit = new PlayUnit(players);
            Assert.AreEqual(90, unit.ZeroOne100Stats.Rating);
        }

        [Test]
        public void Cricket80Stats_ShouldBeAverageOfMembers()
        {
            var players = Enumerable.Range(1, 2).Select(i =>
            {
                var mock = new Mock<IPlayer>();
                mock.Setup(p => p.Id).Returns(new PlayerId(i));
                mock.Setup(p => p.GranId).Returns(new GranId($"GranID_{i}"));
                mock.Setup(p => p.OnlineMatchTotal).Returns(i * 10);
                mock.Setup(p => p.BoardSize).Returns(BoardSize.Inch155);
                mock.Setup(p => p.CR80Stats).Returns(Stats.CreateCR80StatsByRating(i * 70));
                return new PlayUnitMember(mock.Object, DeviceId.MyDeviceId);
            }).ToArray();
            var unit = new PlayUnit(players);
            Assert.AreEqual(105, unit.Cricket80Stats.Rating);
        }

        [Test]
        public void Cricket100Stats_ShouldBeAverageOfMembers()
        {
            var players = Enumerable.Range(1, 2).Select(i =>
            {
                var mock = new Mock<IPlayer>();
                mock.Setup(p => p.Id).Returns(new PlayerId(i));
                mock.Setup(p => p.GranId).Returns(new GranId($"GranID_{i}"));
                mock.Setup(p => p.OnlineMatchTotal).Returns(i * 10);
                mock.Setup(p => p.BoardSize).Returns(BoardSize.Inch155);
                mock.Setup(p => p.CR100Stats).Returns(Stats.CreateCR100StatsByRating(i * 80));
                return new PlayUnitMember(mock.Object, DeviceId.MyDeviceId);
            }).ToArray();
            var unit = new PlayUnit(players);
            Assert.AreEqual(120, unit.Cricket100Stats.Rating);
        }
    }
}