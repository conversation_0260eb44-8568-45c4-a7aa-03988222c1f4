using NUnit.Framework;
using com.luxza.grandarts.domains.game.factory;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.player;
using Moq;
using com.luxza.grandartslogic.domain.game;

namespace com.luxza.grandarts.tests.domains.game.factory {
    [TestFixture]
    public class DartsLogicUnitFactoryTest {
        [Test]
        public void Create_ShouldReturnUnit_WhenValidPlayUnitAndTargetStatsRateProvided() {
            // Arrange
            var mockPlayer = new Mock<IPlayer>();
            mockPlayer.Setup(p => p.Settings.TargetStatsRate).Returns(TargetStatsRate._100);
            var playUnit = new PlayUnit(new PlayUnitMember[] {
                new PlayUnitMember(mockPlayer.Object, DeviceId.MyDeviceId)
            });

            var convertedPlayer = new grandartslogic.Player(
                "GranId123",
                1,
                boardSize: grandartslogic.BoardSize.Steel,
                roundInputType: grandartslogic.domain.game.round.RoundInputType.Segment,
                rating01: 5.0f,
                ratingCR: 4.0f,
                isFreeGuest: false,
                isCPUPlayer: true);

            mockPlayer.Setup(p => p.Convert( It.IsAny<GameCode>(), It.IsAny<bool>(), It.IsAny<TargetStatsRate>())).Returns(convertedPlayer);

            // Act
            var result = DartsLogicUnitFactory.Create(playUnit, GameCode._301, mockPlayer.Object.Settings.TargetStatsRate);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsInstanceOf<grandartslogic.Unit>(result);
        }

        [Test]
        public void Create_ShouldCallConvertOnEachMember() {
            // Arrange
            var mockPlayer1 = new Mock<IPlayer>();
            mockPlayer1.Setup(p => p.Settings.TargetStatsRate).Returns(TargetStatsRate._100);
            var mockPlayer2 = new Mock<IPlayer>();
            mockPlayer2.Setup(p => p.Settings.TargetStatsRate).Returns(TargetStatsRate._100);
            var playUnit = new PlayUnit(new PlayUnitMember[] {
                new PlayUnitMember(mockPlayer1.Object, DeviceId.MyDeviceId),
                new PlayUnitMember(mockPlayer2.Object, DeviceId.MyDeviceId),
            });

            var convertedPlayer1 = new grandartslogic.Player(
                "GranId123",
                1,
                boardSize: grandartslogic.BoardSize.Steel,
                roundInputType: grandartslogic.domain.game.round.RoundInputType.Segment,
                rating01: 5.0f,
                ratingCR: 4.0f,
                isFreeGuest: false,
                isCPUPlayer: true);
            var convertedPlayer2 = new grandartslogic.Player(
                "GranId1234",
                2,
                boardSize: grandartslogic.BoardSize.Steel,
                roundInputType: grandartslogic.domain.game.round.RoundInputType.Segment,
                rating01: 5.0f,
                ratingCR: 4.0f,
                isFreeGuest: false,
                isCPUPlayer: true);

            mockPlayer1.Setup(p => p.Convert(It.IsAny<GameCode>(), It.IsAny<bool>(),It.IsAny<TargetStatsRate>())).Returns(convertedPlayer1);
            mockPlayer2.Setup(p => p.Convert(It.IsAny<GameCode>(), It.IsAny<bool>(),It.IsAny<TargetStatsRate>())).Returns(convertedPlayer2);

            // Act
            var result = DartsLogicUnitFactory.Create(playUnit, GameCode._301, TargetStatsRate._100);

            // Assert
            mockPlayer1.Verify(p => p.Convert(It.IsAny<GameCode>(), It.IsAny<bool>(),It.IsAny<TargetStatsRate>()), Times.Once);
            mockPlayer2.Verify(p => p.Convert(It.IsAny<GameCode>(), It.IsAny<bool>(),It.IsAny<TargetStatsRate>()), Times.Once);
        }
    }
}