using NUnit.Framework;
using com.luxza.grandarts.domains.player;

namespace com.luxza.grandarts.tests.domains.player
{
    [TestFixture]
    public class GranIdValidatorTest
    {
        [Test]
        public void Validate_ShouldReturnFalse_WhenValueIsNull()
        {
            var result = GranIdValidator.Validate(null);
            Assert.IsFalse(result);
        }

        [Test]
        public void Validate_ShouldReturnFalse_WhenValueIsEmpty()
        {
            var result = GranIdValidator.Validate(string.Empty);
            Assert.IsFalse(result);
        }

        [Test]
        public void Validate_ShouldReturnFalse_WhenValueIsNotSingleByteAlphaNumeric()
        {
            var result = GranIdValidator.Validate("abc123!");
            Assert.IsFalse(result);
        }

        [Test]
        public void Validate_ShouldReturnFalse_WhenValueIsLessThan6Characters()
        {
            var result = GranIdValidator.Validate("abc12");
            Assert.IsFalse(result);
        }

        [Test]
        public void Validate_ShouldReturnFalse_WhenValueIsMoreThan12Characters()
        {
            var result = GranIdValidator.Validate("abcdefghijklmnopq");
            Assert.IsFalse(result);
        }

        [Test]
        public void Validate_ShouldReturnTrue_WhenValueIsValid()
        {
            var result = GranIdValidator.Validate("abc123");
            Assert.IsTrue(result);
        }
    }
}