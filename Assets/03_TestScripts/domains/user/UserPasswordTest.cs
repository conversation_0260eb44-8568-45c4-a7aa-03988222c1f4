using NUnit.Framework;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.domains.exceptions;

namespace com.luxza.grandarts.tests.domains.user
{
    [TestFixture]
    public class UserPasswordTests
    {
        [Test]
        public void Constructor_WithValidPassword_ShouldSetPassword()
        {
            // Arrange
            var password = "validPass123";

            // Act
            var userPassword = new UserPassword(password);

            // Assert
            Assert.AreEqual(password, userPassword.Value);
        }

        [Test]
        public void Constructor_WithEmptyPassword_ShouldThrowInvalidPassword()
        {
            // Arrange
            var password = string.Empty;

            // Act
            // Assert
            Assert.Throws<IncorrectPasswordException>(() => new UserPassword(password));
        }

        [Test]
        public void Constructor_WithShortPassword_ShouldThrowInvalidPassword()
        {
            // Arrange
            var password = "short";

            // Act & Assert
            Assert.Throws<IncorrectPasswordException>(() => new UserPassword(password));
        }

        [Test]
        public void Constructor_WithLongPassword_ShouldThrowInvalidPassword()
        {
            // Arrange
            var password = "thisIsAVeryLongPassword";

            // Act & Assert
            Assert.Throws<IncorrectPasswordException>(() => new UserPassword(password));
        }

        [Test]
        public void Equals_WhenPasswordsAreSame_ShouldReturnTrue()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("validPass123");

            // Act
            var areEqual = password1.Equals(password2);

            // Assert
            Assert.IsTrue(areEqual);
        }

        [Test]
        public void Equals_WhenPasswordsAreDifferent_ShouldReturnFalse()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("differentPass");

            // Act
            var areEqual = password1.Equals(password2);

            // Assert
            Assert.IsFalse(areEqual);
        }

        [Test]
        public void GetHashCode_WhenPasswordsAreSame_ShouldReturnSameHashCode()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("validPass123");

            // Act
            var hash1 = password1.GetHashCode();
            var hash2 = password2.GetHashCode();

            // Assert
            Assert.AreEqual(hash1, hash2);
        }

        [Test]
        public void GetHashCode_WhenPasswordsAreDifferent_ShouldReturnDifferentHashCode()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("differentPass");

            // Act
            var hash1 = password1.GetHashCode();
            var hash2 = password2.GetHashCode();

            // Assert
            Assert.AreNotEqual(hash1, hash2);
        }

        [Test]
        public void OperatorEquals_WhenPasswordsAreSame_ShouldReturnTrue()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("validPass123");

            // Act
            var areEqual = password1 == password2;

            // Assert
            Assert.IsTrue(areEqual);
        }

        [Test]
        public void OperatorEquals_WhenPasswordsAreDifferent_ShouldReturnFalse()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("differentPass");

            // Act
            var areEqual = password1 == password2;

            // Assert
            Assert.IsFalse(areEqual);
        }

        [Test]
        public void OperatorNotEquals_WhenPasswordsAreSame_ShouldReturnFalse()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("validPass123");

            // Act
            var areNotEqual = password1 != password2;

            // Assert
            Assert.IsFalse(areNotEqual);
        }

        [Test]
        public void OperatorNotEquals_WhenPasswordsAreDifferent_ShouldReturnTrue()
        {
            // Arrange
            var password1 = new UserPassword("validPass123");
            var password2 = new UserPassword("differentPass");

            // Act
            var areNotEqual = password1 != password2;

            // Assert
            Assert.IsTrue(areNotEqual);
        }
    }
}