using NUnit.Framework;
using com.luxza.grandarts.domains.stats;
using System.Collections.Generic;
using System.Linq;

namespace com.luxza.grandarts.tests.domains.stats
{
    [TestFixture]
    public class StatsTest
    {
        [Test]
        public void CreateZeroOne80StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateZeroOne80StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable80.FindByZeroOneStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable80.FindByZeroOneStats(stats), result.Row);
        }

        [Test]
        public void CreateCR80StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateCR80StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable80.FindByCRStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable80.FindByCRStats(stats), result.Row);
        }

        [Test]
        public void CreateOverall80StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateOverall80StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable80.FindByOverAllStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable80.FindByOverAllStats(stats), result.Row);
        }

        [Test]
        public void CreateZeroOne100StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateZeroOne100StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable100.FindByZeroOneStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable100.FindByZeroOneStats(stats), result.Row);
        }

        [Test]
        public void CreateCR100StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateCR100StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable100.FindByCRStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable100.FindByCRStats(stats), result.Row);
        }

        [Test]
        public void CreateOverall100StatsByStats_ShouldReturnCorrectStats()
        {
            float stats = 50.0f;
            var result = Stats.CreateOverall100StatsByStats(stats);

            Assert.AreEqual(stats, result.Value);
            Assert.AreEqual(RankTable100.FindByOverAllStats(stats).MaxRating, result.Rating);
            Assert.AreEqual(RankTable100.FindByOverAllStats(stats), result.Row);
        }

        [Test]
        public void CreateZeroOne80StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateZeroOne80StatsByRating(rating);

            Assert.AreEqual(RankTable80.FindByRating(rating).ZeroOneStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable80.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateZeroOne100StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateZeroOne100StatsByRating(rating);

            Assert.AreEqual(RankTable100.FindByRating(rating).ZeroOneStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable100.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateCR80StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateCR80StatsByRating(rating);

            Assert.AreEqual(RankTable80.FindByRating(rating).CRStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable80.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateCR100StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateCR100StatsByRating(rating);

            Assert.AreEqual(RankTable100.FindByRating(rating).CRStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable100.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateOverall80StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateOverall80StatsByRating(rating);

            Assert.AreEqual(RankTable80.FindByRating(rating).OverAllStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable80.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateOverall100StatsByRating_ShouldReturnCorrectStats()
        {
            float rating = 50.0f;
            var result = Stats.CreateOverall100StatsByRating(rating);

            Assert.AreEqual(RankTable100.FindByRating(rating).OverAllStatsRange.Min, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable100.FindByRating(rating), result.Row);
        }

        [Test]
        public void Create80Stats_ShouldReturnCorrectStats()
        {
            float value = 50.0f;
            float rating = 50.0f;
            var result = Stats.Create80Stats(value, rating);

            Assert.AreEqual(value, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable80.FindByRating(rating), result.Row);
        }

        [Test]
        public void Create100Stats_ShouldReturnCorrectStats()
        {
            float value = 50.0f;
            float rating = 50.0f;
            var result = Stats.Create100Stats(value, rating);

            Assert.AreEqual(value, result.Value);
            Assert.AreEqual(rating, result.Rating);
            Assert.AreEqual(RankTable100.FindByRating(rating), result.Row);
        }

        [Test]
        public void CreateZeroOne80AverageStats_ShouldReturnCorrectStats()
        {
            var statsList = new List<Stats>
            {
            new Stats(50.0f, 60.0f, RankTable80.FindByRating(60.0f)),
            new Stats(70.0f, 80.0f, RankTable80.FindByRating(80.0f))
            };
            var result = Stats.CreateZeroOne80AverageStats(statsList);

            var expectedAverageValue = statsList.Average(s => s.Value);
            var expectedAverageRating = statsList.Average(s => s.Rating);
            var expectedRow = RankTable80.FindByRating(expectedAverageRating);

            Assert.AreEqual(expectedAverageValue, result.Value);
            Assert.AreEqual(expectedAverageRating, result.Rating);
            Assert.AreEqual(expectedRow, result.Row);
        }

        [Test]
        public void CreateZeroOne100AverageStats_ShouldReturnCorrectStats()
        {
            var statsList = new List<Stats>
            {
            new Stats(50.0f, 60.0f, RankTable100.FindByRating(60.0f)),
            new Stats(70.0f, 80.0f, RankTable100.FindByRating(80.0f))
            };
            var result = Stats.CreateZeroOne100AverageStats(statsList);

            var expectedAverageValue = statsList.Average(s => s.Value);
            var expectedAverageRating = statsList.Average(s => s.Rating);
            var expectedRow = RankTable100.FindByRating(expectedAverageRating);

            Assert.AreEqual(expectedAverageValue, result.Value);
            Assert.AreEqual(expectedAverageRating, result.Rating);
            Assert.AreEqual(expectedRow, result.Row);
        }

        [Test]
        public void CreateCR80AverageStats_ShouldReturnCorrectStats()
        {
            var statsList = new List<Stats>
            {
            new Stats(50.0f, 60.0f, RankTable80.FindByRating(60.0f)),
            new Stats(70.0f, 80.0f, RankTable80.FindByRating(80.0f))
            };
            var result = Stats.CreateCR80AverageStats(statsList);

            var expectedAverageValue = statsList.Average(s => s.Value);
            var expectedAverageRating = statsList.Average(s => s.Rating);
            var expectedRow = RankTable80.FindByRating(expectedAverageRating);

            Assert.AreEqual(expectedAverageValue, result.Value);
            Assert.AreEqual(expectedAverageRating, result.Rating);
            Assert.AreEqual(expectedRow, result.Row);
        }

        [Test]
        public void CreateCR100AverageStats_ShouldReturnCorrectStats()
        {
            var statsList = new List<Stats>
            {
            new Stats(50.0f, 60.0f, RankTable100.FindByRating(60.0f)),
            new Stats(70.0f, 80.0f, RankTable100.FindByRating(80.0f))
            };
            var result = Stats.CreateCR100AverageStats(statsList);

            var expectedAverageValue = statsList.Average(s => s.Value);
            var expectedAverageRating = statsList.Average(s => s.Rating);
            var expectedRow = RankTable100.FindByRating(expectedAverageRating);

            Assert.AreEqual(expectedAverageValue, result.Value);
            Assert.AreEqual(expectedAverageRating, result.Rating);
            Assert.AreEqual(expectedRow, result.Row);
        }
    }
}