using System;
using System.Threading;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.usecases.system;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.userinterface.system.commands
{
    public class InvokeActionWithOnlineAvailableCheckCommand
    {
        public async UniTask ExecuteAsync(Action actionForOnlineAvailable, CancellationToken cancellationToken)
        {
            var result = await new IsCurrentVersionAvailableOnlineUsecase(DIContainer.Instance.Resolve<IAppVersionRepository>()).ExecuteAsync(cancellationToken);
            if (result)
            {
                actionForOnlineAvailable?.Invoke();
            }
            else
            {
                MessageUtility.ShowMessageModal(
                    LocalizeString.GetLocalizedString(LocalizeKey.Notifications.CurrentVersionNotAvailableOnline),
                    GranModal.IconType.OK,
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Update), positiveAction),
                    (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Cancel), negativeAction));

                UniTask positiveAction()
                {
                    Application.Quit();
                    //todo: open app store
                    //Application.OpenURL(AppStoreLink);
                    return UniTask.CompletedTask;
                }

                UniTask negativeAction() {
                    return UniTask.CompletedTask;
                }
            }
        }
    }
}