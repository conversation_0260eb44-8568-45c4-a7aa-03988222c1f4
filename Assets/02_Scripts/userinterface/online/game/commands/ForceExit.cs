using System.Threading;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.context.online;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.usecases.online.lobby.room;
using com.luxza.grandarts.usecases.online.match;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.userinterfaces.online.game.commands
{
    public class ForceExit
    {
        private static string ConfirmSurrenderExitMessage => LocalizeString.GetLocalizedString(LocalizeKey.Notifications.ConfirmSurrenderExitMessage);
        private static string ConfirmExitMessage => LocalizeString.GetLocalizedString(LocalizeKey.Notifications.ConfirmExitMessage);
        private static string Yes => LocalizeString.GetLocalizedString(LocalizeKey.Labels.Yes);
        private static string No => LocalizeString.GetLocalizedString(LocalizeKey.Labels.No);

        public async UniTask Execute(IGameSessionContext context,bool isOpponentDisConnected, CancellationToken cancellationToken, bool withConfirmMessage = true)
        {
            if (context is IOnlineGameSessionContext onlineGameSessionContext)
            {
                await Execute(onlineGameSessionContext, isOpponentDisConnected, cancellationToken, withConfirmMessage);
            }
            else
            {
                context.Dispose();
                await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
            }
        }

        private async UniTask Execute(IOnlineGameSessionContext context,bool isOpponentDisConnected, CancellationToken cancellationToken, bool withConfirmMessage)
        {
            if (withConfirmMessage)
            {
                MessageUtility.ShowMessageModal(
                    isOpponentDisConnected ? ConfirmExitMessage : ConfirmSurrenderExitMessage,
                    iconType: ui.page.GranModal.IconType.Warn,
                    positive: (Yes, () => RunForceExitAction(context, cancellationToken)),
                    negative: (No, null)
                );
            }
            else
            {
                await RunForceExitAction(context, cancellationToken);
            }

            async UniTask RunForceExitAction(IOnlineGameSessionContext context, CancellationToken cancellationToken)
            {
                var onlineMatchClient = context.OnlineGameContext.OnlineMatchClient;
                var matchRoom = context.OnlineGameContext.DataRetriver.MatchRoom;
                if (onlineMatchClient.ConnectionStatus == ConnectionStatus.Connected)
                {
                    onlineMatchClient.NotifyExit();
                    await onlineMatchClient.WaitUntilAllMessageSentAsync(cancellationToken);
                }
                context.Dispose();
                context.OnlineGameContext.OnlineMatchClient?.Dispose();
                context.OnlineGameContext.VideoChatClient?.Dispose();
                context.OnlineGameContext.OnlineMatchServerEventListener?.Dispose();
                try
                {
                    var usecase = new ExitMatchUsecase(DIContainer.Instance.Resolve<IMatchRoomService>(), DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
                    await usecase.ExecuteAsync(matchRoom, cancellationToken);
                    await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
                }
                catch (InvalidMatchRoomStatus ex)
                {
                    if (ex.Status == MatchRoomStatus.WaitingForOpponent)
                    {
                        //already opponent exitted.
                        await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
                    }
                }
            }
        }
    }
}