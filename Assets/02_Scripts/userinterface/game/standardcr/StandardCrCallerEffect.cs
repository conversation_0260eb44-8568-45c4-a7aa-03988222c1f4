using System;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.context.online.standardcr;
using com.luxza.grandarts.domains.game.context.standardcr;
using com.luxza.grandarts.userinterfaces.online.game;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.userinterfaces.game.standardcr
{
    public class StandardCrCallerEffect : AbstractCallerEffect
    {
        private GameStandardCrSessionContext _context;

        public override void OnContextCreated(IGameSessionContext context)
        {
            if(context is OnlineStandardCrGameSessionContext onlineContext)
            {
                _context = onlineContext.GameContext;
            }
            else if (context is GameStandardCrSessionContext standardCrContext)
            {
                _context = standardCrContext;
            }
            else
            {
                throw new ArgumentException("StandardCrCallerEffect only works with GameStandardCrSessionContext");
            }
        }
        protected override void CallOverrideScore(int throwNo)
        {
            var basicGameDataRetriver = _context.DataRetriver.BasicGameDataRetriver;
            if (basicGameDataRetriver.IsReachGameEnd) return;
            _caller.Clear();
            if (basicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                if (basicGameDataRetriver.CurrentThrowerEnabledProMode && !basicGameDataRetriver.IsAllThrowFixed) return;
                
                var overrideMark = segmentInput.Throws[throwNo - 1].VirtualHitArea.Multiplier;
                if (overrideMark == 0)
                {
                    _caller.RegisterCall("NoMark");
                }
                else
                {
                    _caller.RegisterCall(overrideMark + "mark");
                }
            }
        }

        protected override void CallSegmentScore()
        {
            int markCount;
            if (
                _context.DataRetriver.BasicGameDataRetriver.CurrentThrowerEnabledProMode
            )
            {
                if (!_context.DataRetriver.BasicGameDataRetriver.IsAllThrowFixed) return;
                markCount = _context.DataRetriver.CurrentRoundMarkCount;
            }
            else
            {
                markCount = _context.DataRetriver.LatestThrowMarkCount;
            }

            if (markCount == 0)
            {
                _caller.RegisterCall("NoMark");
            }
            else
            {
                _caller.RegisterCall(markCount + "mark");
            }
        }
    }
}