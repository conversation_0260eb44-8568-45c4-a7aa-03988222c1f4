using com.luxza.grandarts.domains.game.cpu;
using com.luxza.grandartslogic.domain.game;

namespace com.luxza.grandarts.userinterfaces.game.cpu
{
    public interface ICPUAnimationFacade
    {
        void PlayThrowAnimation(ICPU target);
        void StopThrowAnimation(ICPU target, Round round);

        void StopAllThrowAnimationImmediately();

        void PlayNextThrowAnimation(ICPU target);

        void PlayStreatchAnimation(ICPU target);
        void StopStretchAnimation(ICPU target);
        void PlayClappingAnimation(ICPU target);
        void StopClappingAnimation(ICPU target);

        void PlayIdleAnimation(ICPU target);

        void PlayChangeAnimation(ICPU target);
    }
}