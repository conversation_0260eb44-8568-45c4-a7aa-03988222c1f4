using System;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.cpu;
using com.luxza.grandarts.domains.game.input;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace com.luxza.grandarts.userinterfaces.game.cpu
{
    public class CPUInput : MonoBehaviour
    {
        [SerializeField] private BasicGameContextAdoptor _adoptor;
        private ICPUAnimationFacade _cpuAnimationFacade;
        private IGameSessionContext _context;
        private Scene _cpuThrowScene;
        private Camera _cpuThrowSceneMainCamera;

        private CancellationTokenSource _cancellation = new();

        private void Start()
        {
            _adoptor.OnReady.Subscribe(async context =>
            {
                _context = context;
                if (!_context.DataRetriver.BasicGameDataRetriver.PlayUnits.SelectMany(u => u.Members).Any(p => p.Id.IsCPU())) return;
                await SceneManager.LoadSceneAsync("CPUThrow", LoadSceneMode.Additive);
                if (gameObject == null) return;
                _cpuThrowScene = SceneManager.GetSceneByName("CPUThrow");
                foreach (var g in _cpuThrowScene.GetRootGameObjects())
                {
                    if (g.TryGetComponent<Camera>(out var cam))
                    {
                        _cpuThrowSceneMainCamera = cam;
                    }

                    if (g.TryGetComponent<CPUSpawner>(out var spawner))
                    {
                        _cpuAnimationFacade = CPUAnimationFacadeFactory.Create(spawner, _context);
                    }
                }

                //context.GameEventPublisher.OnStartMatch += OnStartMatch;
                context.GameEventPublisher.OnRoundStart += OnRoundStart;
                context.GameEventPublisher.OnUpdateProgress += OnUpdateProgress;
                context.GameEventPublisher.OnRoundReverse += OnRoundReverse;
                context.GameEventPublisher.OnThrowReverse += OnThrowReverse;
                context.GameEventPublisher.OnFinishMatch += OnFinishMatch;

                if (context.DataRetriver.BasicGameDataRetriver.HasGameAlreadyStarted)
                {
                    CPUFirstThrowAction();
                }

            }).RegisterTo(destroyCancellationToken);
        }

        private void OnRoundStart(Player sender)
        {
            if (sender.IsCPUPlayer)
            {
                CPUFirstThrowAction();
            }
            else
            {
                _cpuThrowSceneMainCamera.gameObject.SetActive(false);
            }
        }

        private void OnRoundReverse(grandartslogic.Unit sender)
        {
            if (_cancellation != null && !_cancellation.IsCancellationRequested)
            {
                _cancellation.Cancel();
            }

            _cpuAnimationFacade.StopAllThrowAnimationImmediately();
        }

        private void OnThrowReverse(grandartslogic.Unit sender)
        {
            if (_cancellation != null && !_cancellation.IsCancellationRequested)
            {
                _cancellation.Cancel();
            }

            _cpuAnimationFacade.StopAllThrowAnimationImmediately();
        }

        private void OnFinishMatch(MatchFinishStatus status)
        {
            if (_context.DataRetriver.BasicGameDataRetriver.TryFindCPU(_context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id, out var cpu))
            {
                _cpuAnimationFacade.PlayClappingAnimation(cpu);
            }
        }

        private void OnUpdateProgress(grandartslogic.Unit _)
        {
            if (!_context.DataRetriver.BasicGameDataRetriver.IsCPUTurn) return;
            if (_context.DataRetriver.BasicGameDataRetriver.IsAllThrowFixed || _context.DataRetriver.BasicGameDataRetriver.IsReachGameEnd)
            {
                UniTask.Void(async () =>
                {
                    if (_context.DataRetriver.BasicGameDataRetriver.TryFindCPU(_context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id, out var cpu))
                    {
                        _cpuAnimationFacade.StopThrowAnimation(cpu, _context.DataRetriver.BasicGameDataRetriver.CurrentRound);
                    }
                    if(_cancellation == null || _cancellation.IsCancellationRequested)
                    {
                        _cancellation = new ();
                    }
                    CancellationTokenSource linked = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken, _cancellation.Token);
                    await UniTask.Delay(TimeSpan.FromSeconds(5), cancellationToken: linked.Token);
                    GameInputHub.Instance.NotifyChangeKeyReceived();
                });
            }
            else
            {
                if (_context.DataRetriver.BasicGameDataRetriver.TryFindCPU(_context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id, out var cpu))
                {
                    _cpuAnimationFacade.PlayNextThrowAnimation(cpu);
                    ThrowAction(cpu);
                }
            }
        }

        private void CPUFirstThrowAction()
        {
            if (_cpuThrowSceneMainCamera != null)
            {
                _cpuThrowSceneMainCamera.gameObject.SetActive(_context.DataRetriver.BasicGameDataRetriver.IsCPUTurn);
            }
            if (!_context.DataRetriver.BasicGameDataRetriver.IsCPUTurn) return;
            if (_context.DataRetriver.BasicGameDataRetriver.TryFindCPU(_context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id, out var cpu))
            {
                _cpuAnimationFacade.PlayChangeAnimation(cpu);
                _cpuAnimationFacade.PlayThrowAnimation(cpu);
                ThrowAction(cpu);
            }
        }

        private void ThrowAction(ICPU thrower)
        {
            var hit = thrower.DecideHit();
            UniTask.Void(async () =>
            {
                if(_cancellation == null || _cancellation.IsCancellationRequested)
                {
                    _cancellation = new ();
                }
                CancellationTokenSource linked = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken, _cancellation.Token);
                await UniTask.Delay(TimeSpan.FromSeconds(2), cancellationToken: linked.Token);
                GameInputHub.Instance.NotifySegmentKeyReceived(hit.Code);
            });
        }

        private void OnDestroy()
        {
            if (SceneManager.GetSceneByName("CPUThrow").isLoaded)
            {
                SceneManager.UnloadSceneAsync("CPUThrow");
            }
        }
    }
}
