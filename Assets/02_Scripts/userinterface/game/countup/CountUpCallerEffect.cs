using System;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.context.countup;
using com.luxza.grandarts.userinterfaces.online.game;

namespace com.luxza.grandarts.userinterfaces.game.countup
{
    public class CountUpCallerEffect : AbstractCallerEffect
    {
        private GameCountUpSessionContext _context;

        public override void OnContextCreated(IGameSessionContext context)
        {
            _context = context as GameCountUpSessionContext;
            if (_context == null)
            {
                throw new ArgumentException("CountUpCallerEffect only works with GameCountUpSessionContext");
            }
        }
    }
}