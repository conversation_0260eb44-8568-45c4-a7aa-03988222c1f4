using R3;
using System;
using System.Linq;
using System.Threading;
using System.Collections.Generic;
using UnityEngine;
using Luxza.Unit.MDNS;
using Cysharp.Threading.Tasks;
using com.luxza.granlog;
using com.luxza.graneye;
using com.luxza.ui.page;
using com.luxza.ui.components.molecules;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.userinterfaces.utils;


namespace com.luxza.grandarts.userinterfaces.components.game.play.local.menu
{
    public class GranEyeCheckMenu : MonoBehaviour
    {
        [SerializeField] private GranButton _btn_LeftCamera_SearchAgain;
        [SerializeField] private GranButton _btn_RightCamera_SearchAgain;
        [SerializeField] private GranButton _btn_BaseUnit_SearchAgain;
        [SerializeField] private GameObject _obj_LeftCamera_Connected;
        [SerializeField] private GameObject _obj_RightCamera_Connected;
        [SerializeField] private GameObject _obj_BaseUnit_Connected;
        [SerializeField] private GameObject _obj_LeftCamera_Fail;
        [SerializeField] private GameObject _obj_RightCamera_Fail;
        [SerializeField] private GameObject _obj_BaseUnit_Fail;
        private CancellationTokenSource _echoCts;
        private CancellationTokenSource _leftCameraCts;
        private CancellationTokenSource _rightCameraCts;
        private float _timeOut => 5f;

        private enum SearchType
        {
            None,
            LeftCamera,
            RightCamera,
            AllCameras
        }

        private SearchType _currentSearchType = SearchType.None;
        private bool _isInSearch = false;

        async void Awake()
        {
            MDnsServiceDiscovery.Instance.OnDiscoveryDone.AddListener(DiscoveryDone);
            GranEye.EchoCore.On_EchoConnectionChanged_Callback.AddListener(OnEchoConnectionChanged);

            _btn_LeftCamera_SearchAgain.onClickAsObservable.Subscribe(async _ => await OnLeftCameraSearchAgainClicked())
                .RegisterTo(destroyCancellationToken);
            _btn_RightCamera_SearchAgain.onClickAsObservable.Subscribe(async _ => await OnRightCameraSearchAgainClicked())
                .RegisterTo(destroyCancellationToken);
            _btn_BaseUnit_SearchAgain.onClickAsObservable.Subscribe(async _ => await OnEchoSearchAgainClicked())
                .RegisterTo(destroyCancellationToken);

            await OnConnectionStatusCheckClicked();
        }

        private async void OnEchoConnectionChanged(bool success)
        {
            _echoCts?.Cancel();
            await UniTask.Delay(200, cancellationToken: destroyCancellationToken);
            Log.d($"Echo connection status: {success}");
            _obj_BaseUnit_Fail.SetActive(!success);
            _obj_BaseUnit_Connected.SetActive(success);
        }

        private async void DiscoveryDone(List<MDnsDeviceEntity> list)
        {
            Log.d($"DiscoveryDone listCount: {list.Count}");
            foreach (var item in list)
            {
                Log.d($"DiscoveryDone IP: {item.IP}, Name: {item.Name}");
            }

            if (list.Count >= 0)
            {
                switch (_currentSearchType)
                {
                    case SearchType.LeftCamera:
                        {
                            var reconnectResult = ReconnectCamera(GranEyeCore.CameraPos.Left, list);
                            if (reconnectResult)
                            {
                                _leftCameraCts?.Cancel();
                                _leftCameraCts = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken);
                                var result = await GranEye.GranEyeCore.PingCamera(GranEyeCore.CameraPos.Left, _leftCameraCts.Token);
                                _obj_LeftCamera_Connected.SetActive(result);
                                _obj_LeftCamera_Fail.SetActive(!result);
                            }
                            else
                            {
                                _obj_LeftCamera_Connected.SetActive(false);
                                _obj_LeftCamera_Fail.SetActive(true);
                            }
                        }
                        break;
                    case SearchType.RightCamera:
                        {
                            var reconnectResult = ReconnectCamera(GranEyeCore.CameraPos.Right, list);
                            if (reconnectResult)
                            {
                                _rightCameraCts?.Cancel();
                                _rightCameraCts = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken);
                                var result = await GranEye.GranEyeCore.PingCamera(GranEyeCore.CameraPos.Right, _rightCameraCts.Token);
                                _obj_RightCamera_Connected.SetActive(result);
                                _obj_RightCamera_Fail.SetActive(!result);
                            }
                            else
                            {
                                _obj_RightCamera_Connected.SetActive(false);
                                _obj_RightCamera_Fail.SetActive(true);
                            }
                        }
                        break;
                    case SearchType.AllCameras:
                        {
                            var leftReconnectResult = ReconnectCamera(GranEyeCore.CameraPos.Left, list);
                            var rightReconnectResult = ReconnectCamera(GranEyeCore.CameraPos.Right, list);
                            if (leftReconnectResult)
                            {
                                _leftCameraCts?.Cancel();
                                _leftCameraCts = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken);
                                var result = await GranEye.GranEyeCore.PingCamera(GranEyeCore.CameraPos.Left, _leftCameraCts.Token);
                                _obj_LeftCamera_Connected.SetActive(result);
                                _obj_LeftCamera_Fail.SetActive(!result);
                            }
                            else
                            {
                                _obj_LeftCamera_Connected.SetActive(false);
                                _obj_LeftCamera_Fail.SetActive(true);
                            }
                            if (rightReconnectResult)
                            {
                                _rightCameraCts?.Cancel();
                                _rightCameraCts = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken);
                                var result = await GranEye.GranEyeCore.PingCamera(GranEyeCore.CameraPos.Right, _rightCameraCts.Token);
                                _obj_RightCamera_Connected.SetActive(result);
                                _obj_RightCamera_Fail.SetActive(!result);
                            }
                            else
                            {
                                _obj_RightCamera_Connected.SetActive(false);
                                _obj_RightCamera_Fail.SetActive(true);
                            }
                        }
                        break;
                }
            }
            _isInSearch = false;
        }

        private async UniTask OnLeftCameraSearchAgainClicked()
        {
            _obj_LeftCamera_Fail.SetActive(false);
            switch (_currentSearchType)
            {
                case SearchType.RightCamera:
                    _currentSearchType = SearchType.AllCameras;
                    break;
                case SearchType.AllCameras:
                    break;
                default:
                    _currentSearchType = SearchType.LeftCamera;
                    break;
            }
#if !UNITY_EDITOR
        if(!_isInSearch)
        {
            _isInSearch = true;
            MDnsServiceDiscovery.Instance.StartDiscovery();
        }
#else
            _isInSearch = true;
            await UniTask.Delay(TimeSpan.FromSeconds(3.0f), cancellationToken: destroyCancellationToken);
            List<MDnsDeviceEntity> list = new List<MDnsDeviceEntity>();
            var camera1 = new MDnsDeviceEntity();
            camera1.IP = "***************";
            camera1.Name = "GranEye-78:22:88:02:68:94";
            list.Add(camera1);
            var camera2 = new MDnsDeviceEntity();
            camera2.IP = "***************";
            camera2.Name = "GranEye-78:22:88:02:68:B2";
            list.Add(camera2);
            DiscoveryDone(list);
#endif
        }

        private async UniTask OnRightCameraSearchAgainClicked()
        {
            _obj_RightCamera_Fail.SetActive(false);
            switch (_currentSearchType)
            {
                case SearchType.LeftCamera:
                    _currentSearchType = SearchType.AllCameras;
                    break;
                case SearchType.AllCameras:
                    break;
                default:
                    _currentSearchType = SearchType.RightCamera;
                    break;
            }
#if !UNITY_EDITOR
            if(!_isInSearch)
            {
                _isInSearch = true;
                MDnsServiceDiscovery.Instance.StartDiscovery();
            }
#else
            _isInSearch = true;
            await UniTask.Delay(TimeSpan.FromSeconds(3.0f), cancellationToken: destroyCancellationToken);
            List<MDnsDeviceEntity> list = new List<MDnsDeviceEntity>();
            var camera1 = new MDnsDeviceEntity();
            camera1.IP = "***************";
            camera1.Name = "GranEye-78:22:88:02:68:94";
            list.Add(camera1);
            var camera2 = new MDnsDeviceEntity();
            camera2.IP = "***************";
            camera2.Name = "GranEye-78:22:88:02:68:B2";
            list.Add(camera2);
            DiscoveryDone(list);
#endif
        }

        private async UniTask OnEchoSearchAgainClicked()
        {
            _obj_BaseUnit_Fail.SetActive(false);
            BluetoothCheckUtility.CheckBluetoothAndSearchConnect();
            EchoConnectTimeout();
        }

        private async void EchoConnectTimeout()
        {
            _echoCts?.Cancel();
            _echoCts = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken);
            try
            {
                var timeoutTask = UniTask.Delay(TimeSpan.FromSeconds(_timeOut), cancellationToken: _echoCts.Token);
                var conditionTask = UniTask.WaitUntil(() => GranEye.IsEchoConnected, cancellationToken: _echoCts.Token);
                int completedIndex = await UniTask.WhenAny(timeoutTask, conditionTask);
                Log.d($"completedIndex = {completedIndex}", color: Color.green);
                if (completedIndex == 0)
                {
                    if (this != null && _obj_BaseUnit_Fail != null)
                    {
                        _obj_BaseUnit_Fail.SetActive(true);
                    }
                }
                else
                {
                    _obj_BaseUnit_Connected.SetActive(true);
                }
            }
            catch (OperationCanceledException)
            {
                Log.d("OperationCanceledException");
            }
        }

        private async UniTask OnConnectionStatusCheckClicked()
        {
            _leftCameraCts?.Cancel();
            _rightCameraCts?.Cancel();
            _echoCts?.Cancel();

            _obj_LeftCamera_Fail.SetActive(false);
            _obj_RightCamera_Fail.SetActive(false);
            _obj_BaseUnit_Fail.SetActive(false);
            _obj_LeftCamera_Connected.SetActive(false);
            _obj_RightCamera_Connected.SetActive(false);
            _obj_BaseUnit_Connected.SetActive(false);

            var results = await GranEye.GranEyeCore.PingCameras(destroyCancellationToken);
            if (results.leftCameraPingResult)
            {
                _obj_LeftCamera_Connected.SetActive(true);
            }
            else
            {
                _obj_LeftCamera_Fail.SetActive(true);
            }
            if (results.rightCameraPingResult)
            {
                _obj_RightCamera_Connected.SetActive(true);
            }
            else
            {
                _obj_RightCamera_Fail.SetActive(true);
            }
            if (GranEye.IsEchoConnected)
            {
                _obj_BaseUnit_Connected.SetActive(true);
            }
            else
            {
                BluetoothCheckUtility.CheckBluetoothAndSearchConnect();
                EchoConnectTimeout();
            }
        }

        private async UniTask OnGranEyeSettingClicked()
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeSettingTop));
        }

        private bool ReconnectCamera(GranEyeCore.CameraPos cameraPos, List<MDnsDeviceEntity> list)
        {
            var tempMac = GranEye.GranEyeCore.Get_IpCameraMacAddress(cameraPos);
            var targetEntity = list.Where(entity =>
                entity.Name.Split('-').Length > 1 && tempMac.Equals(entity.Name.Split('-')[1])).ToList();
            if (targetEntity.Count > 0)
            {
                var targetIp = targetEntity[0].IP;
                GranEye.GranEyeCore.Set_IpCameraAddress(cameraPos, targetIp);
                GranEye.GranEyeCore.GetCamera(cameraPos).Connect(targetIp);
                return true;
            }
            return false;
        }
        
        private void OnDestroy()
        {
            MDnsServiceDiscovery.Instance.OnDiscoveryDone.RemoveListener(DiscoveryDone);
            GranEye.EchoCore.On_EchoConnectionChanged_Callback.RemoveListener(OnEchoConnectionChanged);
            _echoCts?.Cancel();
            _echoCts?.Dispose();
            _leftCameraCts?.Cancel();
            _leftCameraCts?.Dispose();
            _rightCameraCts?.Cancel();
            _rightCameraCts?.Dispose();
        }
    }
}
