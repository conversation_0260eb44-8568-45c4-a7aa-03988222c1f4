using R3;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.userinterfaces.components.game.input.shanghai
{
    public class ShangHaiSegmentInputKeyboard : SegmentInputKeyboard_ver3
    {
        protected override void Init()
        {
            DeActivate();
            for (int i = 0; i < _segmentButtons.Length; i++)
            {
                _segmentButtons[i].OnClick.Subscribe(OnClickSegmentButton).RegisterTo(destroyCancellationToken);
            }
            foreach (var item in _throwResults)
            {
                item.onClick.Subscribe(OnClickThrowResultButton).RegisterTo(destroyCancellationToken);
            }
            ClearInput();
            _submitButton.onClickAsObservable.Subscribe(OnClickSubmit).RegisterTo(destroyCancellationToken);
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context;
                OnContextCreated(_context);
                DeActivate();
                _context.GameEventPublisher.OnStartMatch += () => ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                _context.GameEventPublisher.OnChange += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnRoundReverse += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnUpdateProgress += (_) => UpdateThrows();
                _context.GameEventPublisher.OnThrowReverse += (_) => UpdateThrows();
                _context.GameEventPublisher.OnOverrideThrow += (_, _) =>
                {
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnRecovery += () =>
                {
                    ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnGameConfirmed += () =>
                {
                    _submitButton.interactable = false;
                };
            }).RegisterTo(destroyCancellationToken);
            if (_positionTab != null)
            {
                _positionTab.onSelectedIndexChanged += OnTabIndexChanged;
                OnTabIndexChanged(0);
            }
        }

        protected override void UpdateThrows()
        {
            if (_context.DataRetriver.BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                for (var i = 0; i < segmentInput.Throws.Length; i++)
                {
                    var th = segmentInput.Throws[i];
                    if (th.IsFixed)
                    {
                        _segmentKeys[i] = th.ActuaryHitArea.Code;
                        _throwResults[i].SetSegmentKey(th.ActuaryHitArea.Code, th.ActuaryHitArea.BasicScore);
                    }
                    else
                    {
                        _segmentKeys[i] = null;
                        _throwResults[i].ClearSegmentKey();
                    }
                }

                if (_positionTab != null)
                {
                    _positionTab.SetIndex(0);
                    OnTabIndexChanged(0);
                }
            }
        }

        private void UpdateSegmentKey()
        {
            
        }

        protected virtual void ChangeThrower(grandartslogic.Unit sender)
        {
            ClearInput();
            UpdateSegmentKey();
            _submitButton.interactable = true;

            SetThrowResultButtonInteractable(_context.IsMyDeviceTurn);

            if (!_context.IsMyDeviceTurn)
            {
                DeActivate();
                return;
            }

            Activate();
            _throwResultObject.SetActive(true);

            void SetThrowResultButtonInteractable(bool val)
            {
                foreach (var item in _throwResults)
                {
                    item.SetButtonInteractable(val);
                }
            }
        }

        public override void Activate()
        {
            UIRoot.SetActive(true);
        }

        public override void DeActivate()
        {
            UIRoot.SetActive(false);
        }

    }
}