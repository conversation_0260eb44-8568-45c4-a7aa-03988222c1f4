using R3;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.userinterfaces.components.game.input.shanghai
{
    public class ShangHaiSegmentInputKeyboard : SegmentInputKeyboard_ver3
    {
        protected override void Init()
        {
            DeActivate();
            for (int i = 0; i < _segmentButtons.Length; i++)
            {
                _segmentButtons[i].OnClick.Subscribe(OnClickSegmentButton).RegisterTo(destroyCancellationToken);
            }
            foreach (var item in _throwResults)
            {
                item.onClick.Subscribe(OnClickThrowResultButton).RegisterTo(destroyCancellationToken);
            }
            ClearInput();
            _submitButton.onClickAsObservable.Subscribe(OnClickSubmit).RegisterTo(destroyCancellationToken);
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context;
                OnContextCreated(_context);
                DeActivate();
                _context.GameEventPublisher.OnStartMatch += () => ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                _context.GameEventPublisher.OnChange += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnRoundReverse += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnUpdateProgress += (_) => UpdateThrows();
                _context.GameEventPublisher.OnThrowReverse += (_) => UpdateThrows();
                _context.GameEventPublisher.OnOverrideThrow += (_, _) =>
                {
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnRecovery += () =>
                {
                    ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnGameConfirmed += () =>
                {
                    _submitButton.interactable = false;
                };
            }).RegisterTo(destroyCancellationToken);
            if (_positionTab != null)
            {
                _positionTab.onSelectedIndexChanged += OnTabIndexChanged;
                OnTabIndexChanged(0);
            }
        }

        protected override void UpdateThrows()
        {
            if (_context.DataRetriver.BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                for (var i = 0; i < segmentInput.Throws.Length; i++)
                {
                    var th = segmentInput.Throws[i];
                    if (th.IsFixed)
                    {
                        _segmentKeys[i] = th.ActuaryHitArea.Code;
                        _throwResults[i].SetSegmentKey(th.ActuaryHitArea.Code, th.ActuaryHitArea.BasicScore);
                    }
                    else
                    {
                        _segmentKeys[i] = null;
                        _throwResults[i].ClearSegmentKey();
                    }
                }

                if (_positionTab != null)
                {
                    _positionTab.SetIndex(0);
                    OnTabIndexChanged(0);
                }
            }
        }

        private void UpdateSegmentKey()
        {
            var currentRoundNo = _context.DataRetriver.BasicGameDataRetriver.CurrentRound.No;

            // Shanghai游戏中，每轮只能命中对应轮次的数字
            // 例如：第1轮只能命中1，第2轮只能命中2，以此类推
            if (currentRoundNo >= 1 && currentRoundNo <= 20)
            {
                // 计算当前轮次对应的SegmentCode
                // SegmentCode枚举模式：Change(0), S1_In(1), S1_Out(2), D1(3), T1(4), S2_In(5), S2_Out(6), D2(7), T2(8), ...
                var singleInCode = (SegmentCode)((currentRoundNo - 1) * 4 + 1); // S1_In = 1, S2_In = 5, S3_In = 9, ...
                var doubleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 3);   // D1 = 3, D2 = 7, D3 = 11, ...
                var tripleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 4);   // T1 = 4, T2 = 8, T3 = 12, ...

                // 设置前三个按钮为当前轮次的Single In, Double, Triple
                if (_segmentButtons.Length >= 3)
                {
                    _segmentButtons[0].SetSegmentCode(singleInCode);
                    _segmentButtons[1].SetSegmentCode(doubleCode);
                    _segmentButtons[2].SetSegmentCode(tripleCode);
                }

                // 其余按钮设置为Miss或OUT（根据需要）
                for (int i = 3; i < _segmentButtons.Length; i++)
                {
                    if (i == _segmentButtons.Length - 1)
                    {
                        _segmentButtons[i].SetSegmentCode(SegmentCode.OUT);
                    }
                    else
                    {
                        _segmentButtons[i].SetSegmentCode(SegmentCode.Miss);
                    }
                }
            }
        }

        protected virtual void ChangeThrower(grandartslogic.Unit sender)
        {
            ClearInput();
            UpdateSegmentKey();
            _submitButton.interactable = true;

            SetThrowResultButtonInteractable(_context.IsMyDeviceTurn);

            if (!_context.IsMyDeviceTurn)
            {
                DeActivate();
                return;
            }

            Activate();
            _throwResultObject.SetActive(true);

            void SetThrowResultButtonInteractable(bool val)
            {
                foreach (var item in _throwResults)
                {
                    item.SetButtonInteractable(val);
                }
            }
        }

        public override void Activate()
        {
            UIRoot.SetActive(true);
        }

        public override void DeActivate()
        {
            UIRoot.SetActive(false);
        }

    }
}