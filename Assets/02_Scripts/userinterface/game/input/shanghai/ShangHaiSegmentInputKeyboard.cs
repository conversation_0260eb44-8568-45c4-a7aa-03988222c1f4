using R3;
using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.ui.components.molecules;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.input.shanghai
{
    public class ShangHaiSegmentInputKeyboard : SegmentInputKeyboard_ver3
    {
        [SerializeField] protected GranButton _noScoreButton;
        [SerializeField] protected GranButton _shangHaiButton;

        protected override void Init()
        {
            // base.Init();
            _shangHaiButton.onClickAsObservable.Subscribe(_ => OnClickShangHai()).RegisterTo(destroyCancellationToken);
            _noScoreButton.onClickAsObservable.Subscribe(_ => OnClickNoScore()).RegisterTo(destroyCancellationToken);
            DeActivate();
            for (int i = 0; i < _segmentButtons.Length; i++)
            {
                _segmentButtons[i].OnClick.Subscribe(OnClickSegmentButton).RegisterTo(destroyCancellationToken);
            }
            foreach (var item in _throwResults)
            {
                item.onClick.Subscribe(OnClickThrowResultButton).RegisterTo(destroyCancellationToken);
            }
            ClearInput();
            _submitButton.onClickAsObservable.Subscribe(OnClickSubmit).RegisterTo(destroyCancellationToken);
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context;
                OnContextCreated(_context);
                DeActivate();
                _context.GameEventPublisher.OnStartMatch += () => ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                _context.GameEventPublisher.OnChange += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnRoundReverse += (sender) => ChangeThrower(sender);
                _context.GameEventPublisher.OnUpdateProgress += (_) => UpdateThrows();
                _context.GameEventPublisher.OnThrowReverse += (_) => UpdateThrows();
                _context.GameEventPublisher.OnOverrideThrow += (_, _) =>
                {
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnRecovery += () =>
                {
                    ChangeThrower(_context.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnitAsGranDartsLogicModel);
                    UpdateThrows();
                };
                _context.GameEventPublisher.OnGameConfirmed += () =>
                {
                    _submitButton.interactable = false;
                };
            }).RegisterTo(destroyCancellationToken);
            if (_positionTab != null)
            {
                _positionTab.onSelectedIndexChanged += OnTabIndexChanged;
                OnTabIndexChanged(0);
            }
        }

        protected override void UpdateThrows()
        {
            if (_context.DataRetriver.BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                for (var i = 0; i < segmentInput.Throws.Length; i++)
                {
                    var th = segmentInput.Throws[i];
                    if (th.IsFixed)
                    {
                        _segmentKeys[i] = th.ActuaryHitArea.Code;
                        _throwResults[i].SetSegmentKey(th.ActuaryHitArea.Code, th.ActuaryHitArea.BasicScore);
                    }
                    else
                    {
                        _segmentKeys[i] = null;
                        _throwResults[i].ClearSegmentKey();
                    }
                }

                if (_positionTab != null)
                {
                    _positionTab.SetIndex(0);
                    OnTabIndexChanged(0);
                }
            }
        }

        private void UpdateSegmentKey()
        {
            var currentRoundNo = _context.DataRetriver.BasicGameDataRetriver.CurrentRound.No;

            // Shanghai游戏中，每轮只能命中对应轮次的数字
            // 例如：第1轮只能命中1，第2轮只能命中2，以此类推
            if (currentRoundNo >= 1 && currentRoundNo <= 20)
            {
                // 计算当前轮次对应的SegmentCode
                // SegmentCode枚举模式：Change(0), S1_In(1), S1_Out(2), D1(3), T1(4), S2_In(5), S2_Out(6), D2(7), T2(8), ...
                var singleInCode = (SegmentCode)((currentRoundNo - 1) * 4 + 1); // S1_In = 1, S2_In = 5, S3_In = 9, ...
                var doubleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 3);   // D1 = 3, D2 = 7, D3 = 11, ...
                var tripleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 4);   // T1 = 4, T2 = 8, T3 = 12, ...

                // 设置前三个按钮为当前轮次的Single In, Double, Triple
                if (_segmentButtons.Length >= 3)
                {
                    _segmentButtons[0].SetSegmentCode(singleInCode);
                    _segmentButtons[1].SetSegmentCode(doubleCode);
                    _segmentButtons[2].SetSegmentCode(tripleCode);
                }

                // 其余按钮设置为Miss或OUT（根据需要）
                for (int i = 3; i < _segmentButtons.Length; i++)
                {
                    if (i == _segmentButtons.Length - 1)
                    {
                        _segmentButtons[i].SetSegmentCode(SegmentCode.OUT);
                    }
                    else
                    {
                        _segmentButtons[i].SetSegmentCode(SegmentCode.Miss);
                    }
                }
            }
        }

        private void OnClickNoScore()
        {
            for (int i = 0; i < _segmentKeys.Length; i++)
            {
                if (_segmentKeys[i] == null)
                {
                    OnClickSegmentButton(SegmentCode.Miss);
                }
            }
        }

        private void OnClickShangHai()
        {
            if (_context.DataRetriver.BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(
                    out var segmentInput))
            {
                var currentRoundNo = _context.DataRetriver.BasicGameDataRetriver.CurrentRound.No;

                // 检查当前轮次是否在有效范围内
                if (currentRoundNo < 1 || currentRoundNo > 20)
                {
                    return;
                }

                // 计算当前轮次对应的SegmentCode
                var expectedSingleInCode = (SegmentCode)((currentRoundNo - 1) * 4 + 1); // S1_In, S2_In, ...
                var expectedDoubleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 3);   // D1, D2, ...
                var expectedTripleCode = (SegmentCode)((currentRoundNo - 1) * 4 + 4);   // T1, T2, ...

                // 检查已投掷的镖中是否包含当前轮次的任何一个目标，并计数
                int singleCount = 0;
                int doubleCount = 0;
                int tripleCount = 0;

                foreach (var thr in segmentInput.Throws)
                {
                    if (thr.IsFixed && thr.ActuaryHitArea != null)
                    {
                        var hitCode = thr.ActuaryHitArea.Code;
                        if (hitCode == expectedSingleInCode)
                        {
                            singleCount++;
                        }
                        else if (hitCode == expectedDoubleCode)
                        {
                            doubleCount++;
                        }
                        else if (hitCode == expectedTripleCode)
                        {
                            tripleCount++;
                        }
                    }
                }

                // 如果有重复的相同目标（2个或以上），直接返回
                if (singleCount > 1 || doubleCount > 1 || tripleCount > 1)
                {
                    return;
                }

                // // 如果没有命中任何一个目标，直接返回
                // if (singleCount == 0 && doubleCount == 0 && tripleCount == 0)
                // {
                //     return;
                // }

                // 创建缺少的目标列表
                var missingTargets = new List<SegmentCode>();

                if (singleCount == 0)
                {
                    missingTargets.Add(expectedSingleInCode);
                }
                if (doubleCount == 0)
                {
                    missingTargets.Add(expectedDoubleCode);
                }
                if (tripleCount == 0)
                {
                    missingTargets.Add(expectedTripleCode);
                }

                // TODO: 在这里处理缺少的目标列表
                // 例如：显示提示信息、更新UI等
                Debug.Log($"Round {currentRoundNo}: Missing targets: {string.Join(", ", missingTargets)}");

                for (int i = 0; i < missingTargets.Count; i++)
                {
                    OnClickSegmentButton(missingTargets[i]);
                }
            }
        }

        protected virtual void ChangeThrower(grandartslogic.Unit sender)
        {
            ClearInput();
            UpdateSegmentKey();
            _submitButton.interactable = true;

            SetThrowResultButtonInteractable(_context.IsMyDeviceTurn);

            if (!_context.IsMyDeviceTurn)
            {
                DeActivate();
                return;
            }

            Activate();
            _throwResultObject.SetActive(true);

            void SetThrowResultButtonInteractable(bool val)
            {
                foreach (var item in _throwResults)
                {
                    item.SetButtonInteractable(val);
                }
            }
        }

        public override void Activate()
        {
            UIRoot.SetActive(true);
        }

        public override void DeActivate()
        {
            UIRoot.SetActive(false);
        }
    }
}