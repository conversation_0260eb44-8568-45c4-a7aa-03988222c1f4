using System;
using com.luxza.grandarts.domains.game.input;
using UnityEngine;
using R3;
using com.luxza.ui.components.molecules;
using com.luxza.granlog;
using com.luxza.grandarts.domains.game.context;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.userinterfaces.components.game.input
{
    public class ShortcutVisitInputKeyboard_ver3 : MonoBehaviour, IGameContextHolder
    {
        [SerializeField] private GameObject UIRoot;
        [SerializeField] private NormalVisitInputKeyboard_ver3 _normalVisitInputKeyboard;
        [SerializeField] private BasicGameContextAdoptor _adoptor;

        [SerializeField] private VisitInputShortcutButton[] _shortcuts;

        [SerializeField] private GranButton _switchToNormalButton;
        [SerializeField] private GranButton _submitButton;
        [SerializeField] private GranInputField _scoreInputField;
        [SerializeField] private GranButton _scoreResetButton;

        protected static readonly TimeSpan DELAY_TIME_FOR_END_GAME = TimeSpan.FromSeconds(3);


        private const string zero = "0";

        protected int _score;

        private IGameSessionContext _context;

        void Awake()
        {
            foreach (var shortcut in _shortcuts)
            {
                shortcut.onClick.Subscribe(OnClickShortcut).RegisterTo(destroyCancellationToken);
            }
            _submitButton.onClickAsObservable.Subscribe(OnClickSubmit).RegisterTo(destroyCancellationToken);
            _switchToNormalButton.onClickAsObservable.Subscribe(OnClickSwitchToNormalButton).RegisterTo(destroyCancellationToken);
            _scoreResetButton.onClickAsObservable.Subscribe(OnClickScoreResetButton).RegisterTo(destroyCancellationToken);
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context;
                OnContextCreated(context);
                DeActivate();
                context.GameEventPublisher.OnStartMatch += DeActivate;
                context.GameEventPublisher.OnChange += (_) =>
                {
                    ClearInput();
                    DeActivate();
                };
                context.GameEventPublisher.OnFinishMatch += (_) =>
                {
                    _submitButton.interactable = false;
                };
            }).RegisterTo(destroyCancellationToken);
        }

        private void OnClickShortcut(int score)
        {
            Log.d($"OnClickShortcut: {score}");
            _scoreInputField.text = score.ToString();
            _score = score;
        }

        protected virtual void OnClickSubmit(R3.Unit _)
        {
            PostClickSubmit();
        }

        protected virtual void PostClickSubmit()
        {
            SendScoreAndChange(0, 0);
        }

        protected void SendScoreAndChange(int checkout, int checkoutTry)
        {
            InvokeInputScore(_score, checkout, checkoutTry);
            UniTask.Void(async () =>
            {
                if (_context.DataRetriver.BasicGameDataRetriver.IsReachGameEnd)
                {
                    await UniTask.Delay(DELAY_TIME_FOR_END_GAME);
                }

                InvokeChange();
                ClearInput();
            });
        }

        private void OnClickScoreResetButton(R3.Unit _)
        {
            Log.d($"[SHORTCUT] OnClickScoreResetButton called, GameObject: {_scoreResetButton.name}, current score: {_score}");

            // ショートカット入力がアクティブでない場合は何もしない
            if (!UIRoot.activeSelf)
            {
                Log.d($"[SHORTCUT] UIRoot is not active, ignoring click");
                return;
            }
            ClearInput();
        }

        protected void ClearInput()
        {
            _score = 0;
            _scoreInputField.text = zero;
        }

        private void OnClickSwitchToNormalButton(R3.Unit _)
        {
            // 現在のスコアを通常入力側に引き継ぎ
            _normalVisitInputKeyboard.SetScore(_score);
            _normalVisitInputKeyboard.ActivateNomalInput();
            DeActivate();
        }

        public void Activate()
        {
            UIRoot.SetActive(true);
        }

        public void SetScore(int score)
        {
            // バリデーションチェック：無効なスコアの場合は0にリセット
            if (!VisitInputValidator.Validate(score))
            {
                Log.w($"Invalid score {score} was attempted to be set. Resetting to 0.");
                _score = 0;
                _scoreInputField.text = "0";
            }
            else
            {
                _score = score;
                _scoreInputField.text = score.ToString();
            }
        }

        public void DeActivate()
        {
            UIRoot.SetActive(false);
        }
        protected void InvokeInputScore(int score, int checkout, int checkoutTry)
        {
            GameInputHub.Instance.NotifyScoreReceived(score, checkout, checkoutTry);
        }

        protected void InvokeChange()
        {
            GameInputHub.Instance.NotifyChangeKeyReceived();
        }

        public virtual void OnContextCreated(IGameSessionContext context)
        {
        }
    }
}