using com.luxza.ui.components.molecules;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.input.multiplecr
{
    public class MultipleCRSegmentInputKeyboardWithNoScore : MultipleCRSegmentInputKeyboard
    {
        [SerializeField] protected GranButton _noScoreButton;

        protected override void Init()
        {
            base.Init();
            _noScoreButton.onClickAsObservable.Subscribe(_ => OnClickNoScore()).RegisterTo(destroyCancellationToken);
        }

        public void OnClickNoScore()
        {
            for (int i = 0; i < _segmentKeys.Length; i++)
            {
                if (_segmentKeys[i] == null)
                {
                    OnClickSegmentButton(grandartslogic.domain.game.SegmentCode.Miss);
                }
            }
        }
        
        public override void Activate()
        {
            UIRoot.SetActive(true);
        }

        public override void DeActivate()
        {
            UIRoot.SetActive(false);
        }
    }
}