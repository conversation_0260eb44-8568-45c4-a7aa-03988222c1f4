using UnityEngine;
using com.luxza.ui.components.atoms;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.domains.game.unit;

namespace com.luxza.grandarts.userinterfaces.components.result
{
    public class GameResultUnitIcon : MonoBehaviour
    {
        /// <summary>
        /// List of player avatars in Unit
        /// </summary>
        [SerializeField] private PlayerIcon _playerIconPrefab;

        /// <summary>
        /// Nickname of the player currently playing darts
        /// </summary>
        [SerializeField] private GranText _TextPlayerName;

        [SerializeField] private RectTransform _iconsOwner;
        [SerializeField] private GameObject _Win;
        PlayUnit _playerUnit;
        public void Bind(PlayUnit playerUnit,bool isWin)
        {
            if(_Win != null) _Win.SetActive(isWin);
            _playerUnit = playerUnit;
            foreach (Transform item in _iconsOwner.transform) Destroy(item.gameObject);


            for(var i = 0; i < _playerUnit.Members.Length; i++)
            {
                var icon = Instantiate(_playerIconPrefab, _iconsOwner);
                icon.Bind(_playerUnit.Members[i]);
                icon.gameObject.SetActive(true);
            }

            _TextPlayerName.text = _playerUnit.Members[0].Name;
            _TextPlayerName.gameObject.SetActive(true);
        }
    }
}