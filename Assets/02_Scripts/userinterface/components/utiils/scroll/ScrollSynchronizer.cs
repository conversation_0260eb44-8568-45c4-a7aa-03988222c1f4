using UnityEngine;
using UnityEngine.UI;

namespace com.luxza.grandarts.userinterfaces.components.utils.scroll {
    public class ScrollSyncronizer : MonoBehaviour
    {
        [SerializeField] private ScrollRect _scrollRectA;
        [SerializeField] private ScrollRect _scrollRectB;

        [SerializeField] private bool _syncVertical;
        [SerializeField] private bool _syncHorizontal;

        private void Awake()
        {
            _scrollRectA.onValueChanged.AddListener(OnScrollRectAValueChanged);
            //_scrollRectB.onValueChanged.AddListener(OnScrollRectBValueChanged);
        }

        private void OnScrollRectAValueChanged(Vector2 value)
        {
            _scrollRectB.normalizedPosition = new Vector2(
                _syncHorizontal ? value.x : _scrollRectB.normalizedPosition.x,
                _syncVertical ? value.y : _scrollRectB.normalizedPosition.y);
        }

        private void OnScrollRectBValueChanged(Vector2 value) {
            _scrollRectA.normalizedPosition = new Vector2(
                _syncHorizontal ? value.x : _scrollRectA.normalizedPosition.x,
                _syncVertical ? value.y : _scrollRectA.normalizedPosition.y);
        }
    }
}