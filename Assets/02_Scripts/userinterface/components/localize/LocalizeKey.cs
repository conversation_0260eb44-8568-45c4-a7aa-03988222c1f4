namespace com.luxza.grandarts.userinterfaces.components.localize
{
    public class LocalizeKey
    {
        public string Key;
        public LocalizeCategory Category;
        public enum LocalizeCategory
        {
            notifications,
            prompts,
            systems,
            tips,
            labels
        }

        public LocalizeKey(LocalizeCategory category, string key)
        {
            Category = category;
            Key = key;
        }

        public string GetLocalize(LocalizeCategory category, string key)
        {
            return LocalizeString.GetLocalizedString(new LocalizeKey(category, key));
        }

        public static class Notifications
        {
            public static LocalizeKey GranEyeFound = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.found");
            public static LocalizeKey GranEyeNotFound = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.notFound");
            public static LocalizeKey GranEyeSettingCompleted = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.settingCompleted");
            public static LocalizeKey CouldNotGetCalibrationPoints = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.Couldnotgetcalibrationpoints");
            public static LocalizeKey YourVerifiCationCodeIsExpired = new LocalizeKey(LocalizeCategory.notifications, "notification.signin.Yourverificationcodeisexpired");
            public static LocalizeKey ImagePermissionDenied = new LocalizeKey(LocalizeCategory.notifications, "ImagePermissionDenied");
            public static LocalizeKey PasswordCorrectNumber = new LocalizeKey(LocalizeCategory.notifications, "PasswordCorrectNumber");
            public static LocalizeKey UserNotFound = new LocalizeKey(LocalizeCategory.notifications, "UserNotFound");
            public static LocalizeKey LoginFailed = new LocalizeKey(LocalizeCategory.notifications, "LoginFailed");
            public static LocalizeKey AccountHasBeenBanned = new LocalizeKey(LocalizeCategory.notifications, "AccountHasBeenBanned");
            public static LocalizeKey VerificationCodeisExpired = new LocalizeKey(LocalizeCategory.notifications, "VerificationCodeisExpired");
            public static LocalizeKey RequestHasBeenDeclined = new LocalizeKey(LocalizeCategory.notifications, "RequestHasBeenDeclined");
            public static LocalizeKey AllowAccessCameraroll = new LocalizeKey(LocalizeCategory.notifications, "notification.selecticon.allowAccessCameraroll");
            public static LocalizeKey CameraFactoryRest = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.CameraFactoryRest");
            public static LocalizeKey SettingInitializationCompleted = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.SettingInitializationCompleted");
            public static LocalizeKey EchoOTAFinished = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.echoOTAFinished");
            public static LocalizeKey DeviceNotFound = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.devicenotfound");
            public static LocalizeKey SignOut = new LocalizeKey(LocalizeCategory.notifications, "notification.menu.signout");
            public static LocalizeKey CameraPermissionRequired = new LocalizeKey(LocalizeCategory.notifications, "notification.cameraPermissionRequired");
            public static LocalizeKey MicPermissionRequired = new LocalizeKey(LocalizeCategory.notifications, "notification.micPermissionRequired");
            public static LocalizeKey EchoOTAUpdateFinished = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.echoOTAUpdateFinished");
            public static LocalizeKey CameraRestartCheck = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.restartCheck");
            public static LocalizeKey NoSubmitScore = new LocalizeKey(LocalizeCategory.notifications, "notification.visitinput.noSubmitScore");
            public static LocalizeKey SaveSuccess = new LocalizeKey(LocalizeCategory.notifications, "notification.setting.saveSuccess");
            public static LocalizeKey PermissionDenied = new LocalizeKey(LocalizeCategory.notifications, "PermissionDenied");
            public static LocalizeKey ReStartGame = new LocalizeKey(LocalizeCategory.notifications, "notification.game.restartGame");
            public static LocalizeKey ExitGame = new LocalizeKey(LocalizeCategory.notifications, "notification.game.exitGame");
            public static LocalizeKey EchoIsNotConnected = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.echoIsNotConnected");
            public static LocalizeKey RegisterGuestSuccessfully = new LocalizeKey(LocalizeCategory.notifications, "notification.qrentry.registerGuestSuccessfully");
            public static LocalizeKey RemoveGuestSuccess = new LocalizeKey(LocalizeCategory.notifications, "notification.guest.removeguestSuccess");
            public static LocalizeKey ConfirmSurrenderExitMessage = new LocalizeKey(LocalizeCategory.notifications, "notification.online.ConfirmSurrenderExitMessage");
            public static LocalizeKey ConfirmExitMessage = new LocalizeKey(LocalizeCategory.notifications, "notification.online.ConfirmExitMessage");
            public static LocalizeKey ReachMaxGuests = new LocalizeKey(LocalizeCategory.notifications, "notification.guest.ReachMaxGuests");
            public static LocalizeKey GuestExistedAlready = new LocalizeKey(LocalizeCategory.notifications, "notification.guest.existedAlready");
            public static LocalizeKey SuccessFirmwareUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.SuccessFirmwareUpdate");

            public static LocalizeKey PleaseCalibrationfirst = new LocalizeKey(LocalizeCategory.notifications, "notification.gameplaysettting.graneye.plzCalibrationfirst");
            public static LocalizeKey RemoveGuest = new LocalizeKey(LocalizeCategory.notifications, "notification.guest.removeGuest");
            public static LocalizeKey RemoveInstantPlayer = new LocalizeKey(LocalizeCategory.notifications, "notification.guest.removeGuest");
            public static LocalizeKey LeftCamImageAcquisitionError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.LeftCamImageAcquisitionError");
            public static LocalizeKey RightCamImageAcquisitionError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.RightCamImageAcquisitionError");
            public static LocalizeKey AllCamImageAcquisitionError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.AllCamImageAcquisitionError");

            public static LocalizeKey BaseUnitNotDetectedError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.baseUnitNotDetected");
            public static LocalizeKey BothCamerasNotDetectedError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.bothCamerasNotDetected");
            public static LocalizeKey LeftCameraNotDetectedError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.leftCameraNotDetected");
            public static LocalizeKey RightCameraNotDetectedError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.rightCameraNotDetected");
            public static LocalizeKey AllUnitsNotDetectedError = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.allUnitsNotDetected");
            public static LocalizeKey NoPlayerSelected = new LocalizeKey(LocalizeCategory.notifications, "notification.game.noPlayerSelected");
            public static LocalizeKey AppUpdateNotification = new LocalizeKey(LocalizeCategory.notifications, "notification.appversion.AppUpdateNotification");
            public static LocalizeKey CurrentVersionNotAvailableOnline = new LocalizeKey(LocalizeCategory.notifications, "notification.appversion.CurrentVersionNotAvailableOnline");
            public static LocalizeKey GranEyeUdpSearch = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.udpSearch");
            public static LocalizeKey GranCameraForcedUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.forcedUpdate");
            public static LocalizeKey GranEchoForcedUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.echoForcedUpdate");
            public static LocalizeKey GranEyePlayGameForcedUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.playGameForcedUpdate");
            public static LocalizeKey GranCameraLatestUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.cameraLatestUpdate");
            public static LocalizeKey GranEchoLatestUpdate = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.echoLatestUpdate");
            public static LocalizeKey GranEyeGetCameraImageFailed = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.getCameraImageFailed");
            public static LocalizeKey GranEyeUSBPermissionPrompt = new LocalizeKey(LocalizeCategory.notifications, "notification.graneye.USBPermissionPrompt");
            public static LocalizeKey DeletePlayerLast = new LocalizeKey(LocalizeCategory.notifications, "notification.deletePlayerLast");
            public static LocalizeKey DeletePlayerSuccess = new LocalizeKey(LocalizeCategory.notifications, "notification.deletePlayerSuccess");
            public static LocalizeKey FriendRequestSuccess = new LocalizeKey(LocalizeCategory.notifications, "notification.friendRequestSuccess");
            public static LocalizeKey FriendRequestSent = new LocalizeKey(LocalizeCategory.notifications, "notification.friendRequestSent");
            public static LocalizeKey FriendDeleteSuccess = new LocalizeKey(LocalizeCategory.notifications, "notification.friendDeleteSuccess");
            public static LocalizeKey RegisterPlayerAgain = new LocalizeKey(LocalizeCategory.notifications, "notification.registerPlayerAgain");
            public static LocalizeKey AllowBluetoothPermissions = new LocalizeKey(LocalizeCategory.notifications, "notification.allowBluetoothPermissions");

            public static class Online
            {
                public static LocalizeKey YourRequestRefused = new LocalizeKey(LocalizeCategory.notifications, "notification.online.yourrequestrefused");
                public static LocalizeKey PreaparationDelayed = new(LocalizeCategory.notifications, "notification.online.preparationDelayed");
                public static LocalizeKey OpponentRematch = new LocalizeKey(LocalizeCategory.notifications, "notification.online.opponentRematch");
                public static LocalizeKey OpponentHasBeenQuited = new LocalizeKey(LocalizeCategory.notifications, "notification.online.opponenthasbeenQuited");
                public static LocalizeKey SelectLegsPlayOnline = new LocalizeKey(LocalizeCategory.notifications, "notification.online.selectlegsplayonline");
                public static LocalizeKey OpponentExitYourRoom = new LocalizeKey(LocalizeCategory.notifications, "notification.online.opponentExitYourRoom");

                public static LocalizeKey InProgressMatchFound = new LocalizeKey(LocalizeCategory.notifications, "notification.online.inProgressMatchFound");
                public static LocalizeKey MatchHasBeenTimedOut = new LocalizeKey(LocalizeCategory.notifications, "notification.online.matchHasBeenTimedOut");
            }
        }

        public static class Labels
        {
            public static LocalizeKey WelcomeBack = new LocalizeKey(LocalizeCategory.labels, "label.signIn.welcomeBack");
            public static LocalizeKey OK = new LocalizeKey(LocalizeCategory.labels, "button.ok");
            public static LocalizeKey Cancel = new LocalizeKey(LocalizeCategory.labels, "button.cancel");
            public static LocalizeKey Continue = new LocalizeKey(LocalizeCategory.labels, "button.continue");
            public static LocalizeKey Close = new LocalizeKey(LocalizeCategory.labels, "button.close");
            public static LocalizeKey SearchAgain = new LocalizeKey(LocalizeCategory.labels, "button.connectCameras.searchAgain");
            public static LocalizeKey ReturnToWifiSetting = new LocalizeKey(LocalizeCategory.labels, "button.connectCameras.returnToWifiSetting");

            // GranEye 自定义半径调整相关
            public static LocalizeKey SelectArea = new LocalizeKey(LocalizeCategory.labels, "label.graneye.selectArea");
            public static LocalizeKey DoubleOuter = new LocalizeKey(LocalizeCategory.labels, "label.graneye.doubleOuter");
            public static LocalizeKey TripleOuter = new LocalizeKey(LocalizeCategory.labels, "label.graneye.tripleOuter");
            public static LocalizeKey TripleInside = new LocalizeKey(LocalizeCategory.labels, "label.graneye.tripleInside");
            public static LocalizeKey OuterBull = new LocalizeKey(LocalizeCategory.labels, "label.graneye.outerBull");
            public static LocalizeKey InnerBull = new LocalizeKey(LocalizeCategory.labels, "label.graneye.innerBull");

            public static LocalizeKey Exit = new LocalizeKey(LocalizeCategory.labels, "label.Exit");
            public static LocalizeKey Searching = new LocalizeKey(LocalizeCategory.labels, "label.connectCameras.searching");
            public static LocalizeKey StartOTA = new LocalizeKey(LocalizeCategory.labels, "label.GranEyeFirmwareUpdate.StartOTA");
            public static LocalizeKey OTAinProgress = new LocalizeKey(LocalizeCategory.labels, "label.GranEyeFirmwareUpdate.OTAinProgress");

            public static LocalizeKey OpenAppSettings = new LocalizeKey(LocalizeCategory.labels, "button.openAppSettings");
            public static LocalizeKey ModeOn = new LocalizeKey(LocalizeCategory.labels, "label.ModeOn");
            public static LocalizeKey ModeOff = new LocalizeKey(LocalizeCategory.labels, "label.ModeOff");
            public static LocalizeKey Yes = new LocalizeKey(LocalizeCategory.labels, "label.Yes");
            public static LocalizeKey No = new LocalizeKey(LocalizeCategory.labels, "label.No");
            public static LocalizeKey Accept = new LocalizeKey(LocalizeCategory.labels, "label.Accept");
            public static LocalizeKey Decline = new LocalizeKey(LocalizeCategory.labels, "label.Decline");
            public static LocalizeKey Remove = new LocalizeKey(LocalizeCategory.labels, "label.Remove");
            public static LocalizeKey GameShot = new LocalizeKey(LocalizeCategory.labels, "label.GameShot");
            public static LocalizeKey GameFinish = new LocalizeKey(LocalizeCategory.labels, "label.GameFinish");

            public static LocalizeKey GranEyeSearch = new LocalizeKey(LocalizeCategory.labels, "label.graneye.search");
            public static LocalizeKey GranEyeReconnect = new LocalizeKey(LocalizeCategory.labels, "label.graneye.reconnect");
            public static LocalizeKey GranEyeMove = new LocalizeKey(LocalizeCategory.labels, "label.graneye.move");
            public static LocalizeKey Update = new LocalizeKey(LocalizeCategory.labels, "button.firmware.update");
            public static LocalizeKey NoUpdateLogin = new LocalizeKey(LocalizeCategory.labels, "label.appversion.noUpdateLogin");
            public static LocalizeKey AppQuit = new LocalizeKey(LocalizeCategory.labels, "label.appversion.appQuit");
            public static LocalizeKey ReturnToMatch = new LocalizeKey(LocalizeCategory.labels, "label.returnToMatch");
            public static LocalizeKey DiscardMatch = new LocalizeKey(LocalizeCategory.labels, "label.discardMatch");
            public static LocalizeKey GranEyeUpdate = new LocalizeKey(LocalizeCategory.labels, "label.update");
            public static LocalizeKey GranEyeUpdateLater = new LocalizeKey(LocalizeCategory.labels, "label.updateLater");
            public static LocalizeKey Times = new LocalizeKey(LocalizeCategory.labels, "label.times");
            public static LocalizeKey PlayerBlocked = new LocalizeKey(LocalizeCategory.labels, "label.playerBlocked");
            public static LocalizeKey Retry = new LocalizeKey(LocalizeCategory.labels, "label.retry");
            public static LocalizeKey AdjustLeftFocus = new LocalizeKey(LocalizeCategory.labels, "label.adjustLeftFocus");
            public static LocalizeKey AdjustRightFocus = new LocalizeKey(LocalizeCategory.labels, "label.adjustRightFocus");
            public static LocalizeKey GoToSettings = new LocalizeKey(LocalizeCategory.labels, "label.goToSettings");
        }

        public static class Tips
        {
            public static LocalizeKey NewFirmWare = new LocalizeKey(LocalizeCategory.tips, "tips.NewFirmWare");
            public static LocalizeKey RestartCamera = new LocalizeKey(LocalizeCategory.tips, "tips.graneye.restartCamera");
            public static LocalizeKey WelComeGranboardUser = new LocalizeKey(LocalizeCategory.tips, "tips.granboardUser");
            public static LocalizeKey AIModelTestProFastest = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.proFastest");
            public static LocalizeKey AIModelTestBasicFastest = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.basicFastest");
            public static LocalizeKey AIModelTestLiteFastest = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.liteFastest");
            public static LocalizeKey AIModelTestAllSlow = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.allSlow");
            public static LocalizeKey AIModelInstructions = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.instructions");
            public static LocalizeKey AIModelNoModelAvailable = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.NoModelAvailable");
            public static LocalizeKey AIModelUsableLiteModel = new LocalizeKey(LocalizeCategory.tips, "tips.aimodel.usableLiteModel");

            public static LocalizeKey ManualGuideStep1Describe = new LocalizeKey(LocalizeCategory.tips, "tips.manualGuideStep1Describe");
            public static LocalizeKey ManualGuideStep2Describe = new LocalizeKey(LocalizeCategory.tips, "tips.manualGuideStep2Describe");
            public static LocalizeKey ManualGuideStep3Describe = new LocalizeKey(LocalizeCategory.tips, "tips.manualGuideStep3Describe");

            public static LocalizeKey USB = new LocalizeKey(LocalizeCategory.tips, "tips.usb");
            public static LocalizeKey USBCable = new LocalizeKey(LocalizeCategory.tips, "tips.usbCable");
            public static LocalizeKey OTGCable = new LocalizeKey(LocalizeCategory.tips, "tips.otgCable");
            public static LocalizeKey USBComplete = new LocalizeKey(LocalizeCategory.tips, "tips.usbComplete");
            public static LocalizeKey USBSearchFailed = new LocalizeKey(LocalizeCategory.tips, "tips.usb.searchFailed");

            // USB数据线确认弹窗相关
            public static LocalizeKey UsbCableRequiredMessage = new LocalizeKey(LocalizeCategory.tips, "tips.usbCableRequiredMessage");

            // 早期用户更新相关
            public static LocalizeKey CameraConnectError = new LocalizeKey(LocalizeCategory.tips, "tips.cameraConnectError");
            public static LocalizeKey Update = new LocalizeKey(LocalizeCategory.tips, "tips.update");
            public static LocalizeKey UpdateFailed = new LocalizeKey(LocalizeCategory.tips, "tips.updateFailed");
            public static LocalizeKey FirmwareBackup = new LocalizeKey(LocalizeCategory.tips, "tips.firmwareBackup");
            public static LocalizeKey CameraSearchUpdate = new LocalizeKey(LocalizeCategory.tips, "tips.cameraSearchUpdate");
            public static LocalizeKey BackupFailed = new LocalizeKey(LocalizeCategory.tips, "tips.backupFailed");
            public static LocalizeKey ResetButtonUpdate = new LocalizeKey(LocalizeCategory.tips, "tips.resetButtonUpdate");
            public static LocalizeKey Completion = new LocalizeKey(LocalizeCategory.tips, "tips.completion");
        }

        public static class Prompts
        {
            public static LocalizeKey FriendBlock = new LocalizeKey(LocalizeCategory.prompts, "prompt.friendBlock");
            public static LocalizeKey GranEyeStatusWithUSB = new LocalizeKey(LocalizeCategory.prompts, "prompt.granEyeStatusWithUSB");
            public static LocalizeKey ChangeWifiMode = new LocalizeKey(LocalizeCategory.prompts, "prompt.changeWifiMode");
        }

        public static class Systems
        {
            public static LocalizeKey UnexpectedError = new LocalizeKey(LocalizeCategory.systems, "unexpectedError");
            public static LocalizeKey GranEyeHomoCoreIsNotInitError = new LocalizeKey(LocalizeCategory.systems, "GranEyeHomoCoreIsNotInitError");
            public static LocalizeKey Yourpasswordisincorrect = new LocalizeKey(LocalizeCategory.systems, "Yourpasswordisincorrect");

            public static LocalizeKey Youraccountcannotbedeleted = new LocalizeKey(LocalizeCategory.systems, "Youraccountcannotbedeleted");
            public static LocalizeKey Youraccountcannotbedeleted_finishyouronlinematch = new LocalizeKey(LocalizeCategory.systems, "Youraccountcannotbedeleted.finishyouronlinematch");
            public static LocalizeKey Youraccountcannotbedeleted_currentlyenteredtournament = new LocalizeKey(LocalizeCategory.systems, "Youraccountcannotbedeleted.currentlyenteredtournament");
            public static LocalizeKey CropImageException = new LocalizeKey(LocalizeCategory.systems, "CropImageException");
            public static LocalizeKey AlreadyInTheRoomException = new LocalizeKey(LocalizeCategory.systems, "isalreadyintheroom");
            public static LocalizeKey CameraPermissionRequired = new LocalizeKey(LocalizeCategory.systems, "CameraPermissionRequired");
            public static LocalizeKey MicPermissionRequired = new LocalizeKey(LocalizeCategory.systems, "MicPermissionRequired");
            public static LocalizeKey UserNotFoundException = new LocalizeKey(LocalizeCategory.systems, "UserNotFoundException");
            public static LocalizeKey LimitExceededException = new LocalizeKey(LocalizeCategory.systems, "LimitExceededException");
            public static LocalizeKey InvalidMatchRoomStatus_WaitingForOpponent = new LocalizeKey(LocalizeCategory.systems, "InvalidMatchRoomStatus.WaitingForOpponent");
            public static LocalizeKey InvalidVerifyCode = new LocalizeKey(LocalizeCategory.systems, "LimitExceededException");
            public static LocalizeKey InvalidEmailAddress = new LocalizeKey(LocalizeCategory.systems, "InvalidEmailAddress");
            public static LocalizeKey MatchRoomHasBeenDeletedException = new LocalizeKey(LocalizeCategory.systems, "notification.online.roomdeleted");
            public static LocalizeKey GuestExistedAlready = new LocalizeKey(LocalizeCategory.systems, "GuestExistedAlready");
            public static LocalizeKey GranIdWasDuplicatedException = new LocalizeKey(LocalizeCategory.systems, "GranIdWasDuplicatedException");
            public static LocalizeKey PlayerCountHasReachedLimitException = new LocalizeKey(LocalizeCategory.systems, "PlayerCountHasReachedLimitException");
            public static LocalizeKey UnhandledException = new LocalizeKey(LocalizeCategory.systems, "UnhandledException");

            public static LocalizeKey NetworkError = new LocalizeKey(LocalizeCategory.systems, "NetworkError");
            public static LocalizeKey GranIdValidationException = new LocalizeKey(LocalizeCategory.systems, "GranIdValidationException");

            public static LocalizeKey ProcessTimeout = new LocalizeKey(LocalizeCategory.systems, "ProcessTimeout");
            public static LocalizeKey SessionTimeout = new LocalizeKey(LocalizeCategory.systems, "SessionTimeout");
            public static LocalizeKey SignUpAccountAlreadyExist = new LocalizeKey(LocalizeCategory.systems, "SignUpAccountAlreadyExist");
        }

    }
}
