using UnityEngine.Networking;
using Cysharp.Threading.Tasks;
using com.luxza.granlog;

namespace com.luxza.grandarts.userinterfaces.page.splash
{
    public class GlobalIPRegister
    {
        private const string GetGlobalIPURL = "https://checkip.amazonaws.com/";
        private const string RegisterGlobalIPURL = "https://3tuv7a65gyjre4q2pwkzvuqcnm0bklsf.lambda-url.us-east-1.on.aws/";

        public GlobalIPRegister()
        {
            Log.d("Init GlobalIPRegister");
        }

        public async UniTask<bool> RequestRegisterGlobalIP()
        {
            UnityWebRequest request = UnityWebRequest.Get(GetGlobalIPURL);


            await request.SendWebRequest().ToUniTask();

            if (request.result == UnityWebRequest.Result.Success && request.responseCode == 200)
            {
                string jsonText = request.downloadHandler.text;
                string gloabalIPText = jsonText.Replace("\n", "").Replace("\r", "");

                Log.d("RequestGlobalIP Success: " + gloabalIPText);

                return await RequestRegisterGlobalIPURL(gloabalIPText);
            }
            else
            {
                string serverResponse = request.downloadHandler?.text ?? "No response";
                Log.e($"HTTP Error {request.responseCode}: {request.error}, Response: {serverResponse}");
                return false;
            }

        }

        private async UniTask<bool> RequestRegisterGlobalIPURL(string gloabalIPText)
        {
            if (string.IsNullOrEmpty(gloabalIPText))
            {
                Log.e("Invalid IP address: The input is null or empty.");
                return false;
            }

            Log.d("RequestRegisterGlobalIPURL: " + gloabalIPText);

            string jsonData = $"{{\"ip\": \"{gloabalIPText}\"}}";

            UnityWebRequest request = new UnityWebRequest(RegisterGlobalIPURL, "POST")
            {
                uploadHandler = new UploadHandlerRaw(System.Text.Encoding.UTF8.GetBytes(jsonData)),
                downloadHandler = new DownloadHandlerBuffer()
            };
            request.SetRequestHeader("Content-Type", "application/json");
            await request.SendWebRequest().ToUniTask();

            if (request.result == UnityWebRequest.Result.Success && request.responseCode == 200)
            {
                string jsonText = request.downloadHandler.text;
                Log.d("RequestRegisterGlobalIPURL Success: " + jsonText);
                return true;
            }
            else
            {
                string serverResponse = request.downloadHandler?.text ?? "No response";
                Log.e($"HTTP Error {request.responseCode}: {request.error}, Response: {serverResponse}");
                return false;
            }

        }
    }
}