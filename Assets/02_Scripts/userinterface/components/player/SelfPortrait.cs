using com.luxza.grandarts.usecases.auth;
using UnityEngine;
using R3;

namespace com.luxza.grandarts.userinterfaces.components.player
{
    public class SelfPortrait : MonoBehaviour
    {
        [SerializeField] private PlayerIcon _portrait;


        void Awake() {
            if(ApplicationAuth.IsLoggedIn) {
                if(ApplicationAuth.LoggedInUser.HasCurrentPlayer) {
                    _portrait.Bind(ApplicationAuth.LoggedInUser.CurrentPlayer);
                }

                ApplicationAuth.LoggedInUser.OnSwitchPlayer.Subscribe(player => {
                    _portrait.Bind(player);
                }).RegisterTo(destroyCancellationToken);
            } else {
                _portrait.UnBind();
            }
        }
    }
}