using com.luxza.ui.components.molecules;
using UnityEngine;
using R3;
using AeLa.EasyFeedback;
using com.luxza.granlog;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.infrastructures.graneye.exception;

namespace com.luxza.grandarts.userinterface.game.graneye
{
    public class GranEyeBugReporter : MonoBehaviour
    {
        [SerializeField] private GranButton bugReportButton;
        private void Awake()
        {
            bugReportButton.gameObject.SetActive(true);
            bugReportButton.onClickAsObservable.Subscribe(OnBugReportClicked).RegisterTo(destroyCancellationToken);
#if Production
            bugReportButton.gameObject.SetActive(Application.version.Split('.')[0].Equals("0"));
#endif
        }

        private async void OnBugReportClicked(Unit _)
        {
            FeedbackForm feedbackForm = FindFirstObjectByType<FeedbackForm>();
            if (feedbackForm == null)
            {
                Log.e("FeedbackForm is not found");
                return;
            }
            feedbackForm.Show();
            using var cameras = new GranEyeCameras();
            try
            {
                var tBack_Left = await cameras.TakePictureLeft();
                var tLeftCameraData = (tBack_Left as Texture2D).EncodeToJPG();
                feedbackForm.CurrentReport.AttachFile("LeftCamera", tLeftCameraData);
            }
            catch (TakePictureFailedException)
            {
                ;
            }

            try
            {
                var tBack_Right = await cameras.TakePictureRight();
                var tRightCameraData = (tBack_Right as Texture2D).EncodeToJPG();
                feedbackForm.CurrentReport.AttachFile("RightCamera", tRightCameraData);
            }
            catch (TakePictureFailedException)
            {
                ;
            }
        }
    }
}