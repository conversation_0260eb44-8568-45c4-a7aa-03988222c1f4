using System.Linq;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandartslogic.domain.settings;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using Cysharp.Threading.Tasks;
using TMPro;
using UnityEngine;
using R3;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.userinterfaces.components.game.play;
using com.luxza.grandarts.utils;

namespace com.luxza.grandarts.userinterfaces.components.game.setting.cricket
{
    public class LocalMedleyStandardCROptionView : MonoBehaviour {
        [SerializeField] GranTab _bestOfOrFirstToTab;
        [SerializeField] GranPullDown _setPullDown;
        [SerializeField] GranPullDown _legsPullDown;
        [SerializeField] GranToggle _looserFirstToggle;
        [SerializeField] GranToggle _swapFirstToggle;

        [SerializeField] GranToggle _maxRound10Toggle;
        [SerializeField] GranToggle _maxRound15Toggle;
        [SerializeField] GranToggle _maxRound20Toggle;
        [SerializeField] GranToggle _maxRound25Toggle;
        [SerializeField] GranToggle _maxRound30Toggle;
        [SerializeField] GranToggle _maxRoundFreeToggle;

        [SerializeField] GranSwitchToggle _autoHandicapToggle;

        [SerializeField] GameObject _loadingObject;

        [SerializeField] CanvasGroup _canvasGroup;

        [SerializeField] GranText _requestFormatText;

        public MedleyRequestFormat RequestFormat { get; private set; }

        private async void Awake() {
            _setPullDown.ReplaceOptions(Enumerable.Range(1, Sets.MaxSets).Select(i => new TMP_Dropdown.OptionData(i.ToString())).ToList());
            await UniTask.WaitUntil(() => ApplicationAuth.IsLoggedIn);
            await LoadFormatAsync();

            _bestOfOrFirstToTab.onSelectedIndexChanged += index => {
                var format = index == 0 ? Legs.Format.BestOf : Legs.Format.FirstTo;
                bool isOddLegsOnly = format == Legs.Format.BestOf && RequestFormat.Sets.Value > 1;
                if (isOddLegsOnly)
                {
                    RefreshLegsPullDown(isOddLegsOnly);
                    _legsPullDown.SetValueWithoutNotify(isOddLegsOnly ? 1 : 2);
                }
                else if (format == Legs.Format.FirstTo && _legsPullDown.GetOptionDatas.Count != Legs.MaxLegs)
                {
                    RefreshLegsPullDown(false);
                    _legsPullDown.SetValueWithoutNotify(2);
                }
                RequestFormat.SetLegs(new Legs(3, format), grandartslogic.domain.game.GameCode._StandardCR);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            };

            _setPullDown.onValueChanged.AddListener(i => {
                var beforeSets = RequestFormat.Sets;
                RequestFormat.SetSets(new Sets(_setPullDown.value + 1), RequestFormat.Legs, grandartslogic.domain.game.GameCode._StandardCR);
                if (RequestFormat.Legs.LegsFormat == Legs.Format.BestOf)
                {
                    if (beforeSets == 1 && RequestFormat.Sets.Value > 1)
                    {
                        RefreshLegsPullDown(RequestFormat.Sets.Value > 1);
                        _legsPullDown.SetValueWithoutNotify(1);
                        RequestFormat.SetLegs(new Legs(3, RequestFormat.Legs.LegsFormat), grandartslogic.domain.game.GameCode._StandardCR);
                    }
                    if (beforeSets > 1 && RequestFormat.Sets.Value == 1)
                    {
                        RefreshLegsPullDown(RequestFormat.Sets.Value > 1);
                        _legsPullDown.SetValueWithoutNotify(2);
                        RequestFormat.SetLegs(new Legs(3, RequestFormat.Legs.LegsFormat), grandartslogic.domain.game.GameCode._StandardCR);
                    }
                }
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            });

            _legsPullDown.onValueChanged.AddListener(i => {
                var selectText = _legsPullDown.GetOptionDatas[i].text;
                int legCount = 1;
                if (int.TryParse(selectText, out legCount)){
                    RequestFormat.SetLegs(new Legs(legCount, RequestFormat.Legs.LegsFormat), grandartslogic.domain.game.GameCode._StandardCR);
                }
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            });

            _looserFirstToggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (isOn) RequestFormat.ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.LooserFirst;
            }).RegisterTo(destroyCancellationToken);

            _swapFirstToggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (isOn) RequestFormat.ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.TakeTurn;
            }).RegisterTo(destroyCancellationToken);

            _maxRound10Toggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(10);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _maxRound15Toggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(15);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _maxRound20Toggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(20);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _maxRound25Toggle.onValueChangedAsObservable.Subscribe(isOn => {
                if(!isOn) return;
                RequestFormat.MaxRound = new MaxRound(25);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _maxRound30Toggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (!isOn) return;
                RequestFormat.MaxRound = new MaxRound(30);
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _maxRoundFreeToggle.onValueChangedAsObservable.Subscribe(isOn => {
                if (!isOn) return;
                RequestFormat.MaxRound = MaxRound.Free();
                _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1,1,RequestFormat);
            }).RegisterTo(destroyCancellationToken);

            _autoHandicapToggle.onValueChangedObservable.Subscribe(isOn => {
                RequestFormat.HandicapSetting = isOn ? HandicapSetting.Auto : HandicapSetting.None;
            }).RegisterTo(destroyCancellationToken);
        }

        private async UniTask LoadFormatAsync() {
            Loading(true);
            try {
                var usecase = new GetCricketOnlyMedleyPlayFormatUsecase(DIContainer.Instance.Resolve<ILocalGameRequestFormatRepository>());
                var format = await usecase.ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer, destroyCancellationToken);
                RequestFormat = format.RequestFormat;
                OnEnable();
            } finally {
                Loading(false);
            }
        }

        private void RefreshLegsPullDown(bool isOddLegsOnly)
        {
            var legs = Enumerable.Range(1, Legs.MaxLegs);
            if (isOddLegsOnly) legs = legs.Where(leg => leg % 2 != 0);

            _legsPullDown.ReplaceOptions(legs.Select(i => new TMP_Dropdown.OptionData(i.ToString())).ToList());
        }

        private void OnEnable()
        {
            if (RequestFormat == null) return;
            _bestOfOrFirstToTab.SetIndexDelay(RequestFormat.Legs.LegsFormat == Legs.Format.BestOf ? 0 : 1);
            _setPullDown.SetValueWithoutNotify(RequestFormat.Sets - 1);
            RefreshLegsPullDown(RequestFormat.Sets.Value > 1);
            var index = _legsPullDown.GetOptionDatas.FirstIndex(v => v.text.Equals(RequestFormat.Legs.Value.ToString()));
            _legsPullDown.SetValueWithoutNotify(index);
            _looserFirstToggle.SetIsOnWithoutNotify(RequestFormat.ThrowOrderAtMiddleLeg == ThrowOrderAtMiddleLeg.LooserFirst);
            _swapFirstToggle.SetIsOnWithoutNotify(RequestFormat.ThrowOrderAtMiddleLeg == ThrowOrderAtMiddleLeg.TakeTurn);

            _maxRound10Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(10));
            _maxRound15Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(15));
            _maxRound20Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(20));
            _maxRound25Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(25));
            _maxRound30Toggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == new MaxRound(30));
            _maxRoundFreeToggle.SetIsOnWithoutNotify(RequestFormat.MaxRound == MaxRound.Free());

            _autoHandicapToggle.SetIsOnWithoutAnimation(RequestFormat.HandicapSetting == HandicapSetting.Auto);

            _requestFormatText.text = GameFormatUtility.GameFormatTextWithOptions(1, 1, RequestFormat);
        }

        private void Loading(bool isLoading) {
            _loadingObject.SetActive(isLoading);
            _canvasGroup.alpha = isLoading ? 0.5f : 1f;
            _canvasGroup.interactable = !isLoading;
        }

        private void OnDestroy() {
            new SaveCricketMedleyRequestFormatUsecase(DIContainer.Instance.Resolve<ILocalGameRequestFormatRepository>()).ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer.Id, RequestFormat, destroyCancellationToken).SafeForget();
        }
    }
}