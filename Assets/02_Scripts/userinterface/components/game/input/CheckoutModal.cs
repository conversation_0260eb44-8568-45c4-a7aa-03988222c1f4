using System;
using System.Linq;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.input {
    public class CheckoutModal : MonoBehaviour {
        [SerializeField] private GranButton _confirmButton;
        [SerializeField] private GranToggle _1TryToggle;
        [SerializeField] private GranToggle _2TryToggle;
        [SerializeField] private GranToggle _3TryToggle;
        [SerializeField] private GranToggle _1CheckoutToggle;
        [SerializeField] private GranToggle _2CheckoutToggle;
        [SerializeField] private GranToggle _3CheckoutToggle;

        [SerializeField] private GranToggleGroup _tryGroup;
        [SerializeField] private GranToggleGroup _checkoutGroup;

        [SerializeField] private Page _page;

        CheckoutModalParameter _parameter;

        void Awake() {
            _confirmButton.interactable = _tryGroup.gameObject.activeSelf ? _tryGroup.ActiveToggles().Count() > 0 : false && _checkoutGroup.ActiveToggles().Count() > 0;
            _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        }

        private void OnActivate(Unit _)
        {
            if(!_page.Meta.TryGetParameter<CheckoutModalParameter>(out var parameter)) {
                throw new ArgumentException("CheckoutModalParameter is required.");
            }
            _parameter = parameter;
            _confirmButton.onClickAsObservable.Subscribe(OnClickConfim).RegisterTo(destroyCancellationToken);

            if(_parameter._outCondition == grandartslogic.domain.game.zeroone.OutCondition.DoubleOut) {
                _1TryToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 1);
                _2TryToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 2);
                _3TryToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 3);

                _1TryToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
                _2TryToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
                _3TryToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
            } else {
                _tryGroup.gameObject.SetActive(false);
            }

            _1CheckoutToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 3);
            _2CheckoutToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 2);
            _3CheckoutToggle.gameObject.SetActive(parameter._possibleMaxTryCount >= 1);

            _1CheckoutToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
            _2CheckoutToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
            _3CheckoutToggle.onValueChangedAsObservable.Subscribe(OnAnyToggleValueChanged).RegisterTo(destroyCancellationToken);
        }

        private async void OnClickConfim(Unit _)
        {
            _parameter._onComfirm?.Invoke(_1CheckoutToggle.isOn ? 1 : _2CheckoutToggle.isOn ? 2 : 3, _1TryToggle.isOn ? 1 : _2TryToggle.isOn ? 2 : 3);
            await PageManager.Instance.CloseModalAsync(_page);
        }

        private void OnAnyToggleValueChanged(bool value)
        {
            _confirmButton.interactable = _tryGroup.gameObject.activeSelf ? _tryGroup.ActiveToggles().Count() > 0 : false && _checkoutGroup.ActiveToggles().Count() > 0;
        }
    }
}