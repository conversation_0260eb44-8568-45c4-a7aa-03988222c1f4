using R3;
using System;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;
using com.luxza.ui.page;
using com.luxza.ui.components.atoms;
using com.luxza.grandarts.utils;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandarts.domains.game.context.hiddencutthroat;


namespace com.luxza.grandarts.userinterfaces.components.game.play.local.hiddencutthroat
{
    [DisallowMultipleComponent]
    public class HiddenCutthroatMultiScoreView : MonoBehaviour
    {
        [SerializeField] private GameObject _backgroundOwner;
        [SerializeField] private RawImage _backgroundImage;
        [SerializeField] private Image _unitColor;

        [SerializeField] private GranText _gameFormatText;

        [SerializeField] private UnitIconsWithScoreListView _unitIcons;

        [SerializeField] private HiddenCutthroatGameContextAdoptor _adoptor;

        private PlayUnit[] _playUnits;
        private GameHiddenCutthroatSessionContext _context;

        private void Awake()
        {
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context as GameHiddenCutthroatSessionContext;
                if (_context == null)
                {
                    throw new ArgumentException("INvalid context type.");
                }
                Bind(context.DataRetriver.BasicGameDataRetriver.PlayUnits.ToArray());
                context.GameEventPublisher.OnStartMatch += OnStartMatch;
                context.GameEventPublisher.OnUpdateProgress += (_) => OnProgressUpdated();
                context.GameEventPublisher.OnRoundReverse += (_) => OnProgressUpdated();
                context.GameEventPublisher.OnThrowReverse += (_) => OnProgressUpdated();
                context.GameEventPublisher.OnOverrideThrow += (_, _) => OnProgressUpdated();
                context.GameEventPublisher.OnChange += (_) => OnThrowerChanged();
            }).RegisterTo(destroyCancellationToken);

            _gameFormatText.text = string.Empty;
        }

        private void Bind(PlayUnit[] units)
        {
            _playUnits = units;
            _unitIcons.Bind(units, showScore: true);
        }

        private void ApplyThrower((UnitId unitId, PlayerId playerId) thrower)
        {
            _unitIcons.UpdateThrower(thrower.playerId);
            _unitColor.color = UnitColors.FromIndex(_playUnits.FirstIndex(u => u.Id == thrower.unitId));
        }

        private void OnStartMatch()
        {
            _unitIcons.UpdateThrower(_playUnits.First().HostPlayerId);
            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
            _unitColor.color = UnitColors.FromIndex(_playUnits.FirstIndex(u => u.Id == _playUnits.First().Id));
            _gameFormatText.text = GameFormatUtility.CutThroatGameFormatTextWithOptions(_context.CutthroatCRPlayFormat.RequestFormat, 1);
        }

        private void OnProgressUpdated()
        {
            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
        }

        private void OnThrowerChanged()
        {
            var playerId = _context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id;
            _unitIcons.UpdateThrower(playerId);
            _unitColor.color = UnitColors.FromIndex(_playUnits.FirstIndex(u => u.IsMember(playerId)));
            _gameFormatText.text = GameFormatUtility.CutThroatGameFormatTextWithOptions(_context.CutthroatCRPlayFormat.RequestFormat, _context.DataRetriver.BasicGameDataRetriver.CurrentRound.No);
        }
    }
}
