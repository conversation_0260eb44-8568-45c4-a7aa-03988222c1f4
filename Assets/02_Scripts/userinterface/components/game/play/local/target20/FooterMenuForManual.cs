using System;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.input;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandartslogic.domain.game;
using com.luxza.ui.components.atoms;
using com.luxza.ui.page;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.target20
{
    public class FooterMenuForManual : MonoBehaviour
    {
        [SerializeField] protected BasicGameContextAdoptor _adoptor;
        [SerializeField] protected GranIconButton _btn_ThrowReverse;
        [SerializeField] protected GranIconButton _btn_RoundResult;
        [SerializeField] protected GranIconButton _btn_PlayData;
        [SerializeField] protected GranIconButton _btn_Menu;
        [SerializeField] protected Target20RoundResult _roundResult;
        [SerializeField] protected Target20PlayData _playData;

        private IGameSessionContext _context;
        private bool _isGameOver = false;
        
        private void Awake()
        {
            _adoptor.OnReady.Take(1).Subscribe(context => {
                _context = context;
                if(_context == null) {
                    throw new ArgumentException("Invalid context type.");
                }
                _roundResult.Init(_context);
                _playData.Init(_context);
                _context.GameEventPublisher.OnFinishMatch += OnGameFinished;
            }).RegisterTo(destroyCancellationToken);
            
            _btn_ThrowReverse.onClickAsObservable.Subscribe(OnThrowReverseClicked).RegisterTo(destroyCancellationToken);
            _btn_RoundResult.onClickAsObservable.Subscribe(OnRoundResultClicked).RegisterTo(destroyCancellationToken);
            _btn_PlayData.onClickAsObservable.Subscribe(OnPlayDataClicked).RegisterTo(destroyCancellationToken);
            _btn_Menu.onClickAsObservable.Subscribe(OnMenuClicked).RegisterTo(destroyCancellationToken);
        }
        
        private void OnGameFinished(MatchFinishStatus status)
        {
            _isGameOver = true;
        }
        
        private void OnThrowReverseClicked(Unit unit)
        {
            if (_isGameOver)
            {
                return;
            }
            GameInputHub.Instance.NotifyOnRevertThrowReceived();
        }
        private void OnRoundResultClicked(Unit unit)
        {
            if (_isGameOver)
            {
                return;
            }
            this._playData.Hide();
            if (_roundResult.IsOpened)
            {
                this._roundResult.Hide();
            }
            else
            {
                this._roundResult.Open();
            }
        }
        private void OnPlayDataClicked(Unit unit)
        {
            if (_isGameOver)
            {
                return;
            }
            this._roundResult.Hide();
            if (_playData.IsOpened)
            {
                this._playData.Hide();
            }
            else
            {
                this._playData.Open();
            }
        }
        private async void OnMenuClicked(Unit unit)
        {
            if (_isGameOver)
            {
                return;
            }
            this._roundResult.Hide();
            this._playData.Hide();
            await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.LocalGameMenu));
        }
    }
}