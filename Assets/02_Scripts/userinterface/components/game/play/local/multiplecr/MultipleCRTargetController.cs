using System;
using com.luxza.grandarts.domains.game.context.multiplecr;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.multiplecr;
using R3;
using UnityEngine;
using UnityEngine.UI;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.multiplecr
{
    public class MultipleCRTargetController : MonoBehaviour
    {
        [SerializeField] private BasicGameContextAdoptor _adoptor;
        [SerializeField] private GameObject[] _throwAtList;
        [SerializeField] private GameObject[] _obj_AreaList;
        
        private GameMultipleCRSessionContext _context;
        private GameRuleMultipleCR _rule;
        
        private void Awake() {
            _adoptor.OnReady.Subscribe(context =>
            {
                _context = context as GameMultipleCRSessionContext;
                if (_context == null)
                {
                    throw new ArgumentException("Rotation context is null.");
                }
                _rule = _context.DataRetriver.GameRule;
                _context.GameEventPublisher.OnStartMatch += OnStartMatch;
                _context.GameEventPublisher.OnUpdateProgress += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnChange += (_) => OnThrowerChanged();
                _context.GameEventPublisher.OnRoundReverse += OnRoundReverse;
                _context.GameEventPublisher.OnOverrideThrow += (_, _) => OnProgressUpdated();
                _context.GameEventPublisher.OnRoundReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnThrowReverse += (_) => OnProgressUpdated();
            }).RegisterTo(destroyCancellationToken);
        }
        
        private void OnStartMatch()
        {
            this.ShowTargetForCurrentRound();
        }

        private void OnProgressUpdated()
        {
            
        }

        private void OnThrowerChanged()
        {
            this.ShowTargetForCurrentRound();
        }
        
        private void OnRoundReverse(grandartslogic.Unit unit)
        {
            this.ShowTargetForCurrentRound();
        }
        
        private void ShowTargetForCurrentRound()
        {
            var target = _context.DataRetriver.TargetSegmentInRound;
            this.ShowTargetArea(target, _rule.Option);
        }
        
        private void ShowTargetArea(Segment target, GameRuleMultipleCR.MultipleCROption option)
        {
            var num = target.IsBull ?  21 : target.PositionCode;
            for (int i = 0; i < _obj_AreaList.Length; i++)
            {
                _obj_AreaList[i].SetActive(num - 1 == i);
                _throwAtList[i].SetActive(num - 1 == i);
                if (num - 1 == i)
                {
                    var tList = _obj_AreaList[i].GetComponentsInChildren<Image>(true);
                    for (int j = 0; j < tList.Length; j++)
                    {
                        if (option == GameRuleMultipleCR.MultipleCROption.TripleOut)
                        {
                            tList[j].gameObject.SetActive(tList[j].name.Equals("SegmentTriple"));
                        }
                        else
                        {
                            tList[j].gameObject.SetActive(true);
                        }
                    }
                }
            }
        }
    }
}