using com.luxza.grandartslogic.domain.game;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces
{
    public class HalfItTarget : MonoBehaviour
    {
        [SerializeField] private Segment _segment;
        [SerializeField] private GameObject _target;
        [SerializeField] private GameObject _targetSegmentImage;

        public Segment TargetSegment => _segment;
        public void Init()
        {
            Reset();
        }

        public void Show()
        {
            _target.SetActive(true);
            if (_targetSegmentImage != null) _targetSegmentImage.SetActive(true);
        }

        public void Reset()
        {
            _target.SetActive(false);
            if (_targetSegmentImage != null) _targetSegmentImage.SetActive(false);
        }
    }
}
