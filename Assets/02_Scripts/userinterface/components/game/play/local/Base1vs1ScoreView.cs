using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local
{
    public class Base1vs1ScoreView : MonoBehaviour
    {
        protected void UnitColorActiveAnimation(Image targetImage)
        {
            var startAlpha = 0.1f;
            var fadeDuration = 1.0f;
            var targetColor = UnitColors.FromIndex(0);
            targetImage.DOKill();
            targetImage.color = new Color(targetColor.r, targetColor.g, targetColor.b, startAlpha);
            targetImage.DOFade(1, fadeDuration).SetEase(Ease.Linear).SetLink(targetImage.gameObject);
        }
    }
}