using com.luxza.ui.components.molecules;
using UnityEngine;
using R3;
using System;
using com.luxza.ui.page;
using com.luxza.grandarts.userinterfaces.page.utils;

namespace com.luxza.grandarts.userinterfaces.page {
    public class MedleyGameSelectViewController : MonoBehaviour
    {
        [SerializeField] private GranButton _x01Button;
        [SerializeField] private GranButton _cricketButton;
        [SerializeField] private GranButton _mixButton;
        private void Awake()
        {
            _x01Button.onClickAsObservable.Subscribe(OnClickX01Button).RegisterTo(destroyCancellationToken);
            _cricketButton.onClickAsObservable.Subscribe(OnClickCricketButton).RegisterTo(destroyCancellationToken);
            _mixButton.onClickAsObservable.Subscribe(OnClickMixButton).RegisterTo(destroyCancellationToken);
        }

        private async void OnClickMixButton(Unit unit)
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalMixMedleyGameSetting));
        }

        private async void OnClickCricketButton(Unit _)
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalCricketMedleyGameSetting));
        }

        private async void OnClickX01Button(Unit _)
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalX01MedleyGameSetting));
        }
    }
}