using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.result.match;
using com.luxza.grandarts.domains.game.result.match.zeroone;
using com.luxza.ui.page;

namespace com.luxza.grandarts.userinterfaces.page.parameters {
    public class Localx01PageParameter : IPageParameter
    {
        public readonly MatchID MatchID;
        public readonly x01PlayFormat PlayFormat;
        public readonly ZeroOneMatchProgress x01Progress;
        public readonly MedleyMatchProgress MedleyMatchProgress;
        public readonly MedleyPlayFormat MedleyPlayFormat;

        public bool IsMedley => MedleyPlayFormat != null;
        public bool IsInMedleyProgress => MedleyMatchProgress != null;

        public Localx01PageParameter(MatchID matchID, x01PlayFormat playFormat)
        {
            MatchID = matchID;
            PlayFormat = playFormat;
            MedleyPlayFormat = null;
            x01Progress = null;
            MedleyMatchProgress = null;
        }

        public Localx01PageParameter(MatchID matchID, ZeroOneMatchProgress progress)
        {
            MatchID = matchID;
            x01Progress = progress;
            PlayFormat = progress.PlayFormat;
            MedleyPlayFormat = null;
            MedleyMatchProgress = null;
        }

        public Localx01PageParameter(MatchID matchID, MedleyPlayFormat playFormat)
        {
            MatchID = matchID;
            MedleyMatchProgress = null;
            MedleyPlayFormat = playFormat;
            PlayFormat = new x01PlayFormat(
                playFormat.EntrySlots,
                playFormat.RequestFormat.Createx01RequestFormat(1, 1)
            );
            x01Progress = null;
        }

        public  Localx01PageParameter(MedleyMatchProgress progress)
        {
            MatchID = progress.MatchID;
            MedleyMatchProgress = progress;
            MedleyPlayFormat = progress.PlayFormat;
            PlayFormat = new x01PlayFormat(
                MedleyMatchProgress.PlayFormat.EntrySlots,
                MedleyMatchProgress.PlayFormat.RequestFormat.Createx01RequestFormat(progress.SetNo, progress.LegNo)
            );
            x01Progress = null;
        }
    }
}