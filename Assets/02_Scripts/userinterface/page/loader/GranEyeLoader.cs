using com.luxza.grandarts.userinterfaces.page.utils;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace com.luxza.grandarts.userinterfaces.page.loader
{
    public class GranEyeLoader : MonoBehaviour
    {
        async void Awake()
        {
            var scene = SceneManager.GetSceneByName(PageNames.GranEye);
            if (!scene.isLoaded)
            {
                AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(PageNames.GranEye, LoadSceneMode.Additive);
                await asyncLoad.ToUniTask();
            }
        }
    }
}