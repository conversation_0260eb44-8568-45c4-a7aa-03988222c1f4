using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.ui.components.atoms;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.page.onlineTop
{
    public class GlobalLobbyInMatchCountViewController : MonoBehaviour
    {
        [SerializeField] private GranText _playingCountInGlobalLobbyCountText;

        private void Awake()
        {
            _playingCountInGlobalLobbyCountText.loading = true;
        }

        private async void Start()
        {
            var usecase = new GetInMatchCountInLobbyUsecase(DIContainer.Instance.Resolve<ILobbyService>());
            var count = await usecase.ExecuteAsync(LobbyId.OpenLobbyId, destroyCancellationToken);
            _playingCountInGlobalLobbyCountText.text = count.ToString("000");
            _playingCountInGlobalLobbyCountText.loading = false;
        }
    }
}