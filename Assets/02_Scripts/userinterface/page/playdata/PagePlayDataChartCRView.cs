using System;
using System.Linq;
using AwesomeCharts;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.playdata;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.organizms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;
using UnityEngine.UI;
using static com.luxza.ui.components.atoms.Portrait;

namespace com.luxza.grandarts.userinterfaces.page.playdata
{
    public class PagePlayDataChartCRView : MonoBehaviour
    {
        [SerializeField] private Page _page;
        [SerializeField] private GranHeader _GranHeader;
        [SerializeField] private Image _img_RankLing;
        [SerializeField] private GranText _txt_RankCR;
        [SerializeField] private GranText _txt_RatingCR;
        [SerializeField] private LineChart _LineChartCR;

        [SerializeField] private GameObject[] _chartObjects;
        [SerializeField] private ChartDataItem[] _chartDataItems;
        [SerializeField] Material _goldMaterial;
        [SerializeField] Material _silverMaterial;
        [SerializeField] Material _bronzeMaterial;
        [SerializeField] Material _redMaterial;
        [SerializeField] Material _greenMaterial;
        [SerializeField] Material _blueMaterial;

        private PageChartViewParameter _param;
        private Player _currentPlayer;

        private void Awake()
        {
            _GranHeader.onClickLeftIcon1.Subscribe(async _ => await OnBackButtonClicked()).RegisterTo(destroyCancellationToken);
            for (var i = 0; i < _chartDataItems.Length; i++)
            {
                _chartDataItems[i].Init(i + 1);
            }
            foreach (var item in _chartObjects) item.SetActive(false);
        }

        private async void Start()
        {
            await UniTask.WaitUntil(() => ApplicationAuth.IsLoggedIn, cancellationToken: destroyCancellationToken);
            _param = _page.Meta.GetParameter<PageChartViewParameter>();
            if (_param == null)
            {
                _chartObjects[0].SetActive(true);
            }
            else
            {
                if (_param.showGameData == PageChartViewParameter.ShowGameData.CR_100Percent)
                {
                    _chartObjects[0].SetActive(true);
                }
                else if (_param.showGameData == PageChartViewParameter.ShowGameData.CR_80Percent)
                {
                    _chartObjects[1].SetActive(true);
                }

            }

            var getPlayDataUseCase = new GetLatest30StatsUsecase(DIContainer.Instance.Resolve<IPlayDataService>());
            var Latest30Stats = await getPlayDataUseCase.ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer.Id, destroyCancellationToken);

            ApplicationAuth.LoggedInUser.CurrentPlayer.Latest30Stats = Latest30Stats;
            _currentPlayer = ApplicationAuth.LoggedInUser.CurrentPlayer;

            _txt_RatingCR.text = "";

            //todo 100Percentを表示する仕組みしかないので80Percentも出せるようにする
            if (_param.showGameData == PageChartViewParameter.ShowGameData.CR_80Percent)
            {
                RefreshCR80PercentPlayData();
            }
            else if (_param.showGameData == PageChartViewParameter.ShowGameData.CR_100Percent)
            {
                RefreshCR100PercentPlayData();
            }
        }

        private void RefreshCR80PercentPlayData()
        {
            _txt_RatingCR.text = _currentPlayer.CR80Stats.Value.ToString("F2");
            _txt_RankCR.text = _currentPlayer.CR80Stats.Rank.ShortName();
            _txt_RankCR.color = _currentPlayer.CR80Stats.Rank.GetRankColor();

            _img_RankLing.fillAmount = _currentPlayer.CR80Stats.Rank.GetRankRingFillAmountFor80CR(_currentPlayer.CR80Stats.Value);
            SetBorderColor(_currentPlayer.CR80Stats.Rank.GetBorderColor());
            var history = ApplicationAuth.LoggedInUser.CurrentPlayer.Latest30Stats.Cr80StatsHistorys.ToArray();
            for (var i = 0; i < history.Length; i++)
            {
                _chartDataItems[i].Bind(i + 1, history[i]);
                _LineChartCR.GetChartData().DataSets[0].Entries.Add(new LineEntry(history.Length - i, history[i] < 0 ? float.NaN : history[i]));
            }

            _LineChartCR.SetDirty();
        }
        private void RefreshCR100PercentPlayData()
        {
            _txt_RatingCR.text = _currentPlayer.CR100Stats.Value.ToString("F2");
            _txt_RankCR.text = _currentPlayer.CR100Stats.Rank.ShortName();
            _txt_RankCR.color = _currentPlayer.CR100Stats.Rank.GetRankColor();
            _img_RankLing.fillAmount = _currentPlayer.CR100Stats.Rank.GetRankRingFillAmountFor100CR(_currentPlayer.CR100Stats.Value);
            SetBorderColor(_currentPlayer.CR100Stats.Rank.GetBorderColor());

            var history = ApplicationAuth.LoggedInUser.CurrentPlayer.Latest30Stats.Cr100StatsHistorys.ToArray();
            for (var i = 0; i < history.Length; i++)
            {
                _chartDataItems[i].Bind(i + 1, history[i]);
                _LineChartCR.GetChartData().DataSets[0].Entries.Add(new LineEntry(history.Length - i, history[i] < 0 ? float.NaN : history[i]));
            }

            _LineChartCR.SetDirty();
        }

        private void SetBorderColor(BorderColor color)
        {
            _img_RankLing.material = color switch
            {
                BorderColor.Gold => _goldMaterial,
                BorderColor.Silver => _silverMaterial,
                BorderColor.Bronze => _bronzeMaterial,
                BorderColor.Red => _redMaterial,
                BorderColor.Green => _greenMaterial,
                BorderColor.Blue => _blueMaterial,
                _ => throw new NotSupportedException($"{color} is not supported")
            };
        }

        private async UniTask OnBackButtonClicked()
        {
            await PageManager.Instance.Back();
        }

    }
}