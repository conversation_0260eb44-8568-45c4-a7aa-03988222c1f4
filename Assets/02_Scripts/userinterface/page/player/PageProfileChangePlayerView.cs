using R3;
using System.Linq;
using UnityEngine;
using com.luxza.granlog;
using Cysharp.Threading.Tasks;
using com.luxza.ui.page;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.usecases.user;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using com.luxza.ui.components.molecules;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.components.atoms;

namespace com.luxza.grandarts.userinterfaces.page.player
{
    public class PageProfileChangePlayerView : MonoBehaviour
    {
        [SerializeField] private GranButton _btn_ChangePlayer;
        [SerializeField] private GranButton _btn_Cancel;
        [SerializeField] private GranButton _btn_OK;
        [SerializeField] private SwitchPlayerSlot[] _changePlayerIcons;

        [SerializeField] private Page _page;

        private bool _firstSelectOnAppLaunch = false;
        IPlayer _selectPlayer;
        void Start()
        {
            if(_page.Meta.TryGetParameter<PageChangePlayerParameter>(out var param)) {
                _firstSelectOnAppLaunch = param._firstSelectOnAppLaunch;
            }

            if(_firstSelectOnAppLaunch) {
                _btn_Cancel.gameObject.SetActive(false);
                _btn_ChangePlayer.gameObject.SetActive(false);

                _btn_OK.gameObject.SetActive(true);
                _btn_OK.AddOnClickActionWithLoading(OnClickChangePlayer, destroyCancellationToken, new[] {
                    _btn_Cancel,
                    _btn_ChangePlayer
                });
            } else {
                _btn_OK.gameObject.SetActive(false);

                _btn_Cancel.gameObject.SetActive(true);
                _btn_ChangePlayer.gameObject.SetActive(true);
                _btn_ChangePlayer.AddOnClickActionWithLoading(OnClickChangePlayer, destroyCancellationToken, new[] {
                    _btn_Cancel,
                    _btn_OK
                });
                _btn_Cancel.onClickAsObservable.Subscribe(async _ => await OnClickCancel()).RegisterTo(destroyCancellationToken);
            }

            foreach (var item in _changePlayerIcons)
            {
                item.Init();
                item.onClickAddPlayer.Subscribe(OnClickAddPlayer).RegisterTo(destroyCancellationToken);
            }

            //TODO: Supports soft tip players.
            var players = ApplicationAuth.LoggedInUser.OnlySteelPlayers.ToArray();
            for (int i = 0; i < players.Length; i++)
            {
                _changePlayerIcons[i].Bind(players[i]);
                _changePlayerIcons[i].onClickSelect.Subscribe(OnClickPlayerIcon).RegisterTo(destroyCancellationToken);
            }
        }

        private void OnClickPlayerIcon(IPlayer player)
        {
            _selectPlayer = player;
             foreach (var item in _changePlayerIcons)
            {
                if (!item.IsBinded) continue;
                if (_selectPlayer.Id != item.BindPlayer.Id)
                    item.ResetSelectFlame();
            }
        }

        private async void OnClickAddPlayer(Unit _)
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.PlayerCreation, new PagePlayerCreationParameter(enableCloseButton: true)));
            await PageManager.Instance.CloseModalAsync(_page);
        }

        private async UniTask OnClickChangePlayer()
        {
            if (_selectPlayer == null) return;
            var usecase = new SwitchPlayerUsecase(
                DIContainer.Instance.Resolve<IUserCacheRepository>(),
                DIContainer.Instance.Resolve<IInstantPlayerRepository>(),
                DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
            await usecase.SwitchAsync(_selectPlayer.Id,destroyCancellationToken);

            await PageManager.Instance.CloseModalAsync(_page);
            if(_firstSelectOnAppLaunch) {
                await PageManager.Instance.ReplaceAsync(new PageMeta(PageNames.Home));
            }
        }

        private async UniTask OnClickCancel()
        {
            await PageManager.Instance.CloseModalAsync(_page);
        }
    }
}