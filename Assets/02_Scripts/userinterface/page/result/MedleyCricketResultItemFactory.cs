using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandarts.domains.game.result.standardcr;

namespace com.luxza.grandarts.userinterfaces.page.result {
    public class MedleyCricketResultItemFactory : IResultItemFactory
    {
        public List<(string title, string value)> Create(GameResultByUnit unit)
        {
            return CreateCricketResultItems(unit);
        }

        private List<(string title, string value)> CreateCricketResultItems(GameResultByUnit unit) {
            var memberAnalysis = unit.GameResultByMembers.Select(m => {
                if (m is StandardCrGameResultByPlayer player) return player.analysisData;
                throw new ArgumentException("Invalid game result type.");
            });
            var unitAnalysis = StandardCrAnalysisDataAggregator.Aggregate(memberAnalysis);
            List<(string title, string value)> data = new();
            data.Add(("MPR", unitAnalysis.Stats100.ToString("F2")));
            return data;
        }
    }
}