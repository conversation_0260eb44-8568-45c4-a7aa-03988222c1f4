using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.granlog;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;
namespace com.luxza.grandarts.userinterfaces.page.home
{
    public class PageHomeView : MonoBehaviour
    {
        [SerializeField] private GranButton _Btn_Online;
        [SerializeField] private GranButton _Btn_PlayData;
        [SerializeField] private GranButton _Btn_LocalGame;
        [SerializeField] private GranButton _Btn_GranEyeSetting;
        [SerializeField] private GranButton _Btn_Friend;

        private void Awake()
        {
            _Btn_LocalGame.onClickAsObservable.Subscribe(async _ => await OnLocalGameButtonClicked()).RegisterTo(destroyCancellationToken);
            _Btn_Online.onClickAsObservable.Subscribe(async _ => await OnOnlineButtonClicked()).RegisterTo(destroyCancellationToken);
            _Btn_PlayData.onClickAsObservable.Subscribe(async _ => await OnPlayDataButtonClicked()).RegisterTo(destroyCancellationToken);
            _Btn_GranEyeSetting.onClickAsObservable.Subscribe(async _ => await OnGranEyeSettingButtonClicked()).RegisterTo(destroyCancellationToken);
            _Btn_Friend.onClickAsObservable.Subscribe(async _ => await OnFriendButtonClicked()).RegisterTo(destroyCancellationToken);
        }

        private async UniTask OnOnlineButtonClicked()
        {
            Log.d("Online button clicked");
            await PageManager.Instance.ReplaceAsync(new PageMeta(PageNames.Online), withAnimation: false);
        }

        private async UniTask OnLocalGameButtonClicked()
        {
            Log.d("Local game button clicked");
            await PageManager.Instance.ReplaceAsync(new PageMeta(PageNames.LocalPlay), withAnimation: false);
        }

        private async UniTask OnGranEyeSettingButtonClicked()
        {
            Log.d("GanEye setting button clicked");
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeSettingTop));
        }

        private async UniTask OnFriendButtonClicked()
        {
            Log.d("Menu button clicked");
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.Friend));
        }

        private async UniTask OnPlayDataButtonClicked()
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.PlayDataTop));
        }

    }
}