using System;
using System.Linq;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.online.room.notification;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.exceptions;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.usecases.online.lobby.room;
using com.luxza.grandarts.usecases.permission;
using com.luxza.grandarts.usecases.permission.interfaces;
using com.luxza.grandarts.usecases.playdata;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.userinterfaces.components.game.play;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.page.lobby
{
    public class MatchRoomSettingBannerViewController : MonoBehaviour
    {
        [SerializeField] private GranText _legsFormatText;
        [SerializeField] private GranText _optionsText;
        [SerializeField] private GranButton _editButton;
        [SerializeField] private GranSwitchToggle _enterLobbyToggle;

        [SerializeField] private PlayerIcon[] _playerIcons;

        [SerializeField] private PageGlobalLobbyViewController _pageGrobalLobbyViewController;

        private void Awake()
        {
            _editButton.onClickAsObservable.Subscribe(OnClickEdit).RegisterTo(destroyCancellationToken);
            _enterLobbyToggle.onValueChangedObservable.Subscribe(OnEnterLobbyToggle).RegisterTo(destroyCancellationToken);
            _legsFormatText.loading = true;
            _optionsText.loading = true;
            _enterLobbyToggle.interactable = false;
            foreach (var icon in _playerIcons)
            {
                icon.loading = true;
            }

            PlayerPresence.OnUpdateMyMatchRoomId.Subscribe(v =>
            {
                if (_enterLobbyToggle != null)
                {
                    _enterLobbyToggle.SetIsOn(PlayerPresence.HasMyRoom, true, false);
                }
            }).RegisterTo(destroyCancellationToken);
        }


        private async void OnEnterLobbyToggle(bool v)
        {
            _enterLobbyToggle.interactable = false;
            _editButton.interactable = false;
            var updateusecase = new GetPlayerStatsAndUpdateUsecase(DIContainer.Instance.Resolve<IPlayerService>());
            await updateusecase.ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer, destroyCancellationToken);
            var roomId = await DIContainer.Instance.Resolve<ILobbyService>().PublishNewMatchRoomIdAsync(_pageGrobalLobbyViewController.LobbyId, destroyCancellationToken);
            if (v)
            {
                _pageGrobalLobbyViewController.CreateMyRoomWithCreatingStatus(roomId);
            }
            else
            {
                _pageGrobalLobbyViewController.RequestDeleteMyRoom();
            }
            try
            {
                if (v)
                {
                    var usecase = new CreateMyRoomUsecase(
                        DIContainer.Instance.Resolve<ILobbyService>(),
                        DIContainer.Instance.Resolve<IOnlineRequestFormatRepository>(),
                        DIContainer.Instance.Resolve<IRoomNotificationCeterFactory>(),
                        DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
                    await usecase.ExecuteAsync(
                        roomId,
                        _pageGrobalLobbyViewController.LobbyId,
                        destroyCancellationToken);
                }
                else
                {
                    var usecase = new RemoveMyRoomUsecase(
                        DIContainer.Instance.Resolve<ILobbyService>(),
                        DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
                    await usecase.ExecuteAsync(_pageGrobalLobbyViewController.LobbyId, destroyCancellationToken);
                }
            }
            catch (PermissionRequiredException e)
            {
                if (e.DeniedPermission == AppPermission.Camera)
                {
                    MessageUtility.ShowMessageModal(
                        message: LocalizeString.GetLocalizedString(LocalizeKey.Systems.CameraPermissionRequired),
                        iconType: GranModal.IconType.Error,
                        positive: (
                            label: LocalizeString.GetLocalizedString(LocalizeKey.Labels.OpenAppSettings),
                            action: OpenAppSettings
                        ),
                        negative: (
                            label: LocalizeString.GetLocalizedString(LocalizeKey.Labels.Cancel),
                            action: () => UniTask.CompletedTask
                        )
                    );
                }
                else if (e.DeniedPermission == AppPermission.Mic)
                {
                    MessageUtility.ShowMessageModal(
                        message: LocalizeString.GetLocalizedString(LocalizeKey.Systems.MicPermissionRequired),
                        iconType: GranModal.IconType.Error,
                        positive: (
                            label: LocalizeString.GetLocalizedString(LocalizeKey.Labels.OpenAppSettings),
                            action: OpenAppSettings
                        ),
                        negative: (
                            label: LocalizeString.GetLocalizedString(LocalizeKey.Labels.Cancel),
                            action: () => UniTask.CompletedTask
                        )
                    );
                }

                UniTask OpenAppSettings()
                {
                    PermissionServiceProvider.GetPermissionService().OpenAppSettings();
                    return UniTask.CompletedTask;
                }

                if (v)
                {
                    _pageGrobalLobbyViewController.CancelCreation();
                }
                else
                {
                    _pageGrobalLobbyViewController.CancelDeletion();
                }
            }
            catch (AlreadyExist)
            {
                MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Systems.AlreadyInTheRoomException));
                if (v)
                {
                    _pageGrobalLobbyViewController.CancelCreation();
                }
                else
                {
                    _pageGrobalLobbyViewController.CancelDeletion();
                }
            }
            catch (Exception)
            {
                if (v)
                {
                    _pageGrobalLobbyViewController.CancelCreation();
                }
                else
                {
                    _pageGrobalLobbyViewController.CancelDeletion();
                }
                throw;
            }
            finally
            {
                if (_enterLobbyToggle != null)
                {
                    _enterLobbyToggle.interactable = true;
                    _enterLobbyToggle.SetIsOn(PlayerPresence.HasMyRoom, true, false);
                }
                if (_editButton != null) _editButton.interactable = true;
            }
        }

        private async void Start()
        {
            await UniTask.WaitUntil(() => ApplicationAuth.IsLoggedIn);
            var usecase = new GetOnlinePlayFormatUsecase(DIContainer.Instance.Resolve<IOnlineRequestFormatRepository>());
            var requestFormat = await usecase.ExecuteAsync(ApplicationAuth.LoggedInUser.CurrentPlayer, destroyCancellationToken);
            _legsFormatText.text = GameFormatUtility.GameFormatText(requestFormat);
            _optionsText.text = GameFormatUtility.GameOptionsText(requestFormat);
            _legsFormatText.loading = false;
            _optionsText.loading = false;
            _editButton.interactable = true;
            _enterLobbyToggle.interactable = true;
            _enterLobbyToggle.SetIsOn(PlayerPresence.HasMyRoom, false, false);
            var enteredPlayers = requestFormat.EntrySlots.EnteredPlayers.ToArray();
            for (int i = 0; i < enteredPlayers.Length; i++)
            {
                if (i >= 2) throw new SystemException("Online does not support more than Trios.");
                _playerIcons[i].Bind(enteredPlayers[i]);
                _playerIcons[i].loading = false;
                _playerIcons[i].gameObject.SetActive(true);
            }

            _enterLobbyToggle.SetIsOnWithoutAnimation(true);
        }

        private async void OnClickEdit(Unit _)
        {
            if (PlayerPresence.HasMyRoom)
            {
                MessageUtility.ShowMessageModal("If you want to change the settings, delete the room first.");
                return;
            }
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.OnlineGameSetting));
        }
    }
}