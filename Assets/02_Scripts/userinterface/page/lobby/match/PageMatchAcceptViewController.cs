
using System;
using System.Globalization;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.online.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.userinterfaces.online.game.x01;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandartslogic.domain.game;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.page.lobby.match
{
    public class PageMatchAcceptViewController : MonoBehaviour
    {
        [SerializeField] private Page _page;
        [SerializeField] private SinglesUnitProfileViewController _singlesUnitProfileViewController;
        [SerializeField] private DoublesUnitProfileViewController _doublesUnitProfileViewController;
        [SerializeField] private AudioSource _ReceiveMatchSE;

        [SerializeField] private GranButton _Btn_Accept;
        [SerializeField] private GranButton _Btn_Cancel;

        private OnlinePlayUnit _opponent;
        private MatchRoom _room;

        void Awake()
        {
            _Btn_Accept.onClickAsObservable.Subscribe(OnClickAccept).RegisterTo(destroyCancellationToken);
            _Btn_Cancel.onClickAsObservable.Subscribe(OnClickCancel).RegisterTo(destroyCancellationToken);
            _singlesUnitProfileViewController.loading = true;
            _doublesUnitProfileViewController.loading = true;
            _singlesUnitProfileViewController.activate = true;
            _doublesUnitProfileViewController.activate = false;
            _page.OnActivate.Subscribe(OnActivated).RegisterTo(destroyCancellationToken);
        }

        private void OnActivated(Unit _)
        {
            _Btn_Accept.interactable = false;
            _Btn_Cancel.interactable = false;
            if (_page.Meta.TryGetParameter<PageMatchAcceptParameter>(out var parameter))
            {
                Bind(parameter.MatchRoom);
                _singlesUnitProfileViewController.loading = false;
                _doublesUnitProfileViewController.loading = false;
                _ReceiveMatchSE.Play();
            }
            else
            {
                throw new ArgumentException("PageMatchRequestParameter is required.");
            }
        }

        void Start()
        {
#if Mock
            if (_page.Meta == null)
            {
                //Create a dummy paramter.
                UniTask.Void(async () =>
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(1));
                    await UniTask.WaitUntil(() => ApplicationAuth.IsLoggedIn);
                    var format = MedleyRequestFormat.Default(BoardSize.Steel);
                    format.SetLegs(new Legs(7), new GameCode?[7] {
                        GameCode._501,
                        GameCode._StandardCR,
                        GameCode._501,
                        GameCode._StandardCR,
                        GameCode._501,
                        GameCode._StandardCR,
                        GameCode._501,
                    });
                    var owner = new ReadonlyPlayer(
                        new PlayerId(1),
                        new GranId("Gran1"),
                        "Owner1",
                        "",
                        BoardSize.Steel,
                        RegionInfo.CurrentRegion);
                    var matchRoom = new MatchRoom(
                        new MatchRoomId("DummyRoom"),
                        LobbyId.OpenLobbyId,
                        new OnlinePlayUnit(new[]{
                            new PlayUnitMember(
                                owner,
                                DeviceId.MyDeviceId)
                        }),
                        format,
                        owner.Id
                    );
                    matchRoom.Join(new OnlinePlayUnit(new[]{
                        new PlayUnitMember(
                                new ReadonlyPlayer(
                                    new PlayerId(2),
                                    new GranId("Gran2"),
                                    "Participate2",
                                    "",
                                    BoardSize.Steel,
                                    RegionInfo.CurrentRegion),
                                DeviceId.MyDeviceId)
                        }),
                        new[] {
                            ("dummy-server", 30),
                        }
                    );
                    Bind(matchRoom);
                    _singlesUnitProfileViewController.loading = false;
                    _doublesUnitProfileViewController.loading = false;
                });
                return;
            }
#endif
        }

        private void Bind(MatchRoom room)
        {
            //For now, we only support 2 units for a online match.
            _room = room;
            OnlinePlayUnit opponent = null;
            foreach (var unit in room.Participants)
            {
                if (room.Owner.Id != unit.Id)
                {
                    opponent = unit;
                }
            }

            if (opponent == null)
            {
                throw new ArgumentException("Opponent is not found.");
            }

            _opponent = opponent;

            if (opponent.IsSingleUnit)
            {
                _singlesUnitProfileViewController.Bind(opponent, room.RequestFormat);
            }
            else if (opponent.IsDoublesUnit)
            {
                _doublesUnitProfileViewController.Bind(opponent, room.RequestFormat);
            }

            _Btn_Accept.interactable = true;
            _Btn_Cancel.interactable = true;
        }

        private async void OnClickCancel(Unit _)
        {
            if (_opponent == null) return;
            if (_room == null) return;
            _Btn_Accept.loading = true;
            _Btn_Cancel.loading = true;
            try
            {
                var usecase = new RefuseJoinRoomRequestUsecase(DIContainer.Instance.Resolve<ILobbyService>());
                await usecase.ExecuteAsync(_opponent, _room, destroyCancellationToken);
                await PageManager.Instance.CloseModalAsync(_page);
            }
            finally
            {
                if (_Btn_Accept != null) _Btn_Accept.loading = false;
                if (_Btn_Cancel != null) _Btn_Cancel.loading = false;
            }

        }

        private async void OnClickAccept(Unit _)
        {
#if Mock
            throw new NotSupportedException("OnlineMatch not supported on Mock.");
#endif
            if (_opponent == null) return;
            if (_room == null) return;
            _Btn_Accept.loading = true;
            _Btn_Cancel.loading = true;
            try
            {
                var usecase = new AcceptJoinRoomRequestUsecase(
                    DIContainer.Instance.Resolve<ILobbyService>(),
                    DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
                var room = await usecase.ExecuteAsync(_opponent, _room, destroyCancellationToken);
                await PageManager.Instance.CloseModalAsync(_page);
                var gameForFirstLeg = room.RequestFormat.GetGameCode(1, 1);
                switch (gameForFirstLeg)
                {
                    case GameCode._301:
                    case GameCode._501:
                    case GameCode._701:
                        await PageManager.Instance.OpenAsync(new PageMeta(
                            PageNames.OnlineMatchx01,
                            new BasicOnlineGamePageParameter(room)));
                        break;
                    case GameCode._StandardCR:
                        await PageManager.Instance.OpenAsync(new PageMeta(
                            PageNames.OnlineMatchCricket,
                            new BasicOnlineGamePageParameter(room)));
                        break;
                    default:
                        throw new NotSupportedException($"GameCode {gameForFirstLeg} is not supported.");
                }
            }
            finally
            {
                if (_Btn_Accept != null) _Btn_Accept.loading = false;
                if (_Btn_Cancel != null) _Btn_Cancel.loading = false;
            }
        }
    }
}