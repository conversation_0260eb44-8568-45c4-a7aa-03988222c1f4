using System.Threading;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.user;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.resetpassword
{
    public class ResetPasswordRequestUseCase
    {
        private readonly IAuthService _authServiceClient;
        private readonly IUserCacheRepository _userCacheRepository;
        public ResetPasswordRequestUseCase(IAuthService authServiceClient, IUserCacheRepository userCacheRepository)
        {
            _authServiceClient = authServiceClient;
            _userCacheRepository = userCacheRepository;
        }

        public async UniTask<bool> ExecuteAsync(EmailAddress email,VerifyCode code, string newPassword, CancellationToken cancellationToken)
        {
            var isSuccess = await _authServiceClient.SendPasswordResetRequest(email, code, newPassword, cancellationToken);
            if (isSuccess)
            {
                if (ApplicationAuth.IsLoggedIn)
                {
                    ApplicationAuth.LoggedInUser.Password = new UserPassword(newPassword);
                    if (await _userCacheRepository.TryGetUserPasswordAsync(out var password, cancellationToken))
                    {
                        await _userCacheRepository.SavePasswordAsync(new UserPassword(newPassword), cancellationToken);
                    }
                }
            }

            return isSuccess;
        }

    }
}
