using System;
using System.Threading;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.utils;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.online.lobby.room
{
    public class RemoveMyRoomUsecase
    {
        private readonly ILobbyService _lobbyService;
        private readonly IPlayerPresenceRepository _playerPresenceRepository;

        public RemoveMyRoomUsecase(ILobbyService lobbyService, IPlayerPresenceRepository playerPresenceRepository)
        {
            _lobbyService = lobbyService;
            _playerPresenceRepository = playerPresenceRepository;
        }

        public async UniTask ExecuteAsync(LobbyId lobbyId, CancellationToken cancellationToken)
        {
            if (PlayerPresence.MyMatchRoomId == null) return;
            await _lobbyService.LeaveFromLobby(lobbyId, PlayerPresence.MyMatchRoomId, cancellationToken);
            PlayerPresence.ClearMyRoom();
            PlayerPresence.Status = PlayerStatus.Online;
            _playerPresenceRepository.SaveAsync().SafeForget();
        }
    }
}