using System.IO;
using System.Threading;
using com.luxza.grandarts.usecases.image;
using com.luxza.granlog;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.tests.usecases.image
{
    public class LoadImageThenSaveUseCase
    {
        private IImageRepository _fileRepository;
        private IImageRepository _webImageRepository;
        public LoadImageThenSaveUseCase(IImageRepository fileRepository,IImageRepository webImageRepository) {
            _fileRepository = fileRepository;
            _webImageRepository = webImageRepository;
        }
        public async UniTask<Texture2D> LoadThenSaveAsync(string imageUrl, CancellationToken cancellationToken)
        {
            try
            {
                var icon = await _fileRepository.LoadAsync(imageUrl, cancellationToken);
                return icon;
            }
            catch (FileNotFoundException)
            {
                Log.w("File not found.");
                var tex = await _webImageRepository.LoadAsync(imageUrl, cancellationToken);
                var _ = _fileRepository.SaveAsync(tex, imageUrl, false, cancellationToken);
                return tex;
            }
        }
    }
}
