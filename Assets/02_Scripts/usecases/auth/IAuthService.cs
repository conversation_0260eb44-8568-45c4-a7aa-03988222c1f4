using System.Threading;
using com.luxza.grandarts.domains.user;
using Cysharp.Threading.Tasks;
using com.luxza.grandarts.domains.exceptions;
using System.Globalization;
using com.luxza.grandarts.domains.language;

namespace com.luxza.grandarts.usecases.auth
{
    public interface IAuthService
    {
        /// <summary>
        /// Sign up with email and password
        /// </summary>
        /// <param name="email"></param>
        /// <param name="password"></param>
        /// <param name="country"></param>
        /// <param name="language"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        UniTask<User> SignUpAsync(EmailAddress email, UserPassword password, RegionInfo country, Language language, CancellationToken cancellationToken);

        /// <summary>
        /// Login with email and password
        /// </summary>
        /// <param name="email">email</param>
        /// <param name="password">password</param>
        /// <param name="cancellationToken">cancellation token</param>
        /// <returns>User logged in</returns>
        /// <exception cref="UserNotFoundException">Thrown when user not found with this email.</exception>
        /// <exception cref="IncorrectPasswordException">Thrown when password is invalid.</exception>
        UniTask<User> LoginAsync(EmailAddress email, UserPassword password, CancellationToken cancellationToken);

        UniTask<User> AutoLoginAsync(CancellationToken cancellationToken);
        UniTask LogoutAsync(UserId userId, CancellationToken cancellationToken);

        /// <summary>
        /// Verify email with verify code.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="verifyCode"></param>
        /// <param name="cancellationToken"></param>
        /// <returns>If verify code is valid, this method finished with no returns. If vertify code is invalid, this method raise exceptions.</returns>
        /// <exception cref="InvalidVerifyCode">Thrown when verify code is invalid.</exception>
        /// <exception cref="VerifyCodeExpired">Thrown when verify code is expired.</exception>
        UniTask VerifyEmailAsync(EmailAddress email, string verifyCode, CancellationToken cancellationToken);

        /// <summary>
        /// Send verify code to email.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="cancellationToken"></param>
        /// <exception cref="LimitExceededException">Thrown when limit of sending verify code is exceeded.</exception>
        /// <returns></returns>
        UniTask SendVerifyCodeRequest(EmailAddress email, CancellationToken cancellationToken);

        /// <summary>
        /// Send forgot password verify code request
        /// </summary>
        /// <param name="email"></param>
        /// <param name="cancellationToken"></param>
        /// <exception cref="LimitExceededException">Thrown when limit of sending verify code is exceeded.</exception>
        /// <returns></returns>
        UniTask<bool> SendForgotPasswordVerifyCodeRequest(EmailAddress email, CancellationToken cancellationToken);

        /// <summary>
        /// Send forgot password reset password request.
        /// </summary>
        /// <param name="email"></param>
        /// <param name="code"></param>
        /// <param name="newPassword"></param>
        /// <param name="cancellationToken"></param>
        /// <exception cref="LimitExceededException">Thrown when limit of sending verify code is exceeded.</exception>
        /// <returns></returns>
        UniTask<bool> SendPasswordResetRequest(EmailAddress email,VerifyCode code,string newPassword, CancellationToken cancellationToken);
    }
}