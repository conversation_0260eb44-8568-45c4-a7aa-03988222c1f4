using System;
using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.usecases.user;
using com.luxza.grandarts.utils;
using Cysharp.Threading.Tasks;
using com.luxza.grandarts.domains.online.videochat;

namespace com.luxza.grandarts.usecases.auth {
    public class LogoutUsecase {
        private readonly IUserCacheRepository _repository;
        private readonly IInstantPlayerRepository _instantPlayerRepository;
        private readonly ILocalGameRequestFormatRepository _localGameRequestFormatRepository;
        private readonly IPlayerPresenceRepository _playerPresenceRepository;
        private readonly IVideoSettingRepository _videoSettingRepository;

        public LogoutUsecase(
            IVideoSettingRepository videoSettingRepository,
            IUserCacheRepository repository,
            IInstantPlayerRepository instantPlayerRepository,
            ILocalGameRequestFormatRepository localGameRequestFormatRepository,
            IPlayerPresenceRepository playerPresenceRepository)
        {
            _repository = repository;
            _videoSettingRepository = videoSettingRepository;
            _instantPlayerRepository = instantPlayerRepository;
            _localGameRequestFormatRepository = localGameRequestFormatRepository;
            _playerPresenceRepository = playerPresenceRepository;
        }

        public async UniTask ExecuteAsync(CancellationToken cancellationToken) {
            await _repository.ClearAllAsync(cancellationToken);
            await _videoSettingRepository.ClearAllAsync(cancellationToken);
            await _instantPlayerRepository.ClearAllAsync(cancellationToken);
            await _localGameRequestFormatRepository.ClearAllSettingsAsync(ApplicationAuth.LoggedInUser.CurrentPlayer.Id, cancellationToken);
            PlayerPresence.Status = PlayerStatus.Offline;
            PlayerPresence.ClearMyRoom();
            PlayerPresence.ClearJoinedRoom();
            _playerPresenceRepository.SaveAsync().SafeForget();
            PlayerPresence.Id = null;
            LobbyInMemoryCacheService.ClearAll();
            ApplicationAuth.LoggedInUser = null;
        }
    }
}