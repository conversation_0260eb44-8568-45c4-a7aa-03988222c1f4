#if UNITY_EDITOR
using com.luxza.grandarts.usecases.permission.editor;
#elif UNITY_ANDROID
using com.luxza.grandarts.usecases.permission.android;
#elif UNITY_IOS
using com.luxza.grandarts.usecases.permission.iOS;
#endif
using com.luxza.grandarts.usecases.permission.interfaces;

namespace com.luxza.grandarts.usecases.permission
{
    public static class PermissionServiceProvider
    {
        public static IPermissionService GetPermissionService()
        {
#if UNITY_EDITOR
            return new EditorPermissionService();
#elif UNITY_ANDROID
            return new AndroidPermissionService();
#elif UNITY_IOS
            return new iOSPermissionService();
#endif
        }
    }
}