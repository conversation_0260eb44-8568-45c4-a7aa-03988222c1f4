using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.player
{
    public class UnblockFollowUsecase
    {
        private IPlayerService _playerService;
        private PlayerId _ownPlayerId;
        private PlayerId _targetPlayerId;
        
        public UnblockFollowUsecase(IPlayerService playerService,PlayerId ownPlayerId, PlayerId targetPlayerId)
        {
            _playerService = playerService;
            _ownPlayerId = ownPlayerId;
            _targetPlayerId = targetPlayerId;
        }

        public async UniTask<bool> ExecuteAsync(CancellationToken cancellationToken) {
            if (!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException();
            }
            return await _playerService.UnblockFollowAsync(_ownPlayerId, _targetPlayerId, cancellationToken);
        }
    }
}