using System;
using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.domains.exceptions;
using Cysharp.Threading.Tasks;
using UnityEngine;
using com.luxza.grandarts.usecases.auth;
using System.Linq;
using System.Collections.Generic;

namespace com.luxza.grandarts.usecases.player
{
    public class AddPlayerUsecase
    {
        private IPlayerRepository _repository;

        public AddPlayerUsecase(IPlayerRepository repository)
        {
            _repository = repository;
        }

        /// <summary>
        /// Add a player to the user.
        /// </summary>
        /// <param name="userId">UserId of user you want to add player</param>
        /// <param name="granId">GranId you want to add</param>
        /// <param name="name">Name of player you want to add</param>
        /// <param name="boardSize">BoardSize of player you want to add</param>
        /// <param name="icon">Icon of Player you want to add</param>
        /// <param name="cancellationToken"></param>
        /// <returns>Player has been added.</returns>
        /// <exception cref="InvalidOperationException">Thrown when called with userId does not matched logged-in userId</exception>
        /// <exception cref="SessionTimeOutException">Thrown when called with no login</exception>
        /// <exception cref="UserNotFoundException">Thrown when the user is not found</exception>
        /// <exception cref="GranIdWasDuplicatedException">Thrown when the player already exists</exception>
        public async UniTask<Player> ExecuteAsync(UserId userId, GranId granId, string name, BoardSize boardSize, Texture2D icon, CancellationToken cancellationToken)
        {
            var loggedInUser = ApplicationAuth.LoggedInUser;

            if (loggedInUser is null)
            {
                throw new SessionTimeOutException("Logged in user not found");
            }

            if (loggedInUser.Id != userId)
            {
                throw new InvalidOperationException($"UserId:{userId} doesn't match");
            }

            IEnumerable<IPlayer> players = loggedInUser.Players;
            if (players.Any(p => p.GranId == granId))
            {
                throw new GranIdWasDuplicatedException("The player with a same GranID already exists");
            }

            var player = await _repository.CreatePlayerAsync(userId, granId, name, boardSize, icon, cancellationToken);
            loggedInUser.AddPlayer(player);
            return player;
        }
    }
}