using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.utils.dataStructure;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.contact
{
    public interface ISendInquiryServiceAPIClient
    {
        UniTask SendInquiryAsync(UserId userId, PlayerId playerId, string inquiryCatgory, string inquiryDetail, CancellationToken cancellationToken);
    }
}