using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.user;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.user
{
    public interface IUserCacheRepository
    {
        UniTask<UserId> GetUserIdAsync(CancellationToken cancellationToken);
        UniTask<bool> TryGetUserIdAsync(out UserId id, CancellationToken cancellationToken);
        UniTask SaveUseIdAsync(UserId id, CancellationToken cancellationToken);
        UniTask<UserPassword> GetUserPasswordAsync(CancellationToken cancellationToken);
        UniTask<bool> TryGetUserPasswordAsync(out UserPassword password, CancellationToken cancellationToken);
        UniTask SavePasswordAsync(UserPassword password, CancellationToken cancellationToken);
        UniTask SaveEmailAsync(EmailAddress email, CancellationToken cancellationToken);
        UniTask<EmailAddress> GetEmailAsync(CancellationToken cancellationToken);
        UniTask<bool> TryGetEmailAsync(out EmailAddress email, CancellationToken cancellationToken);
        UniTask<PlayerId> GetMainPlayerIdAsync(CancellationToken cancellationToken);
        UniTask<bool> TryGetMainPlayerIdAsync(out PlayerId playerId, CancellationToken cancellationToken);
        UniTask SaveMainPlayerIdAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask ClearMainPlayerIdAsync(CancellationToken cancellationToken);
        UniTask ClearAllAsync(CancellationToken cancellationToken);
    }
}