using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game.entry;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.player;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.game.setting
{
    public class GetMultipleCRPlayFormatUsecase
    {
        private readonly ILocalGameRequestFormatRepository _repository;

        public GetMultipleCRPlayFormatUsecase(ILocalGameRequestFormatRepository apiClient) {
            _repository = apiClient;
        }

        public async UniTask<MultipleCRPlayFormat> ExecuteAsync(IPlayer player, CancellationToken cancellationToken) {
            try {
                var requestFormat  = await _repository.GetMultipleCRRequestFormatAsync(player.Id, cancellationToken);
                return new MultipleCRPlayFormat(new EntrySlots(), requestFormat);
            } catch (NotFound) {
                return new MultipleCRPlayFormat(player.BoardSize);
            }
        }
    }
}