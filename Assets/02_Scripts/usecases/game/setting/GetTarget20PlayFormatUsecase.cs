using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game.entry;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.player;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.game.setting
{
    public class GetTarget20PlayFormatUsecase
    {
        private readonly ILocalGameRequestFormatRepository _repository;

        public GetTarget20PlayFormatUsecase(ILocalGameRequestFormatRepository apiClient) {
            _repository = apiClient;
        }

        public async UniTask<Target20PlayFormat> ExecuteAsync(IPlayer player, CancellationToken cancellationToken) {
            try {
                var requestFormat  = await _repository.GetTarget20RequestFormatAsync(player.Id, cancellationToken);
                return new Target20PlayFormat(new EntrySlots(), requestFormat);
            } catch (NotFound) {
                return new Target20PlayFormat(player.BoardSize);
            }

        }
    }
}