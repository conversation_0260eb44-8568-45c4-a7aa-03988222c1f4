#if UNITY_IOS
using System.IO;
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.iOS.Xcode;
using UnityEngine;

namespace com.luxza.grandarts.build.ios {
    public class IOSPostBuildProcess {
        [PostProcessBuild]
        public static void OnPostprocessBuild(BuildTarget target, string pathToBuildProject)
        {
            if (target == BuildTarget.iOS)
            {
                // Info.plistのパスを取得
                string plistPath = Path.Combine(pathToBuildProject, "Info.plist");

                // Plistファイルをロード
                PlistDocument plist = new PlistDocument();
                plist.ReadFromFile(plistPath);

                // 権限の説明を追加
                PlistElementDict rootDict = plist.root;

                // "NSPhotoLibraryUsageDescription"キーを追加
                string photoLibraryUsageDescription = "This app requires access to your photo library to select images.";
                rootDict.SetString("NSPhotoLibraryUsageDescription", photoLibraryUsageDescription);

                // 編集後のPlistファイルを保存
                plist.WriteToFile(plistPath);

                //Delete GoogleService-Info.plist on root for firebase initialization to clean up
                string destinationPath = "Assets/GoogleService-Info.plist";

                if (File.Exists(destinationPath))
                {
                    File.Delete(destinationPath);
                    Debug.Log($"Deleted {destinationPath}");
                }
            }
        }
    }
}
#endif