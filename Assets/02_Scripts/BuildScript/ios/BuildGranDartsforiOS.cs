#if UNITY_EDITOR && UNITY_IOS
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using com.luxza.granlog;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
using UnityEditor.Build;
using UnityEditor.Build.Reporting;
using UnityEditor.iOS.Xcode;
using UnityEditor.Localization;
using UnityEditor.Localization.Plugins.Google;
using UnityEngine;

public class BuildGranDartsforiOS
{
    private static string ProductionSetting => "Production";
    private static string DevelopmentSetting => "Development";
    private static string MockSetting => "Mock";

    private static string DevAddressableGroup => "Dev";

    private static string PseudoBoardSetting => "PseudoBoard";

    private static string LOGLEVEL_DEBUG = "LOG_DEBUG";
    private static string LOGLEVEL_INFO = "LOG_INFO";
    private static string LOGLEVEL_WARN = "LOG_WARN";
    private static string LOGLEVEL_ERROR = "LOG_ERROR";
    private static string LOGLEVEL_NONE = "LOG_NONE";

    private static string GetAddressableGroupName(string serverSetting) => serverSetting.Equals(ProductionSetting) ? ProductionSetting : DevAddressableGroup;

    [InitializeOnLoadMethod]
    private static void InitializeOnLoadMethod()
    {
        //ビルドボタンを押した時のイベント
        BuildPlayerWindow.RegisterBuildPlayerHandler(PushBuildButton);
    }

    private static string[] GetAllScenePaths() => EditorBuildSettings.scenes.Select(scene => scene.path).ToArray();

    //コマンドでビルドの命令処理
    public static void CommandBuildXcode()
    {
        // 引数取得
        string[] args = Environment.GetCommandLineArgs();
        string locationPath = "";
        string serverSetting = "";
        string buildNumber = "";
        string versionNumber = "";
        string showDevelopment = "";
        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "/BuildXcodeFilePath":
                    locationPath = args[i + 1];
                    break;

                case "/ServerSetting":
                    serverSetting = args[i + 1];
                    break;
                case "/BuildNumber":
                    buildNumber = args[i + 1];
                    break;
                case "/VersionNumber":
                    versionNumber = args[i + 1];
                    break;
                case "/ShowDevelopment":
                    showDevelopment = args[i + 1];
                    break;
            }
        }
        var defaultBuildPlayerOptions = new BuildPlayerOptions();
        defaultBuildPlayerOptions.targetGroup = BuildTargetGroup.iOS;
        defaultBuildPlayerOptions.target = BuildTarget.iOS;
        defaultBuildPlayerOptions.options = serverSetting.Equals(DevelopmentSetting) ? BuildOptions.Development : BuildOptions.None;

        if (serverSetting.Equals(ProductionSetting))
        {
            //PlayerSettingを本番用に更新
            UpdatePlayerSetting(ProductionSetting, versionNumber, buildNumber, defaultBuildPlayerOptions.options, false, () => { BuildXcode(defaultBuildPlayerOptions, locationPath); });
        }
        else if (serverSetting.Equals(DevelopmentSetting))
        {
            //PlayerSetting update to Development
            UpdatePlayerSetting(DevelopmentSetting, versionNumber, buildNumber, defaultBuildPlayerOptions.options, false, () => { BuildXcode(defaultBuildPlayerOptions, locationPath); });
        }
        else if (serverSetting.Equals(MockSetting))
        {
            //PlayerSetting update to Mock
            UpdatePlayerSetting(MockSetting, versionNumber, buildNumber, defaultBuildPlayerOptions.options, false, () => { BuildXcode(defaultBuildPlayerOptions, locationPath); });
        }
        
    }

    private static void BuildXcode(BuildPlayerOptions defaultBuildPlayerOptions,string locationPath,bool isPushbutton = false) {
        EditorUserBuildSettings.SetBuildLocation
       (
           target: EditorUserBuildSettings.activeBuildTarget,
           location: Path.GetFullPath(locationPath)
       );

        defaultBuildPlayerOptions.scenes = GetAllScenePaths();
        defaultBuildPlayerOptions.locationPathName = locationPath;

        var report = BuildPipeline.BuildPlayer(defaultBuildPlayerOptions);

        var result = report.summary.result;
        var path = report.summary.outputPath;
        switch (result)
        {
            case BuildResult.Succeeded:
                BuildAfterSetting(path);
                if(!isPushbutton) EditorApplication.Exit(0);
                break;
            case BuildResult.Failed:
                // ビルド時に発生したLogType毎のメッセージを取得
                var messages = report.steps.SelectMany(x => x.messages).ToLookup(x => x.type, x => x.content);
                Log.e(string.Join("\n\t", messages[LogType.Error].ToArray()));
                if (!isPushbutton) EditorApplication.Exit(1);
                break;
            case BuildResult.Cancelled:
            case BuildResult.Unknown:
                if (!isPushbutton) EditorApplication.Exit(1);
                break;
        }
    }


    //ビルドボタンを押してBuildする時の処理
    private static void PushBuildButton(BuildPlayerOptions buildPlayerOptions)
    {
        if (buildPlayerOptions.target != BuildTarget.iOS) {
            return;
        }
        string tTargetDefine = PlayerSettings.GetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.iOS);
        var timeNow = DateTime.Now;
        string buildProjectPath = Environment.GetFolderPath(Environment.SpecialFolder.Personal)+"/iOS_XcodeProject";
        string locationPath = $"{buildProjectPath}/GranDarts_{timeNow.ToString("HHmm")}_{timeNow.Date.ToString("MMdd")}_Ver{PlayerSettings.bundleVersion}";
        if (tTargetDefine.Contains(ProductionSetting))
        {
            //PlayerSettingを本番用に更新
            UpdatePlayerSetting(ProductionSetting,PlayerSettings.bundleVersion, PlayerSettings.iOS.buildNumber, BuildOptions.None, false, () => { BuildXcode(buildPlayerOptions, locationPath, isPushbutton: true); });
        }
        else if (tTargetDefine.Contains(DevelopmentSetting))
        {
            //PlayerSetting update to Development
            UpdatePlayerSetting(DevelopmentSetting,PlayerSettings.bundleVersion, PlayerSettings.iOS.buildNumber, BuildOptions.Development, false, () => { BuildXcode(buildPlayerOptions, locationPath, isPushbutton: true); });
        }
        else if (tTargetDefine.Contains(MockSetting))
        {
            //PlayerSetting update to Mock
            UpdatePlayerSetting(MockSetting,PlayerSettings.bundleVersion, PlayerSettings.iOS.buildNumber, BuildOptions.Development, false, () => { BuildXcode(buildPlayerOptions, locationPath); });
        }

    }

#region Addressable
    //コマンドからDefaultBuildScriptを呼び出す処理
    public static void CommandBuildAddressable() {
        // 引数取得
        string[] args = System.Environment.GetCommandLineArgs();
        string serverSetting = "";

        for (int i = 0; i < args.Length; i++)
        {
            switch (args[i])
            {
                case "/ServerSetting":
                    serverSetting = args[i + 1];
                    break;
            }
        }

        string groupName = GetAddressableGroupName(serverSetting);
        var settings = AddressableAssetSettingsDefaultObject.Settings;
        settings.OverridePlayerVersion = groupName + "_" + Application.version;
        settings.activeProfileId = settings.profileSettings.GetProfileId(groupName);
        AddressableAssetSettings.CleanPlayerContent();
        AddressableAssetSettings.BuildPlayerContent();
    }

#endregion

#region BuildSetting

    private static void UpdatePlayerSetting(string serverSetting,string versionNumber,string buildNumber,BuildOptions buildOptions, bool isUpdateLocalize,Action callBack) {
        string scriptingDifineSymbols = PlayerSettings.GetScriptingDefineSymbols(UnityEditor.Build.NamedBuildTarget.iOS);

        var defines = scriptingDifineSymbols.Split(';').ToList();
        Debug.unityLogger.filterLogType = LogType.Log;
        Debug.unityLogger.logEnabled = true;
        PlayerSettings.productName = "GranDarts" + (serverSetting.Equals(ProductionSetting) ? "" : "Dev");
        PlayerSettings.applicationIdentifier = "jp.luxza.GranDarts" + (serverSetting.Equals(ProductionSetting) ? "" : ".Dev");
        PlayerSettings.SplashScreen.showUnityLogo = false;
        PlayerSettings.bundleVersion = versionNumber;
        PlayerSettings.iOS.buildNumber = buildNumber;

        if (!defines.Contains(LOGLEVEL_INFO))
        {
           defines.Add(LOGLEVEL_INFO);
        }
        if (!defines.Contains(LOGLEVEL_WARN))
        {
           defines.Add(LOGLEVEL_WARN);
        }
        if (!defines.Contains(LOGLEVEL_ERROR))
        {
           defines.Add(LOGLEVEL_ERROR);
        }
        if (!defines.Contains(LOGLEVEL_DEBUG))
        {
            defines.Add(LOGLEVEL_DEBUG);
        }
        if (defines.Contains(LOGLEVEL_NONE))
        {
           defines.Remove(LOGLEVEL_NONE);
        }
        
        if (serverSetting.Equals(ProductionSetting))
        {
           defines.Add(ProductionSetting);
           if (defines.Contains(DevelopmentSetting))
           {
               defines.Remove(DevelopmentSetting);
           }
           if (defines.Contains(MockSetting))
           {
               defines.Remove(MockSetting);
           }
           if (defines.Contains(LOGLEVEL_DEBUG))
           {
               defines.Remove(LOGLEVEL_DEBUG);
           }
           Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.None);
           Application.SetStackTraceLogType(LogType.Warning, StackTraceLogType.None);
           Application.SetStackTraceLogType(LogType.Error, StackTraceLogType.ScriptOnly);
        }
        else if (serverSetting.Equals(DevelopmentSetting))
        {
           defines.Add(DevelopmentSetting);
           if (defines.Contains(ProductionSetting))
           {
               defines.Remove(ProductionSetting);
           }
           if (defines.Contains(MockSetting))
           {
               defines.Remove(MockSetting);
           }
           Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.ScriptOnly);
           Application.SetStackTraceLogType(LogType.Warning, StackTraceLogType.ScriptOnly);
           Application.SetStackTraceLogType(LogType.Error, StackTraceLogType.ScriptOnly);
        }
        else if (serverSetting.Equals(MockSetting))
        {
           defines.Add(MockSetting);
           if (defines.Contains(ProductionSetting))
           {
               defines.Remove(ProductionSetting);
           }
           if (defines.Contains(DevelopmentSetting))
           {
               defines.Remove(DevelopmentSetting);
           }
           Application.SetStackTraceLogType(LogType.Log, StackTraceLogType.ScriptOnly);
           Application.SetStackTraceLogType(LogType.Warning, StackTraceLogType.ScriptOnly);
           Application.SetStackTraceLogType(LogType.Error, StackTraceLogType.ScriptOnly);
        }
        if (buildOptions == BuildOptions.Development)
        {
            if (!defines.Contains("PushButtonBuild"))
            {
                defines.Add("PushButtonBuild");
            }
        }
        else
        {
            if (defines.Contains("PushButtonBuild"))
            {
                defines.Remove("PushButtonBuild");
            }
        }
        PlayerSettings.SetScriptingDefineSymbols(NamedBuildTarget.iOS, string.Join(";", defines));
        //if (isUpdateLocalize)
        //{
        //    AutoPullLocalizationTables(() => { callBack.Invoke(); });
        //}
        //else {
        callBack.Invoke();
        //}
    }


#endregion

    private static void BuildAfterSetting(string path) {
    var projectPath = path + "/Unity-iPhone.xcodeproj/project.pbxproj";

    //todo localize and flame work setting
    var localizeDic = AddLocalizationStringFiles(path);

    //var pbxPath = PBXProject.GetPBXProjectPath(path);
    //var pbx = new PBXProject();
    //pbx.ReadFromFile(pbxPath);

    //RewritePbxProjectFile(pbx);
    //pbx.WriteToFile(pbxPath);

    //var targetMain = pbx.GetUnityMainTargetGuid();
    //var targetFramework = pbx.GetUnityFrameworkTargetGuid();

    addLocalizationInfoPlist(path, localizeDic);

    //// 必要なフレームワークとビルド設定
    //pbx.AddFrameworkToProject(targetFramework, "libz.tbd", true);
    //pbx.SetBuildProperty(targetMain, "ENABLE_BITCODE", "NO");
    //pbx.SetBuildProperty(targetFramework, "ENABLE_BITCODE", "NO");
    //pbx.SetBuildProperty(targetMain, "GCC_ENABLE_OBJC_EXCEPTIONS", "YES");
    //pbx.SetBuildProperty(targetFramework, "GCC_ENABLE_OBJC_EXCEPTIONS", "YES");

    //pbx.AddFrameworkToProject(targetMain, "CoreLocation.framework", false);
    //pbx.AddFrameworkToProject(targetMain, "CoreBluetooth.framework", false);
    //pbx.AddFrameworkToProject(targetMain, "StoreKit.framework", false);
    //pbx.AddFrameworkToProject(targetFramework, "CoreLocation.framework", false);
    //pbx.AddFrameworkToProject(targetFramework, "CoreBluetooth.framework", false);
    //pbx.AddFrameworkToProject(targetFramework, "StoreKit.framework", false);

    ////pbx.AddCapability(targetFramework, PBXCapabilityType.AccessWiFiInformation);

    //// エンタイトルメントファイルのパス
    //var entitlementsFilePath = Path.Combine(path, "Unity-iPhone/ios.entitlements");

    //// エンタイトルメントファイルを使用して In-App Purchase と Push Notifications を追加
    //pbx.AddCapability(targetMain, PBXCapabilityType.InAppPurchase, entitlementsFilePath);
    //pbx.AddCapability(targetMain, PBXCapabilityType.PushNotifications, entitlementsFilePath);

    //File.WriteAllText(projectPath, pbx.WriteToString());

    //// ProjectCapabilityManager でエンタイトルメントファイルを追加
    //var capabilityManager = new ProjectCapabilityManager(pbxPath, entitlementsFilePath, "Unity-iPhone", targetMain);
    //capabilityManager.AddInAppPurchase();
    //capabilityManager.AddPushNotifications(false);
    //capabilityManager.WriteToFile();
}


    private const string DefaultLanguageCode = "en";    // デフォルトの言語コード
    private static readonly string[] LanguageCodes = { DefaultLanguageCode, "fr", "de", "it", "ja", "ko", "es", "Zh-Hans", "Zh-Hant" };   // 言語を増やすときはここに言語コードを追加
    private const string InfoPlistStringsCsvPath = "InfoPlistStrings";  // 実際のローカライズデータを含むCSVのファイル名
    private const char Delimiter = ','; // CSV解析用

    private static Dictionary<string, Dictionary<string, string>> AddLocalizationStringFiles(string pathToBuiltProject)
    {
        var infoPlistCsv = Resources.Load<TextAsset>(InfoPlistStringsCsvPath).ToString();// CSV読み込み
        var textLines = infoPlistCsv.Split('\r', '\n'); // CSVデータを行ごとに分割

        var localizeDic = new Dictionary<string, Dictionary<string, string>>();
        var keys = textLines[0].Split(Delimiter);   // ローカライズ用のキーを持った配列
        for (var i = 1; i < textLines.Length; i++)
        {
            var row = textLines[i].Split(Delimiter);   // ローカライズされた値を持った配列
            var lang = row[0];  // 言語コード
            localizeDic[lang] = new Dictionary<string, string>();
            for (var j = 1; j < row.Length; j++)
                localizeDic[lang][keys[j]] = row[j];   // キーと値を入れる
        }

        var dirPath = Path.Combine(pathToBuiltProject, PBXProject.GetUnityTestTargetName());
        // XCode Project内での Unity-iPhone Tests までのパス（Unity-iPhone Tests内に各ファイルを作成する）
        foreach (var languageCode in LanguageCodes) // Unity-iPhone Tests/en.lproj/InfoPlist.strings
        {
            var lProjDirPath = Path.Combine(dirPath, $"{languageCode}.lproj");
            if (!Directory.Exists(lProjDirPath)) Directory.CreateDirectory(lProjDirPath);
            var filePath = Path.Combine(dirPath, $"{languageCode}.lproj", "InfoPlist.strings");
            var content = string.Join("\n", localizeDic[languageCode].Select(pair => $"{pair.Key} = \"{pair.Value}\";"));
            File.WriteAllText(filePath, content);
        }

        return localizeDic;
    }

    private static void RewritePbxProjectFile(PBXProject pbx)
    {
        var pbxContent = pbx.WriteToString();   // pbx内のデータを文字列としてpbxContentに格納

        var updated = Regex.Replace(pbxContent, @"developmentRegion = English;",
            $"developmentRegion = {DefaultLanguageCode};"); // デフォルトの開発用言語をEnglish(deprecated)からenに変更

        updated = Regex.Replace(updated,    // 古いknownRegionsを消して対応する言語を追加
            @"knownRegions = \(\s*(\w+,[\r\n]\s*)+\);",
            $"knownRegions = (\n{string.Join($",\n", LanguageCodes)},\n);");

        foreach (var languageCode in LanguageCodes)
        {
            if (languageCode == DefaultLanguageCode) continue;  // デフォルトの言語は自動的に設定されるので何もしなくて良い

            var guid = $"XXXXXXXXXXXXXXXXXXXXXX{languageCode.ToUpper()}";
            // 24文字で他のものと重ならなければ動くが、衝突しない保証はない。正しいGUIDの取り方があるかもしれない
            var shortLine = $"{guid} /* {languageCode} */"; // XXXXXXXXXXXXXXXXXXXXXXJA /* ja */
            var longLine = $"{shortLine} = {{isa = PBXFileReference; lastKnownFileType = text.plist.strings; " +
                           $"name = {languageCode}; path = {languageCode}.lproj/InfoPlist.strings; " +
                           "sourceTree = \"<group>\"; };";
            // XXXXXXXXXXXXXXXXXXXXXXJA /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja}.lproj/InfoPlist.strings; sourceTree = "<group>"; };
            // デフォルトのenをjaに、GUIDを新しいものに書き換えているだけなので、こんなにベタ書きしなくてももっとスマートに書けそうな気もする。
            updated = Regex.Replace(updated,
                @"(?<original>.+ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = " +
                DefaultLanguageCode + "; path = " + DefaultLanguageCode + ".lproj/InfoPlist.strings; sourceTree = .+; };)",
                "${original}\n" + $"{longLine}");
            // 正規表現を使って、デフォルトの言語の宣言が書かれた行があったらそこにlongLineを追加する

            updated = Regex.Replace(updated,
                @"(?<original>isa = PBXVariantGroup;[\r\n]\s*children = \([\r\n]\s*(.+,[\r\n]\s*)*)\);",
                "${original}" + $"{shortLine},\n);");
            // 正規表現を使って、PBXVariantGroupのchildrenにshortLineを追加する
        }
        // Build Phase>Copy Bundle ResourcesにInfoPlist.stringsファイルを追加する設定
        var regex = new Regex(@"(?<guid>\w{24} /\* InfoPlist.strings \*/),");
        var infoPlistGuid = regex.Match(updated).Groups["guid"].Value;
        const string newGuid = "XXXXXXXXXXXXXXXXXXXXXXXX";

        updated = Regex.Replace(updated,
            @"(?<original>.+ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = " +
            DefaultLanguageCode + "; path = " + DefaultLanguageCode + ".lproj/InfoPlist.strings; sourceTree = .+; };)",
            newGuid + " /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = " +
            infoPlistGuid + "; };\n${original}");

        updated = Regex.Replace(updated,
            @"(?<original>\w+ /\* Data in Resources \*/,)",
            newGuid + ",\n${original}");

        pbx.ReadFromString(updated);
    }

    static void addLocalizationInfoPlist(string pjdirpath, Dictionary<string, Dictionary<string, string>> localizeDic)
    {
        string plistPath = Path.Combine(pjdirpath, "Info.plist");
        PlistDocument plist = new PlistDocument();

        plist.ReadFromFile(plistPath);
        var rootDict = plist.root;
        var array = rootDict.CreateArray("CFBundleLocalizations");// 言語コードを追加
        foreach (var languageCode in LanguageCodes) array.AddString(languageCode);

            foreach (var pinfo in localizeDic[DefaultLanguageCode])//enにある権限をinfoPlistにデフォルト値として埋める
            {
                string convertedval = System.Text.Encoding.UTF8.GetString(
                    System.Text.Encoding.Convert(
                        System.Text.Encoding.Unicode,
                        System.Text.Encoding.UTF8,
                        System.Text.Encoding.Unicode.GetBytes(pinfo.Value)
                ));
                rootDict.SetString(pinfo.Key, convertedval);
            }
         //輸出コンプライアンスの質問に暗号化を含まないと自動で回答
        rootDict.SetBoolean("ITSAppUsesNonExemptEncryption", false);
        File.WriteAllText(plistPath, plist.WriteToString());
    }

    private static void AutoPullLocalizationTables(Action action)
    {

        // 対象のStringTableCollectionを取得
        StringTableCollection UILabel = AssetDatabase.LoadAssetAtPath<StringTableCollection>("Assets/30_Localize/LocalizationTable/UILabel.asset");
        StringTableCollection ErrorMessage = AssetDatabase.LoadAssetAtPath<StringTableCollection>("Assets/30_Localize/LocalizationTable/ErrorMessage.asset");
        StringTableCollection[] collections = new StringTableCollection[] { UILabel, ErrorMessage };

        // Google認証設定を持つSheetsServiceProviderを取得
        SheetsServiceProvider serviceProvider = AssetDatabase.LoadAssetAtPath<SheetsServiceProvider>("Assets/30_Localize/GoogleSheets/Google Sheets Service.asset");

        // Google Sheetsアクセス用インスタンスを生成
        var sheets = new GoogleSheets(serviceProvider);

        foreach (var collection in collections)
        {
            // GoogleSheetsExtensionをStringTableCollectionから取得
            var sheetsExtension = collection.Extensions.OfType<GoogleSheetsExtension>().FirstOrDefault();

            // ※必ずSpreadSheetIdをGoogleSheetsインスタンスに指定する
            sheets.SpreadSheetId = sheetsExtension.SpreadsheetId;

            // 対象のStringTableCollection内の全言語(全Locale)のPullを実施
            sheets.PullIntoStringTableCollection(
                sheetId: sheetsExtension.SheetId,
                collection: collection,
                columnMapping: sheetsExtension.Columns);
        }

        action.Invoke();
    }


}
#endif