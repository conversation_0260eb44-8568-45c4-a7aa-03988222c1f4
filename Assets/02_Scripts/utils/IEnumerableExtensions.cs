using System;
using System.Collections.Generic;
using System.Linq;

namespace com.luxza.grandarts.utils
{
    public static class IEnumerableExtensions
    {
        /// <summary>
        /// Get the first index of the element that satisfies the condition
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="selector"></param>
        /// <returns>
        /// The index of the first element that satisfies the condition.
        /// Returns -1 if no element satisfies the condition.
        /// </returns>
        public static int FirstIndex<T>(this IEnumerable<T> source, Func<T, bool> selector)
        {
            int i = 0;
            foreach (var item in source)
            {
                if (selector(item))
                {
                    return i;
                }

                i++;
            }

            return -1;
        }

        /// <summary>
        /// Get the last index of the element that satisfies the condition
        /// </summary>
        /// <typeparam name="TSource"></typeparam>
        /// <param name="source"></param>
        /// <param name="selector"></param>
        /// <returns>
        /// The index of the last element that satisfies the condition.
        /// Returns -1 if no element satisfies the condition.
        /// </returns>
        public static int LastIndex<TSource>(this IEnumerable<TSource> source, Func<TSource, bool> selector)
        {
            for (int i = source.Count() - 1; i >= 0; i--)
            {
                if (selector(source.ElementAt(i)))
                {
                    return i;
                }
            }

            return -1;
        }

        /// <summary>
        /// Get the first element that satisfies the condition
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="selector"></param>
        /// <returns>
        /// The first element that satisfies the condition.
        /// Returns null if no element satisfies the condition.
        /// </returns>
        public static T FirstItem<T>(this IEnumerable<T> source, Func<T, bool> selector) where T : class
        {
            foreach (var item in source)
            {
                if (selector(item))
                {
                    return item;
                }
            }

            return null;
        }

        public static bool TryFindFirstItem<T>(this IEnumerable<T> source, Func<T, bool> selector, out T result)
        {
            foreach (var item in source)
            {
                if (selector(item))
                {
                    result = item;
                    return true;
                }
            }

            result = default;
            return false;
        }

        /// <summary>
        /// Get the max element satisfying the condition
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="counter"></param>
        /// <returns>
        /// The max element satisfying the condition.
        /// </returns>
        public static T FindMax<T>(this IEnumerable<T> source, Func<T, int> counter)
        {
            T result = default(T);
            int c = 0;
            foreach (var item in source)
            {
                var count = counter(item);
                if (count > c)
                {
                    result = item;
                    c = count;
                }
            }

            return result;
        }

        public static int FindMaxIndex(this IEnumerable<int> source)
        {
            int maxIndex = -1;
            int maxValue = int.MinValue;
            int index = 0;

            foreach (var item in source)
            {
                if (item > maxValue)
                {
                    maxValue = item;
                    maxIndex = index;
                }
                index++;
            }

            return maxIndex;
        }

        public static int FindMaxIndex(this IEnumerable<double> source)
        {
            int maxIndex = -1;
            var maxValue = double.MinValue;
            int index = 0;

            foreach (var item in source)
            {
                if (item > maxValue)
                {
                    maxValue = item;
                    maxIndex = index;
                }
                index++;
            }

            return maxIndex;
        }

        /// <summary>
        /// Find the first element that satisfies the condition
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="self"></param>
        /// <param name="match"></param>
        /// <param name="result"></param>
        /// <returns>True if element found. False if element not found.</returns>
        public static bool TryFind<T>(this IEnumerable<T> self, Func<T, bool> match, out T result)
        {
            foreach (var item in self)
            {
                if (match(item))
                {
                    result = item;
                    return true;
                }
            }
            result = default;
            return false;
        }

        /// <summary>
        /// Get a element randomly
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="self"></param>
        /// <returns>
        /// A element
        /// </returns>
        public static T GetRandom<T>(this IEnumerable<T> self)
        {
            var random = new Random();
            var index = random.Next(0, self.Count() - 1);
            return self.ElementAt(index);
        }

        /// <summary>
        /// Check if all elements have the same value
        /// </summary>
        /// <typeparam name="TSournce"></typeparam>
        /// <param name="l"></param>
        /// <returns>True if all elements are same. False if any elements are not same.</returns>
        public static bool HasAllSameValues<TSournce>(this IEnumerable<TSournce> l)
        {
            if (!l.Any()) return true;
            var firstValue = l.ElementAt(0);
            return l.All(lv => lv.Equals(firstValue));
        }

        /// <summary>
        /// Check if any elements have different values
        /// </summary>
        /// <typeparam name="TSource"></typeparam>
        /// <param name="l"></param>
        /// <returns>
        /// True if any elements are different. False if all elements are same.
        /// </returns>
        public static bool HasAnyDifferentValue<TSource>(this IEnumerable<TSource> l)
        {
            if (!l.Any()) return false;
            var firstValue = l.ElementAt(0);
            return l.Any(lv => !lv.Equals(firstValue));
        }

        /// <summary>
        /// Merge the list of bools
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static IEnumerable<bool> MergeBoolList(this IEnumerable<IEnumerable<bool>> list)
        {
            return list.Aggregate((a, b) => a.Zip(b, (x, y) => x || y));
            /* Enumerable.Range(0, analysisData.First().HasOpenedAreas.Length)
                    .Select(i => analysisData.Any(a => a.HasOpenedAreas[i]))
                    .ToArray() */
        }

        /// <summary>
        /// Merge the list of ints
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static IEnumerable<int> MergeIntList(this IEnumerable<IEnumerable<int>> list)
        {
            return list.Aggregate((a, b) => a.Zip(b, (x, y) => x + y));
        }
    }
}