using System;
using UnityEngine;
using R3;

namespace com.luxza.grandarts.utils.logger
{
    public class GranLogHandler : ILogHandler, IDisposable
    {
        private readonly ILogHandler _defaultLogHandler;

        private Subject<Exception> _exceptionOccured = new Subject<Exception>();
        public Observable<Exception> ExceptionOccured => _exceptionOccured;

        public GranLogHandler() {
            _defaultLogHandler = Debug.unityLogger.logHandler;
        }

        void ILogHandler.LogException(Exception exception, UnityEngine.Object context)
        {
            if(exception is OperationCanceledException) return;
            _exceptionOccured.OnNext(exception);
            _defaultLogHandler.LogException(exception, context);
        }

        void ILogHandler.LogFormat(LogType logType, UnityEngine.Object context, string format, params object[] args)
        {
            _defaultLogHandler.LogFormat(logType, context, format, args);
        }

        public void Dispose()
        {
            _exceptionOccured.Dispose();
        }
    }
}
