using System;
using System.Collections.Generic;
using com.luxza.grandarts.infrastructures.file;
using com.luxza.grandarts.infrastructures.file.addressables;
using com.luxza.grandarts.infrastructures.playerprefs.user;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.game.setting;
using com.luxza.grandarts.infrastructures.mock.contact;
using com.luxza.grandarts.usecases.contact;
using com.luxza.grandarts.usecases.image;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.usecases.user;
using com.luxza.grandarts.usecases.sounds;
using com.luxza.grandarts.usecases.news;
using com.luxza.grandarts.usecases.playdata;
using com.luxza.grandarts.usecases.game;
using com.luxza.grandarts.usecases.game.local.zeroone;
using com.luxza.grandarts.usecases.game.local.standardcr;
using com.luxza.grandarts.usecases.game.local.hiddencr;
using com.luxza.grandarts.usecases.game.local.countup;
using com.luxza.grandarts.usecases.game.local.target20;
using com.luxza.grandarts.usecases.game.local.kickdown;
using com.luxza.grandarts.usecases.game.local.beyondtop;
using com.luxza.grandarts.usecases.game.local.multiplecr;
using com.luxza.grandarts.infrastructures.file.player;
using com.luxza.grandarts.usecases.analysis;
using com.luxza.grandarts.infrastructures.firebase.analytics;
using com.luxza.grandarts.infrastructures.file.game.setting;

#if !Mock
using com.luxza.grandarts.infrastructures.http;
using com.luxza.grandarts.infrastructures.graphql.services.game;
using com.luxza.grandarts.infrastructures.graphql.services.news;
using com.luxza.grandarts.infrastructures.graphql.services.player;
using com.luxza.grandarts.infrastructures.graphql.services.playdata;
using com.luxza.grandarts.infrastructures.graphql.services.user;
using com.luxza.grandarts.infrastructures.graphql.services.game.zeroone;
using com.luxza.grandarts.infrastructures.graphql.services.game.standardcr;
using com.luxza.grandarts.infrastructures.graphql.services.game.hiddencr;
using com.luxza.grandarts.infrastructures.graphql.services.game.countup;
using com.luxza.grandarts.infrastructures.graphql.services.game.halfit;
using com.luxza.grandarts.infrastructures.graphql.services.game.target20;
using com.luxza.grandarts.infrastructures.graphql.services.game.rotation;
using com.luxza.grandarts.infrastructures.graphql.services.game.kickdown;
using com.luxza.grandarts.infrastructures.graphql.services.game.beyondtop;
using com.luxza.grandarts.infrastructures.graphql.services.game.multiplecr;
using com.luxza.grandarts.usecases.system;
using com.luxza.grandarts.infrastructures.graphql.services.guest;
using com.luxza.grandarts.infrastructures.unified.auth;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.infrastructures.firebase.online.lobby;
using com.luxza.grandarts.domains.online.lobby.notification;
using com.luxza.grandarts.infrastructures.firebase.online.lobby.room;
using com.luxza.grandarts.domains.online.room.notification;
using com.luxza.grandarts.infrastructures.unified.online.lobby;
using com.luxza.grandarts.usecases.guest;
using com.luxza.grandarts.domains.game.videochat;
using com.luxza.grandarts.infrastructures.webrtc.becausewhynot;
using com.luxza.grandarts.usecases.online.lobby.room;
using com.luxza.grandarts.infrastructures.unified.online.match;
using com.luxza.grandarts.infrastructures.firebase.online;
using com.luxza.grandarts.usecases.graneye;
using com.luxza.grandarts.infrastructures.graphql.services.graneye;
using com.luxza.grandarts.infrastructures.unified.system;
using com.luxza.grandarts.usecases.system;
using com.luxza.grandarts.infrastructures.appversion;
using com.luxza.grandarts.usecases.online.match;
using com.luxza.grandarts.domains.online.videochat;
using com.luxza.grandarts.usecases.game.local.cutthroat;
using com.luxza.grandarts.infrastructures.graphql.services.game.cutthroat;
using com.luxza.grandarts.usecases.game.local.hiddencutthroat;
using com.luxza.grandarts.infrastructures.graphql.services.game.hiddencutthroat;
using com.luxza.grandarts.usecases.game.local.halfit;
using com.luxza.grandarts.usecases.game.local.rotation;
using com.luxza.grandarts.domains.dlc;








#else

using com.luxza.grandarts.usecases.system;
using com.luxza.grandarts.infrastructures.mock.player;
using com.luxza.grandarts.infrastructures.mock;
using com.luxza.grandarts.infrastructures.mock.game;
using com.luxza.grandarts.infrastructures.mock.news;
using com.luxza.grandarts.infrastructures.mock.playdata;
using com.luxza.grandarts.infrastructures.mock.user;
using com.luxza.grandarts.infrastructures.mock.game.zeroone;
using com.luxza.grandarts.infrastructures.mock.game.standardcr;
using com.luxza.grandarts.infrastructures.mock.game.hiddencr;
using com.luxza.grandarts.infrastructures.mock.game.countup;
using com.luxza.grandarts.infrastructures.mock.game.halfit;
using com.luxza.grandarts.infrastructures.mock.game.kickdown;
using com.luxza.grandarts.infrastructures.mock.online.lobby;
using com.luxza.grandarts.infrastructures.mock.online.lobby.room;
using com.luxza.grandarts.infrastructures.mock.online.setting;
using com.luxza.grandarts.usecases.online.lobby;
using com.luxza.grandarts.domains.online.lobby.notification;
using com.luxza.grandarts.usecases.guest;
using com.luxza.grandarts.domains.online.room.notification;
using com.luxza.grandarts.usecases.online.match;
using com.luxza.grandarts.domains.online.videochat;
using com.luxza.grandarts.domains.game.videochat;
using com.luxza.grandarts.infrastructures.webrtc.becausewhynot;
using com.luxza.grandarts.infrastructures.http;
using com.luxza.grandarts.infrastructures.mock.online.match;
using com.luxza.infrastructures.mock.player;
using com.luxza.grandarts.infrastructures.unified.system;
using com.luxza.grandarts.infrastructures.unified.online.match;
using com.luxza.grandarts.usecases.game.local.halfit;
using com.luxza.grandarts.usecases.game.local.hiddencutthroat;
using com.luxza.grandarts.usecases.game.local.cutthroat;
using com.luxza.grandarts.infrastructures.mock.game.cutthroat;
using com.luxza.grandarts.infrastructures.mock.game.hiddencutthroat;
using com.luxza.grandarts.domains.dlc;







#endif


#if !UNITY_EDITOR
using com.luxza.grandarts.infrastructures.file.picker;
#endif

#if UNITY_EDITOR
using com.luxza.grandarts.infrastructures.file.picker.editor;
#endif

namespace com.luxza.grandarts.dicontainer
{
    public class DIContainer
    {
        private Dictionary<Type, (Type impementationType, LifeCycleType lifeCycle)> _typeMapping =
            new Dictionary<Type, (Type impementationType, LifeCycleType lifeCycle)>();

        private Dictionary<Type, object> _instanceCache = new Dictionary<Type, object>();

        private static DIContainer _instance;
        public static DIContainer Instance => _instance ??= CreateDefault();

        private DIContainer()
        { }

        public enum LifeCycleType
        {
            Singleton,
            Scoped
        }

        public void Inject<Tinterface, Timplementation>
        (
            LifeCycleType lifeCycle
        )
            where Tinterface : class
            where Timplementation : class, Tinterface
        {
            Inject(lifeCycle, typeof(Tinterface), typeof(Timplementation));
        }

        public void Inject
        (
            LifeCycleType lifeCycle,
            Type interfaceType,
            Type implematationType
        )
        {
            if (_typeMapping.ContainsKey(interfaceType))
            {
                _typeMapping[interfaceType] = (impementationType: implematationType, lifeCycle: lifeCycle);
            }
            else
            {
                _typeMapping.Add(interfaceType, (impementationType: implematationType, lifeCycle: lifeCycle));
            }
        }

        public Tinterface Resolve<Tinterface>()
            where Tinterface : class
        {
            var type = typeof(Tinterface);

            if (!_typeMapping.ContainsKey(type))
            {
                throw new SystemException($"DIContainer has not instance of {typeof(Tinterface)}");
            }

            if (_typeMapping[type].lifeCycle == LifeCycleType.Singleton)
            {
                if (_instanceCache.ContainsKey(type))
                {
                    return _instanceCache[type] as Tinterface;
                }
                else
                {
                    var implType = _typeMapping[type].impementationType;
                    var obj = Activator.CreateInstance(implType);
                    _instanceCache.Add(type, obj);
                    return obj as Tinterface;
                }
            }
            else
            {
                var implType = _typeMapping[type].impementationType;
                return Activator.CreateInstance(implType) as Tinterface;
            }
        }

        private static DIContainer CreateDefault()
        {
            var container = new DIContainer();
#if Mock
            container.Inject<IAuthService, MockAuthService>(LifeCycleType.Scoped);
            container.Inject<IPlayerService, MockPlayerService>(LifeCycleType.Scoped);
            container.Inject<IMatchIDService, MockMatchIDService>(LifeCycleType.Scoped);
            container.Inject<INewsService, MockNewsService>(LifeCycleType.Scoped);
            container.Inject<IPlayDataService, MockPlayDataService>(LifeCycleType.Scoped);
            container.Inject<IPlayerRepository, MockPlayerRepository>(LifeCycleType.Scoped);
            container.Inject<IUserRepository, MockUserRepository>(LifeCycleType.Scoped);
            container.Inject<ISendZeroOnePlayDataService, MockSendZeroOnePlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendStandardCricketPlayDataService, MockSendStandardCricketPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHiddenCRPlayDataService, MockSendHiddenCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendCutthroatCRPlayDataService, MockSendCutthroatCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHiddenCutthroatCRPlayDataService, MockSendHiddenCutthroatCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendCountUpPlayDataService, MockSendCountUpPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHalfItPlayDataService, MockSendHalfItPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendKickdownPlayDataService, MockSendKickdownPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ILobbyService, MockLobbyService>(LifeCycleType.Scoped);
            container.Inject<ILobbyNotificationCenterFactory, MockLobbyNotificationCenterFactory>(LifeCycleType.Scoped);
            container.Inject<IWaitingRoomsFactory, MockWaitingRoomsFactory>(LifeCycleType.Scoped);
            container.Inject<IOnlineRequestFormatRepository, MockOnlineRequestFormatRepository>(LifeCycleType.Scoped);
            container.Inject<IGuestServiceAPIClient, MockGuestServiceApiClient>(LifeCycleType.Scoped);
            container.Inject<IRoomNotificationCeterFactory, MockRoomNotificationCenterFactory>(LifeCycleType.Scoped);
            container.Inject<IOnlineMatchServerService, MockOnlineMatchServerService>(LifeCycleType.Scoped);
            container.Inject<IGuestServiceAPIClient, MockGuestServiceApiClient>(LifeCycleType.Scoped);
            container.Inject<IPlayerPresenceRepository, MockPlayerPresenceRepository>(LifeCycleType.Scoped);
#elif Development || Production
            container.Inject<IAuthService, UnifiedGraphQLFirebaseAuthService>(LifeCycleType.Scoped);
            container.Inject<IAppVersionRepository,GraphQLAppversionCheckDataRequest >(LifeCycleType.Scoped);
            container.Inject<IPlayerService, GraphQLPlayerService>(LifeCycleType.Scoped);
            container.Inject<IMatchIDService, GraphQLMatchIDService>(LifeCycleType.Scoped);
            container.Inject<INewsService, GraphQLNewsService>(LifeCycleType.Scoped);
            container.Inject<IPlayDataService, GraphQLPlayDataService>(LifeCycleType.Scoped);
            container.Inject<IPlayerRepository, GraphQLPlayerRepository>(LifeCycleType.Scoped);
            container.Inject<IUserRepository, GraphqlUserRepository>(LifeCycleType.Scoped);
            container.Inject<ISendZeroOnePlayDataService, GraphQLSendZeroOnePlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendStandardCricketPlayDataService, GraphQLSendStandardCricketPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHiddenCRPlayDataService, GraphQLSendHiddenCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendCutthroatCRPlayDataService, GraphQLSendCutthroatCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHiddenCutthroatCRPlayDataService, GraphQLSendHiddenCutthroatCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendCountUpPlayDataService, GraphQLSendCountUpPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendTarget20PlayDataService, GraphQLSendTarget20PlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendHalfItPlayDataService, GraphQLSendHalfItPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendRotationPlayDataService, GraphQLSendRotationPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendKickdownPlayDataService, GraphQLSendKickdownPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendBeyondTopPlayDataService, GraphQLSendBeyondTopPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ISendMultipleCRPlayDataService, GraphQLSendMultipleCRPlayDataService>(LifeCycleType.Scoped);
            container.Inject<ILobbyService, UnifiedLobbyService>(LifeCycleType.Scoped);
            container.Inject<IMatchRoomService, UnifiedMatchRoomService>(LifeCycleType.Scoped);
            container.Inject<ILobbyNotificationCenterFactory, FirebaseLobbyNotificationCenterFactory>(LifeCycleType.Scoped);
            container.Inject<IWaitingRoomsFactory, FirebaseWaitingRoomsFactory>(LifeCycleType.Scoped);
            container.Inject<IOnlineRequestFormatRepository, FirebaseOnlineRequestFormatRepository>(LifeCycleType.Scoped);
            container.Inject<IRoomNotificationCeterFactory, FirebaseRoomNotificationCenterFactory>(LifeCycleType.Scoped);
            container.Inject<IGuestServiceAPIClient, GraphQLGuestService>(LifeCycleType.Scoped);
            container.Inject<IGranEyeService, GraphQLGranEyeService>(LifeCycleType.Scoped);
            container.Inject<IPlayerPresenceRepository, FireStorePlayerPresenceRepository>(LifeCycleType.Scoped);
#endif
            container.Inject<IUserCacheRepository, PPUserCacheRepository>(LifeCycleType.Scoped);
            container.Inject<IVideoSettingRepository, PPVideoSettingCacheRepository>(LifeCycleType.Scoped);
            container.Inject<IEmbededPlayerIconPortraitRepository, AddressablesEmbededPlayerIconPortraitRepository>(LifeCycleType.Scoped);
            container.Inject<ICPUPlayerRepository, FileCPUPlayerRepository>(LifeCycleType.Scoped);
            container.Inject<IInstantPlayerRepository, PPInstantPlayerRepository>(LifeCycleType.Scoped);
            container.Inject<ILocalGameRequestFormatRepository, PPLocalGamePlayFormatRepository>(LifeCycleType.Scoped);
            container.Inject<ISendInquiryServiceAPIClient, MockContactAPIClient>(LifeCycleType.Scoped);
            container.Inject<ICallerSoundRepository, AddressablesCallerSoundRepository>(LifeCycleType.Scoped);
            container.Inject<ICountryFlagImageLoader, AddressablesCountryFlagImageLoader>(LifeCycleType.Scoped);
            container.Inject<IEventTracker, FirebaseEventTracker>(LifeCycleType.Singleton);
            container.Inject<IAppInitializationService, UnifiedAppInitializationService>(LifeCycleType.Scoped);
            container.Inject<IOnlineMatchRecoveryService, UnifiedMatchRecoveryService>(LifeCycleType.Scoped);
            container.Inject<IDLCManager, AddressableDLCManager>(LifeCycleType.Scoped);
#if UNITY_EDITOR
            container.Inject<IImageFilePicker, ExplorerPicker>(LifeCycleType.Scoped);
#else
            container.Inject<IImageFilePicker, NativeGarallyPicker>(LifeCycleType.Scoped);
#endif

            container.Inject<IVideoChatFactory, BecauseWhyNotVideoChatClientFactory>(LifeCycleType.Scoped);
            return container;
        }

        public IImageRepository ResolveAsPlayerIconRepository()
        {
            return new PlayerIconFileRepository();
        }

        public IImageRepository ResolveAsPlayerIconWebImageRepository()
        {
#if Mock
            return new MockWebPlayerIconImageRepository();
#else
            return new WebImageRepository();
#endif
        }

        public IImageRepository ResolveWebImageRepository()
        {
            return new WebImageRepository();
        }

        public IImageRepository ResolveAsNewsImageRepository()
        {
            return new NewsImageFileRepository();
        }
    }
}