using System.Globalization;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.language;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.infrastructures.firebase.auth;
using com.luxza.grandarts.infrastructures.graphql.services.user;
using com.luxza.grandarts.infrastructures.graphql.requests.auth;
using com.luxza.grandarts.infrastructures.playerprefs.user;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.user;
using Cysharp.Threading.Tasks;
using FirebaseAuthCacheService = com.luxza.grandarts.infrastructures.firebase.cache.AuthCacheService;
using AuthCacheService = com.luxza.grandarts.infrastructures.graphql.cache.AuthCacheService;

namespace com.luxza.grandarts.infrastructures.unified.auth
{
    public class UnifiedGraphQLFirebaseAuthService : IAuthService
    {
        IUserCacheRepository _userCacheRepository = new PPUserCacheRepository();

        IUserRepository _userRepository = new GraphqlUserRepository();
        public async UniTask<User> LoginAsync(EmailAddress email, UserPassword password, CancellationToken cancellationToken)
        {
            var user = await new LoginRequest(email, password).SendAsync(cancellationToken);
            await new FirebaseSignIn().ExecuteAsync(user.FirebaseCustomToken, cancellationToken);
            return user;
        }

        public async UniTask<User> AutoLoginAsync(CancellationToken cancellationToken)
        {
            if (await _userCacheRepository.TryGetEmailAsync(out var email, cancellationToken) == false)
            {
                throw new SessionTimeOutException("Email is not cached.");
            }

            if (await _userCacheRepository.TryGetUserPasswordAsync(out var password, cancellationToken) == false)
            {
                throw new SessionTimeOutException("Password is not cached.");
            }

            if (FirebaseAuthCacheService.TryGetCustomToken(out var firebaseCustomToken) == false)
            {
                throw new SessionTimeOutException("Firebase token is not cached.");
            }

            if (AuthCacheService.TryGetAuths(out var auths) == false)
            {
                throw new SessionTimeOutException("AccessToken or RefreshToken is not cached.");
            }

            await new FirebaseSignIn().ExecuteAsync(firebaseCustomToken, cancellationToken);

            var user = await _userRepository.GetAsync(email, password, cancellationToken);
            user.SetAuth(auths.accessToken, auths.refreshToken, firebaseCustomToken);


            await _userCacheRepository.SaveEmailAsync(user.Email, cancellationToken);
            await _userCacheRepository.SavePasswordAsync(user.Password, cancellationToken);
            await _userCacheRepository.SaveUseIdAsync(user.Id, cancellationToken);

            return user;
        }


        public UniTask LogoutAsync(UserId userId, CancellationToken cancellationToken)
        {
            new FirebaseSignOut().ExecuteAsync();
            FirebaseAuthCacheService.ClearAll();
            AuthCacheService.ClearAll();
            return UniTask.CompletedTask;
        }

        public UniTask<bool> SendForgotPasswordVerifyCodeRequest(EmailAddress email, CancellationToken cancellationToken)
        {
            return new ResetPasswordRequest(email).SendAsync(cancellationToken);
        }

        public UniTask<bool> SendPasswordResetRequest(EmailAddress email, VerifyCode code, string newPassword, CancellationToken cancellationToken)
        {
            return new ConfirmPasswordRequest(email, code.ToString(), newPassword).SendAsync(cancellationToken);
        }

        public UniTask SendVerifyCodeRequest(EmailAddress email, CancellationToken cancellationToken)
        {
            return new SendVerificationCodeToEmailRequest(email).SendAsync(cancellationToken);
        }

        public UniTask<User> SignUpAsync(EmailAddress email, UserPassword password, RegionInfo country, Language language, CancellationToken cancellationToken)
        {
            return new SignUpRequest(email, password, country, language).SendAsync(cancellationToken);
        }

        public UniTask VerifyEmailAsync(EmailAddress email, string verifyCode, CancellationToken cancellationToken)
        {
            return new VerifyEmailRequest(email, verifyCode).SendAsync(cancellationToken);
        }
    }
}