using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.online.room.notification;
using com.luxza.grandarts.domains.online.videochat;
using Cysharp.Threading.Tasks;
using R3;

namespace com.luxza.grandarts.infrastructures.mock.online.lobby.room
{
    public class MockRoomNotificationCenter : IRoomNotificationCenter
    {
        public Observable<(LobbyId lobbyId, MatchRoomId matchRoomId)> OnDeleted => _onDeleted;

        public Observable<MatchRoom> OnUpdated => _onUpdated;

        private Subject<(LobbyId lobbyId, MatchRoomId matchRoomId)> _onDeleted = new();
        private Subject<MatchRoom> _onUpdated = new();

        public MockRoomNotificationCenter(MatchRoom room)
        {
            UniTask.Void(async () =>
            {
                await UniTask.Delay(3000);
                room.StartMatch(
                    "PlayLog",
                    new domains.online.room.OnlineMatchServerInfo()
                    {
                        Host = "localhost",
                        RoomId = "1234",
                    },
                    new VideoChatRoom()
                    {
                        signalingServer = "localhost",
                        turnServer = "localhost",
                        sturnServer = "localhost",
                    });
                _onUpdated.OnNext(room);
            });
        }

        public void Dispose()
        {
            _onDeleted.Dispose();
            _onUpdated.Dispose();
        }
    }
}