using System.Threading;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandarts.usecases.game.local.countup;
using com.luxza.grandartslogic.domain.game.countup;

namespace com.luxza.grandarts.infrastructures.mock.game.countup {
    public class MockSendCountUpPlayDataService : ISendCountUpPlayDataService
    {
        public void SendCountUpPlayDataOnBackground(GameRuleCountUp rule, GameResult result, CancellationToken token)
        {
            // do nothing
        }
    }
}