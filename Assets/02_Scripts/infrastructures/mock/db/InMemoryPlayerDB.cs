
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using com.luxza.grandarts.domains.user;

namespace com.luxza.grandarts.infrastructures.mock.db {
    internal class InMemoryPlayerDB {
        private static InMemoryPlayerDB _instance;
        public static InMemoryPlayerDB Instance => _instance ??= new InMemoryPlayerDB();

        private Dictionary<PlayerId, IPlayer> _data = new Dictionary<PlayerId, IPlayer>();

        private int _idSeed = 1;

        private InMemoryPlayerDB()
        {
             //Profile画面表示用に使用するプレイヤーデータ
            var playerId = CreateId();
            var readonlyPlayer1 = UpdateFieldValuesRandomly(playerId, new ReadonlyPlayer(playerId, new GranId($"ProfilePlayer_{playerId}"), $"ProfilePlayer_{playerId}", $"dummy-{playerId}", BoardSize.Inch155, RegionInfo.CurrentRegion));
            _data[playerId] = readonlyPlayer1;
            playerId = CreateId();
            var readonlyPlayer2 = UpdateFieldValuesRandomly(playerId, new ReadonlyPlayer(playerId, new GranId($"ProfilePlayer_{playerId}"), $"ProfilePlayer_{playerId}", $"dummy-{playerId}", BoardSize.Inch132, RegionInfo.CurrentRegion));
            _data[playerId] = readonlyPlayer2;
            playerId = CreateId();
            var readonlyPlayer3 = UpdateFieldValuesRandomly(playerId, new ReadonlyPlayer(playerId, new GranId($"ProfilePlayer_{playerId}"), $"ProfilePlayer_{playerId}", $"dummy-{playerId}", BoardSize.Steel, RegionInfo.CurrentRegion));
            _data[playerId] = readonlyPlayer3;
        }

        private PlayerId CreateId() {
            return new PlayerId(_idSeed++);
        }

        internal void Clear() {
            _data.Clear();
        }

        public IEnumerable<Player>
        CreateDefaultPlayers(UserId userId) {
            var playerId = CreateId();
            var steelPlayer = new Player(playerId, new GranId($"player-{playerId}"), $"DUMMY_PLAYER_{playerId}", $"dummy-{playerId}", BoardSize.Steel, RegionInfo.CurrentRegion);
            _data[playerId] = steelPlayer;
            InitializeDummyGuestsFor(playerId, BoardSize.Steel);
            playerId = CreateId();
            var _155Player = new Player(playerId, new GranId($"player-{playerId}"), $"DUMMY_PLAYER_{playerId}", $"dummy-{playerId}", BoardSize.Inch155, RegionInfo.CurrentRegion);
            _data[playerId] = _155Player;
            InitializeDummyGuestsFor(playerId, BoardSize.Inch155);
            playerId = CreateId();
            var _132Player = new Player(playerId, new GranId($"player-{playerId}"), $"DUMMY_PLAYER_{playerId}", $"dummy-{playerId}", BoardSize.Inch132, RegionInfo.CurrentRegion);
            _data[playerId] = _132Player;
            InitializeDummyGuestsFor(playerId, BoardSize.Inch132);
            var players = new List<Player>(){steelPlayer, _155Player, _132Player};

            InMemoryRelationForUserAndPlayer.Instance.Create(userId, players.Select(p => p.Id));

            return players;
        }

        private void InitializeDummyGuestsFor(PlayerId playerId, BoardSize boardSize) {
            for(int i = 0; i < 5; i++) {
                var guestId = CreateId();
                var guestGranId = new GranId($"guest-{guestId}");

                var guest = new ReadonlyPlayer(guestId, guestGranId, $"DUMMY_GUEST_{guestId}", $"dummy-{guestId}", boardSize, RegionInfo.CurrentRegion);

                InMemoryGuestDB.Instance.Add(playerId, guestId);

                _data[guestId] = guest;
            }
        }

        internal IPlayer CreateDummyGuestFor(PlayerId playerId) {
            if(!_data.ContainsKey(playerId)) {
                throw new PlayerNotFound($"Player:{playerId} is not found.");
            }

            var owner = _data[playerId];
            var guestId = CreateId();
            var guestGranId = new GranId($"guest-{guestId}");
            var guest = new ReadonlyPlayer(guestId, guestGranId, $"DUMMY_GUEST_{guestId}", $"dummy-{guestId}", owner.BoardSize, RegionInfo.CurrentRegion);
            _data[guest.Id] = guest;
            InMemoryGuestDB.Instance.Add(playerId, guestId);
            return guest;
        }

        internal IPlayer CreateDummyGuestFor(PlayerId playerId, PlayerId guestId) {
            if(!_data.ContainsKey(playerId)) {
                throw new PlayerNotFound($"Player:{playerId} is not found.");
            }

            if(_data.ContainsKey(guestId)) {
                throw new AlreadyExist($"Player {guestId} is already exist.");
            }

            var owner = _data[playerId];
            var guestGranId = new GranId($"guest-{guestId}");
            var guest = new ReadonlyPlayer(guestId, guestGranId, $"DUMMY_GUEST_{guestId}", $"dummy-{guestId}", owner.BoardSize, RegionInfo.CurrentRegion);
            _data[guest.Id] = guest;
            InMemoryGuestDB.Instance.Add(playerId, guestId);
            return guest;
        }

        public Player Create(UserId userId, GranId granId, string name, BoardSize boardSize) {
            var playerId = CreateId();

            if(_data.ContainsKey(playerId)) {
                throw new AlreadyExist($"{playerId} is already exist.");
            }

            var player = new Player(playerId, granId, name, name, boardSize, RegionInfo.CurrentRegion);
            _data[playerId] = player;
            InMemoryRelationForUserAndPlayer.Instance.Add(userId, playerId);
            return player;
        }

        public IPlayer Read(PlayerId playerId)
        {
            if (!_data.ContainsKey(playerId))
            {
                throw new PlayerNotFound($"{playerId} is not found.");
            }
            var playerdata = _data[playerId];

            _data[playerId] = UpdateFieldValuesRandomly(playerId,playerdata);
            return _data[playerId];
        }

        private IPlayer UpdateFieldValuesRandomly(PlayerId playerId, IPlayer player)
        {
            var overall80Rating = UnityEngine.Random.Range(1.00f, 19.99f);
            var overall100Rating = UnityEngine.Random.Range(1.00f, 19.99f);
            var zeroone80Stats = UnityEngine.Random.Range(1.00f, 141.99f);
            var zeroone100Stats = UnityEngine.Random.Range(1.00f, 47.99f);
            var cr80Stats = UnityEngine.Random.Range(1.0f, 5.7f);
            var cr100Stats = UnityEngine.Random.Range(1.0f, 6.0f);

            if (player is Player)
            {
                Player playerData = (Player)player;
                playerData.Overall80Stats = Stats.CreateOverall80StatsByStats(overall80Rating);
                playerData.Overall100Stats = Stats.CreateOverall100StatsByStats(overall100Rating);
                playerData.ZeroOneFatBull80Stats = Stats.CreateZeroOne80StatsByStats(zeroone80Stats);
                playerData.ZeroOneFatBull100Stats = Stats.CreateZeroOne100StatsByStats(zeroone100Stats);
                playerData.ZeroOneSeparatedBull80Stats = Stats.CreateZeroOne80StatsByStats(zeroone80Stats);
                playerData.ZeroOneSeparatedBull100Stats = Stats.CreateZeroOne100StatsByStats(zeroone100Stats);
                playerData.CR80Stats = Stats.CreateCR80StatsByStats(cr80Stats);
                playerData.CR100Stats = Stats.CreateCR100StatsByStats(cr100Stats);
                playerData.FriendTotal = UnityEngine.Random.Range(0, 5000);
                playerData.OnlineMatchTotal = UnityEngine.Random.Range(0, 5000);
                playerData.GoodTotal = UnityEngine.Random.Range(0, 5000);

                return playerData;
            }
            else
            {
                return new ReadonlyPlayer(playerId,
                                         player.GranId,
                                         player.Name,
                                         player.IconURL,
                                         player.BoardSize,
                                         player.Region,
                                         overall80Stats: Stats.CreateOverall80StatsByStats(overall80Rating),
                                         overall100Stats: Stats.CreateOverall100StatsByStats(overall100Rating),
                                         zeroOneFatBull80Stats: Stats.CreateZeroOne80StatsByStats(zeroone80Stats),
                                         zeroOneFatBull100Stats: Stats.CreateZeroOne100StatsByStats(zeroone100Stats),
                                         zeroOneSeparatedBull80Stats: Stats.CreateZeroOne80StatsByStats(zeroone80Stats),
                                         zeroOneSeparatedBull100Stats: Stats.CreateZeroOne100StatsByStats(zeroone100Stats),
                                         cr80Stats: Stats.CreateCR80StatsByStats(cr80Stats),
                                         cr100Stats: Stats.CreateCR100StatsByStats(cr100Stats),
                                         friendTotal: UnityEngine.Random.Range(0, 5000),
                                         onlineMatchTotal: UnityEngine.Random.Range(0, 5000),
                                         goodTotal: UnityEngine.Random.Range(0, 5000)
                                         );
            }
        }

        public bool TryRead(PlayerId playerId, out IPlayer player) {
            if(!_data.ContainsKey(playerId)) {
                player = null;
                return false;
            }

            player = _data[playerId];
            return true;
        }

        public Player Update(PlayerId playerId, Player player) {
            if(!_data.ContainsKey(playerId)) {
                throw new PlayerNotFound($"{playerId} is not found.");
            }

            _data[playerId] = player;
            return player;
        }

        public void Delete(PlayerId playerId) {
            if(!_data.ContainsKey(playerId)) {
                throw new PlayerNotFound($"{playerId} is not found.");
            }

            _data.Remove(playerId);
        }
    }
}