using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.game.result.zeroone;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain.game;
using Firebase.Firestore;

namespace com.luxza.grandarts.infrastructures.firebase.online.lobby.room.entities
{
    [FirestoreData]
    internal class ZeroOneAnalysisEntity
    {
        [FirestoreProperty]
        public float Stats80 { get; set; }
        [FirestoreProperty]
        public float Stats100 { get; set; }
        [FirestoreProperty]
        public int ThrowCount { get; set; }
        [FirestoreProperty]
        public int RoundCount { get; set; }

        [FirestoreProperty]
        public float BullRate { get; set; }
        [FirestoreProperty]
        public float OuterBullRate { get; set; }

        [FirestoreProperty]
        public float InnerBullRate { get; set; }

        [FirestoreProperty]
        public float InnerBullCountDevidedByWholeBullCountRate { get; set; }

        [FirestoreProperty]
        public float NoBullRoundRate { get; set; }

        [FirestoreProperty]
        public int BustCount { get; set; }

        [FirestoreProperty]
        public int HighOffTryCount { get; set; }

        [FirestoreProperty]
        public bool IsHighOffSuccess { get; set; }

        [FirestoreProperty]
        public int CheckoutTryCount { get; set; }

        [FirestoreProperty]
        public int HatChanceCount { get; set; }

        [FirestoreProperty]
        public float Target20Rate { get; set; }

        [FirestoreProperty]
        public float Triple20Rate { get; set; }

        [FirestoreProperty]
        public int Over180RoundCount { get; set; }

        [FirestoreProperty]
        public int Over160RoundCount { get; set; }

        [FirestoreProperty]
        public int Over140RoundCount { get; set; }

        [FirestoreProperty]
        public int Over120RoundCount { get; set; }

        [FirestoreProperty]
        public int Over100RoundCount { get; set; }

        [FirestoreProperty]
        public int Over80RoundCount { get; set; }

        [FirestoreProperty]
        public int Over60RoundCount { get; set; }

        [FirestoreProperty]
        public int Over40RoundCount { get; set; }

        [FirestoreProperty]
        public int Over20RoundCount { get; set; }

        [FirestoreProperty]
        public int Under19RoundCount { get; set; }

        [FirestoreProperty]
        public int Ton80ChanceCount { get; set; }

        [FirestoreProperty]
        public float FirstNineDartsPPR { get; set; }

        [FirestoreProperty]
        public int CheckOutNumber { get; set; }

        [FirestoreProperty]
        public float CheckOutRate { get; set; }

        [FirestoreProperty]
        public RoundEntity[] Rounds { get; set; }

        [FirestoreProperty]
        public Dictionary<string, int> AwardCounts { get; set; }

        [FirestoreProperty]
        public int SBullCount { get; set; }

        [FirestoreProperty]
        public int DBullCount { get; set; }

        [FirestoreProperty]
        public int T20Count { get; set; }

        [FirestoreProperty]
        public Dictionary<string, float> HitRateDic { get; set; }

        [FirestoreProperty]
        public Dictionary<string, int> CheckoutDoubleTryCountDic { get; set; }

        public ZeroOneAnalysisData ToModel(GameCode gameCode, Func<PlayerId, grandartslogic.Player> getPlayer)
        {
            return new ZeroOneAnalysisData(
                stats80: Stats80,
                stats100: Stats100,
                throwCount: ThrowCount,
                roundCount: RoundCount,
                roundSummary: Rounds.Select(r => r.ToZeroOneRoundSummary(getPlayer, gameCode)).ToArray(),
                bullrates: new[] {
                    BullRate,
                    OuterBullRate,
                    InnerBullRate,
                    InnerBullCountDevidedByWholeBullCountRate,
                    NoBullRoundRate
                },
                bustCount: BustCount,
                highOffTryCount: HighOffTryCount,
                isHighOffSuccess: IsHighOffSuccess,
                checkoutTryCount: CheckoutTryCount,
                hatChanceCount: HatChanceCount,
                target20Rate: Target20Rate,
                triple20Rate: Triple20Rate,
                over180RoundCount: Over180RoundCount,
                over160RoundCount: Over160RoundCount,
                over140RoundCount: Over140RoundCount,
                over120RoundCount: Over120RoundCount,
                over100RoundCount: Over100RoundCount,
                over80RoundCount: Over80RoundCount,
                over60RoundCount: Over60RoundCount,
                over40RoundCount: Over40RoundCount,
                over20RoundCount: Over20RoundCount,
                under19RoundCount: Under19RoundCount,
                ton80ChanceCount: Ton80ChanceCount,
                firstNineDartsScore: FirstNineDartsPPR,
                checkOutCount: CheckOutNumber,
                checkOutDoubleTryCountDic: CheckoutDoubleTryCountDic.ToDictionary(
                    kv => int.Parse(kv.Key),
                    kv => kv.Value
                ),
                hitRateDic: HitRateDic.ToDictionary(
                    kv => Segment.FromSegmentCode(Enum.Parse<SegmentCode>(kv.Key)),
                    kv => kv.Value
                ),
                awardCounts: AwardCounts.ToDictionary(
                    kv => Enum.Parse<Award>(kv.Key),
                    kv => kv.Value
                ),
                sBullCount: SBullCount,
                dBullCount: DBullCount,
                t20Count: T20Count
            );
        }

        public static ZeroOneAnalysisEntity Create(ZeroOneAnalysisData data)
        {
            return new ZeroOneAnalysisEntity()
            {
                Stats80 = data.Stats80,
                Stats100 = data.Stats100,
                ThrowCount = data.ThrowCount,
                RoundCount = data.RoundCount,

                BullRate = data.BullRate,
                OuterBullRate = data.OuterBullRate,
                InnerBullRate = data.InnerBullRate,
                InnerBullCountDevidedByWholeBullCountRate = data.InnerBullCountDevidedByWholeBullCountRate,

                NoBullRoundRate = data.NoBullRoundRate,

                BustCount = data.BustCount,

                HighOffTryCount = data.HighOffTryCount,
                IsHighOffSuccess = data.IsHighOffSuccess,

                CheckoutTryCount = data.CheckoutTryCount,

                HatChanceCount = data.HatChanceCount,

                Target20Rate = data.Target20Rate,
                Triple20Rate = data.Triple20Rate,

                Over180RoundCount = data.Over180RoundCount,
                Over160RoundCount = data.Over160RoundCount,
                Over140RoundCount = data.Over140RoundCount,
                Over120RoundCount = data.Over120RoundCount,
                Over100RoundCount = data.Over100RoundCount,
                Over80RoundCount = data.Over80RoundCount,
                Over60RoundCount = data.Over60RoundCount,
                Over40RoundCount = data.Over40RoundCount,
                Over20RoundCount = data.Over20RoundCount,
                Under19RoundCount = data.Under19RoundCount,

                Ton80ChanceCount = data.Ton80ChanceCount,

                FirstNineDartsPPR = data.FirstNineDartsPPR,

                CheckOutNumber = data.CheckOutCount,
                CheckOutRate = data.CheckOutRate,
                Rounds = data.RoundSummary.Select(RoundEntity.Create).ToArray(),
                AwardCounts = data.AwardCounts.ToDictionary(
                    kv => Enum.GetName(typeof(Award), kv.Key),
                    kv => kv.Value
                ),
                SBullCount = data.SbullCount,
                DBullCount = data.DBullCount,
                T20Count = data.T20Count,
                HitRateDic = data.HitRateDic.ToDictionary(
                    kv => Enum.GetName(typeof(SegmentCode), kv.Key.Code),
                    kv => kv.Value
                ),
                CheckoutDoubleTryCountDic = data.CheckOutDoubleTryCountDic.ToDictionary(
                    kv => kv.Key.ToString(),
                    kv => kv.Value
                )
            };
        }
    }
}