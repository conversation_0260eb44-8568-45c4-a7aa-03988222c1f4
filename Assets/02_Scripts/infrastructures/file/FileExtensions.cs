using System.IO;
using com.luxza.granlog;

namespace com.luxza.grandarts.infrastructures.file {
    public static class FileExtensions
    {
        public static bool IsFileLocked(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return false;
            }
            FileStream fs = null;
            try
            {
                fs = new FileStream((filePath), FileMode.Open, FileAccess.Write, FileShare.None);
            }
            catch (IOException ex)
            {
                Log.w(ex.Message);
                //the file is unavailable because it is:
                //still being written to
                //or being processed by another thread
                //or does not exist (has already been processed)
                return true;
            }
            finally
            {
                if (fs != null)
                {
                    fs.Close();
                    fs.Dispose();
                    fs = null;
                }
            }

            //file is not locked
            return false;
        }
    }
}