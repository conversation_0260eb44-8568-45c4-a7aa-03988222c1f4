using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.player;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.infrastructures.file.player {
    public class PPInstantPlayerRepository : IInstantPlayerRepository {
        private const string KEY_BOARD_155 = "InstantPlayers_155inch";
        private const string KEY_BOARD_132 = "InstantPlayers_132inch";
        private const string KEY_BOARD_Steel = "InstantPlayers_Steel";

        public UniTask SaveAsync(IPlayer player, CancellationToken cancellationToken) {
            var p = PlayerPrefs.GetString(GetKey(player.BoardSize));
            var players = new List<string>(p.Split(DIVIDER))
            {
                string.Format(SAVE_FORMAT, player.Id.Value, player.GranId.Value, player.Name)
            };

            players.RemoveAll(string.IsNullOrEmpty);
            PlayerPrefs.SetString(GetKey(player.BoardSize), string.Join(DIVIDER, players));
            return UniTask.CompletedTask;
        }

        public UniTask<ReadonlyPlayer> GetAsync(PlayerId playerId, BoardSize boardSize, CancellationToken cancellationToken) {
            var p = PlayerPrefs.GetString(GetKey(boardSize));
            if(string.IsNullOrEmpty(p)) {
                throw new PlayerNotFound();
            }
            var players = p.Split(DIVIDER);
            foreach (var player in players) {
                var data = player.Split(DATA_DIVIDER);
                if(int.TryParse(data[0], out var id)) {
                    if(id == playerId.Value) {
                        return UniTask.FromResult(CreateInstatPlayer(id, data[1],data[2], boardSize));
                    }
                }
            }

            throw new PlayerNotFound();
        }

        public UniTask<IEnumerable<ReadonlyPlayer>> GetAllAsync(BoardSize boardSize, CancellationToken cancellationToken) {
            var p = PlayerPrefs.GetString(GetKey(boardSize));
            if(string.IsNullOrEmpty(p)) {
                return UniTask.FromResult(Enumerable.Empty<ReadonlyPlayer>());
            }
            var players = p.Split(DIVIDER);
            var instantPlayers = players.Select(player =>
            {
                var data = player.Split(DATA_DIVIDER);
                if (int.TryParse(data[0], out var id))
                {
                    return CreateInstatPlayer(id, data[1], data[2], boardSize);
                }

                throw new SystemException("Player id is not integer.");
            });
            return UniTask.FromResult(instantPlayers);
        }

        public UniTask RemoveAsync(PlayerId playerId, BoardSize boardSize, CancellationToken cancellationToken) {
            var p = PlayerPrefs.GetString(GetKey(boardSize));
            if(string.IsNullOrEmpty(p)) {
                return UniTask.CompletedTask;
            }
            var players = new List<string>(p.Split(DIVIDER));

            players.RemoveAll(player =>
            {
                var data = player.Split(DATA_DIVIDER);
                if (int.TryParse(data[0], out var id))
                {
                    return id == playerId.Value;
                }

                return false;
            });

            PlayerPrefs.SetString(GetKey(boardSize), string.Join(DIVIDER, players));
            return UniTask.CompletedTask;
        }

        public UniTask ClearAllAsync(CancellationToken cancellationToken) {
            PlayerPrefs.DeleteKey(KEY_BOARD_155);
            PlayerPrefs.DeleteKey(KEY_BOARD_132);
            PlayerPrefs.DeleteKey(KEY_BOARD_Steel);
            return UniTask.CompletedTask;
        }

        private string GetKey(BoardSize boardSize) {
            return boardSize switch {
                BoardSize.Inch155 => KEY_BOARD_155,
                BoardSize.Inch132 => KEY_BOARD_132,
                BoardSize.Steel => KEY_BOARD_Steel,
                _ => throw new ArgumentOutOfRangeException(nameof(boardSize), boardSize, null)
            };
        }

        private ReadonlyPlayer CreateInstatPlayer(int id, string granId, string  name, BoardSize boardSize) {
            var instantPlayerId = PlayerId.IssueInstantPlayerId(id);
            return new ReadonlyPlayer(instantPlayerId,
                                      granId,
                                      name,
                                      "",
                                      boardSize,
                                      RegionInfo.CurrentRegion);

        }

        private const string SAVE_FORMAT = "{0}|{1}|{2}"; // id|granId|name
        private const string DIVIDER = ":";
        private const string DATA_DIVIDER = "|";
    }
}