using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.guest
{
    internal class GenerateQRCodeRequest : IGraphQLRequest<string>
    {
        private readonly object _parameter;

        public GenerateQRCodeRequest(PlayerId playerId) {
            _parameter = new {
                input = new {
                    playerId = playerId.Value
                }
            };
        }

        public async UniTask<string> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }

            var response = await client.PostAsync<GenerateQRCodeResponse>(
                new GenerateQRCodeAPI(),
                _parameter,
                auth.accessToken,
                cancellationToken
            );
            
            return response.request_qr_code;
        }

        class GenerateQRCodeResponse : IGraphQLResponse{
            public string request_qr_code;
        }

        class GenerateQRCodeAPI : API
        {
            public override string Name => "GenerateQRCode";
        }
    }
}