using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.gen;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.user
{
    public class ResetPasswordRequest : IGraphQLRequest<bool>
    {
        private readonly object _parameter;
        
        public ResetPasswordRequest(EmailAddress emailAddress) {
            _parameter = new {
                data = new {
                    email = emailAddress.Value
                }
            };
        }
        
        public async UniTask<bool> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);
            
            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }
            
            var response = await client.PostAsync<ResetPasswordResponse>(
                new ResetPasswordAPI(),
                _parameter,
                auth.accessToken,
                cancellationToken
            );
            return response.reset_password.isSuccess;
        }
        
        class ResetPasswordResponse : IGraphQLResponse {
            public Types.Confirmation reset_password;
        }
        
        class ResetPasswordAPI : API {
            public override string Name => "ResetPassword";
        }
    }
}