using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.language;
using com.luxza.grandarts.domains.user;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.exceptions;
using com.luxza.unitygraphqlclient.gen;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.user
{
    public class ChangeEmailAddressRequest : IGraphQLRequest<bool>
    {
        private readonly object _parameter;
        
        public ChangeEmailAddressRequest(EmailAddress emailAddress) {
            _parameter = new {
                input = new {
                    language_code = ApplicationAuth.LoggedInUser.Language.Code(),
                    newEmail = emailAddress.Value,
                    password = ApplicationAuth.LoggedInUser.Password.Value
                }
            };
        }
        
        public async UniTask<bool> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);
            
            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }
            
            try {
                var response = await client.PostAsync<RequestChangeEmailResponse>(
                    new RequestChangeEmailAPI(),
                    _parameter,
                    auth.accessToken,
                    cancellationToken
                );
                return response.request_change_email == Types.SuccessResponse.success;
            } catch (GraphQLError err) {
                if(err.Response.ErrorCodes.Contains("UserAlreadyExistError")) {
                    throw new AlreadyExist();
                }

                throw;
            }
        }
        
        class RequestChangeEmailResponse : IGraphQLResponse {
            public Types.SuccessResponse request_change_email;
        }
        
        class RequestChangeEmailAPI : API {
            public override string Name => "RequestChangeEmail";
        }
    }
}