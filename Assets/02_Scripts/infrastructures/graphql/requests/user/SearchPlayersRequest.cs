using System.Collections.Generic;
using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.infrastructures.graphql.converter;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.gen;
using GraphQLBoardSize = com.luxza.unitygraphqlclient.gen.Types.BoardSize;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.player
{
    public class SearchPlayersRequest : IGraphQLRequest<(List<IPlayer> players,int endCursor,bool hasNextPage)>
    {
        private readonly object _parameter;
        private int _itemCountPerPage = 20;
        private int _startCursor;

        public SearchPlayersRequest(int userId, string searchString, string countryCodeText, string rankText, int startCursor)
        {
            _parameter = new {
                input = new {
                    searchString = string.IsNullOrEmpty(searchString) ? string.Empty : searchString,
                    rank = string.IsNullOrEmpty(rankText) ? null : rankText,
                    countryCode = string.IsNullOrEmpty(countryCodeText) ? null : countryCodeText,
                    ownUserId = userId,
                    boardSizes = new[] { GraphQLBoardSize.SoftTip132, GraphQLBoardSize.SteelTip },
                    args = new {
                        first = _itemCountPerPage,
                        after = startCursor.ToString()
                    }
                }
            };
        }

        public async UniTask<(List<IPlayer> players,int endCursor,bool hasNextPage)> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }

            var response = await client.PostAsync<GetSearchPlayersResponse>(
                new GetSearchPlayersAPI(),
                _parameter,
                auth.accessToken,
                cancellationToken
            );
            
            var players = new List<IPlayer>();
            foreach (var item in response.players.edges)
            {
                var player = item.node.ToModelReadonlyPlayer();
                players.Add(player);
            }
            var cursor = response.players.pageInfo.endCursor.Equals("null")
                ? 0
                : int.Parse(response.players.pageInfo.endCursor);
            return (players, cursor, response.players.pageInfo.hasNextPage);
        }

        class GetSearchPlayersResponse : IGraphQLResponse{
            public Types.PlayerConnection players;
        }

        class GetSearchPlayersAPI : API
        {
            public override string Name => "SearchPlayers";
        }
    }
}