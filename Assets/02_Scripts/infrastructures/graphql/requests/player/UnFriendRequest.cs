using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.usecases.auth;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.gen;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.requests.player
{
    public class UnFriendRequest : IGraphQLRequest<bool>
    {
        private readonly object _parameter;
        private readonly PlayerId _targetPlayerId;

        public UnFriendRequest(PlayerId ownPlayerId, PlayerId targetPlayerId)
        {
            _targetPlayerId = targetPlayerId;
            _parameter = new {
                input = new {
                    ownPlayerId = ownPlayerId.Value,
                    targetPlayerId = targetPlayerId.Value
                }
            };
        }

        public async UniTask<bool> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new TimeOutDecorator(client);
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            if(!ApplicationAuth.IsLoggedIn) {
                throw new SessionTimeOutException("Not logged in");
            }

            if(ApplicationAuth.LoggedInUser == null) {
                throw new SessionTimeOutException("User is null");
            }

            var response = await client.PostAsync<UnFriendResponse>(
                new UnFriendAPI(),
                _parameter,
                auth.accessToken,
                cancellationToken
            );
            return response.unfollow.id == _targetPlayerId.Value;
        }

        class UnFriendResponse : IGraphQLResponse{
            public Types.Player unfollow;
        }

        class UnFriendAPI : API
        {
            public override string Name => "UnFriend";
        }
    }
}