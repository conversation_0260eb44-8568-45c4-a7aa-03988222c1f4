using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.exceptions;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.grandarts.infrastructures.graphql.converter;

namespace com.luxza.grandarts.infrastructures.graphql.requests.playersetting {
    public class UpdateGamePlaySettingRequest : IGraphQLRequest<PlayerSetting>
    {
        private readonly object _parameter;
        private PlayerId _playerId;
        public UpdateGamePlaySettingRequest(PlayerId playerId,PlayerSetting playerSetting) {
            _playerId = playerId;
            _parameter = new
            {
                input = new
                {
                    playerId = playerId.Value,
                    steelTipZeroOneInputMode = playerSetting.RoundInputType.ToGraphQL().ToString(),
                    isReadingAloudEnabled = playerSetting.EnableCaller,
                    steelTipZeroOneProMode = playerSetting.EnableProMode
                }
            };
        }
        public async UniTask<PlayerSetting> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            try {
                var res = await client.PostAsync<UpdateGamePlaySettingResponse>(
                    new UpdateGamePlaySettingAPI(),
                    _parameter,
                    auth.accessToken,
                    cancellationToken
                );
                return res.update_player_app_settings_v2.ToModel(_playerId);
            } catch (GraphQLError e) {
                if(e.Response.ErrorCodes.Contains("PlayerNotFound")) {
                    throw new PlayerNotFound("Player not found", e);
                }

                throw;
            }
        }

        class UpdateGamePlaySettingAPI: API
        {
            public override string Name => "UpdateGamePlaySetting";
        }

        private class UpdateGamePlaySettingResponse : IGraphQLResponse
        {
            public unitygraphqlclient.gen.Types.AppSettings update_player_app_settings_v2;
        }
    }
}