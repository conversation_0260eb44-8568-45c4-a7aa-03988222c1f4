using System.Collections.Generic;
using System.Threading;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.domains.stats;
using com.luxza.grandarts.infrastructures.graphql.requests.playdata;
using com.luxza.grandarts.infrastructures.graphql.requests.player;
using com.luxza.grandarts.infrastructures.graphql.requests.playersetting;
using com.luxza.grandarts.usecases.player;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.grandarts.infrastructures.graphql.services.player {
    public class GraphQLPlayerService : IPlayerService
    {
        public UniTask<float> ReviewPlayerAsync(PlayerId playerId, int review, CancellationToken cancellationToken)
        {
            throw new System.NotImplementedException();
        }

        public UniTask<PlayerSetting> SavePlayerSettingAsync(PlayerId playerId, PlayerSetting playerSetting, CancellationToken cancellationToken)
        {
            throw new System.NotImplementedException();
        }

        UniTask<(
            Stats overall80,
            Stats overall100,
            Stats zerooneFatBull80,
            Stats zerooneFatBull100,
            Stats zerooneSeparatedBull80,
            Stats zerooneSeparatedBull100,
            Stats cricket80,
            Stats cricket100,
            int friendTotal,
            int onlineMatchTotal,
            int goodTotal)> IPlayerService.GetPlayerStatsAsync(PlayerId playerId, CancellationToken cancellationToken)
        {
            return new GetPlayerStatsRequest(playerId).SendAsync(cancellationToken);
        }
        
        public UniTask<PlayerSetting> UpdatePlayerSettingAsync(PlayerId playerId, PlayerSetting playerSetting, CancellationToken cancellationToken)
        {
            return new UpdateGamePlaySettingRequest(playerId,playerSetting).SendAsync(cancellationToken);
        }

        public async UniTask<bool> ReportPlayerAsync(IPlayer sendPlayer, IPlayer reportedPlayer, Texture2D tex, string reportCategory, string description,
            bool hasBlockedTargetPlayer, CancellationToken cancellationToken)
        {
            return await new ReportPlayerRequest(sendPlayer, reportedPlayer, tex, reportCategory, description,
                hasBlockedTargetPlayer).SendAsync(cancellationToken);
        }

        public async UniTask<List<IPlayer>> GetBlockListAsync(PlayerId playerId, string searchString, CancellationToken cancellationToken)
        {
            return await new GetBlockListRequest(playerId, searchString).SendAsync(cancellationToken);
        }

        public async UniTask<bool> BlockFollowAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new BlockFollowRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }

        public async UniTask<bool> UnblockFollowAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new UnblockFollowRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }

        public async UniTask<(int winCount, int loseCount)> WinAndLoseCountAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new WinAndLoseCountRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }

        public async UniTask<(int winCount, int loseCount)> WinLossDrawCountAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new WinLossDrawCountRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }

        public async UniTask<(List<IPlayer> players,int endCursor,bool hasNextPage)> GetFriendListAsync(PlayerId playerId, string searchString, int startCursor, CancellationToken cancellationToken)
        {
            return await new GetFriendListRequest(playerId, searchString, startCursor).SendAsync(cancellationToken);
        }

        public async UniTask<(List<IPlayer> players, int endCursor, bool hasNextPage)> GetFollowerListAsync(PlayerId playerId, string searchString, int startCursor,
            CancellationToken cancellationToken)
        {
            return await new GetFollowerListRequest(playerId, searchString, startCursor).SendAsync(cancellationToken);
        }
        
        public async UniTask<bool> UnFriendAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new UnFriendRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }
        
        public async UniTask<bool> FollowAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new FollowRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }

        public async UniTask<bool> RejectFriendRequestAsync(PlayerId ownPlayerId, PlayerId targetPlayerId, CancellationToken cancellationToken)
        {
            return await new RejectFriendRequestRequest(ownPlayerId, targetPlayerId).SendAsync(cancellationToken);
        }
    }
}