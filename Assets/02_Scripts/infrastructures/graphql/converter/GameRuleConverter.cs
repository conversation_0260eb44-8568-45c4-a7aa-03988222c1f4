using System;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.unitygraphqlclient.gen;

namespace com.luxza.grandarts.infrastructures.graphql.converter {
    public static class GameRuleConverter
    {
        public static Types.ZeroOneInConditionV2 ToGraphQLModel(this InCondition condition)
        {
            return condition switch
            {
                InCondition.OpenIn => Types.ZeroOneInConditionV2.NONE,
                InCondition.DoubleIn => Types.ZeroOneInConditionV2.DOUBLE_IN,
                InCondition.MasterIn => Types.ZeroOneInConditionV2.MASTER_IN,
                _ => throw new NotSupportedException($"{condition} is not suported for graphql schema.")
            };
        }

        public static Types.ZeroOneOutConditionV2 ToGraphQLModel(this OutCondition condition)
        {
            return condition switch
            {
                OutCondition.OpenOut => Types.ZeroOneOutConditionV2.NONE,
                OutCondition.DoubleOut => Types.ZeroOneOutConditionV2.DOUBLE_OUT,
                OutCondition.MasterOut => Types.ZeroOneOutConditionV2.MASTER_OUT,
                _ => throw new NotSupportedException($"{condition} is not suported for graphql schema.")
            };
        }

        public static Types.ZeroOneStartScore ToZeroOneStartScore(this ZeroOneEndScore score)
        {
            return score switch
            {
                ZeroOneEndScore._301 => Types.ZeroOneStartScore.Z301,
                ZeroOneEndScore._501 => Types.ZeroOneStartScore.Z501,
                ZeroOneEndScore._701 => Types.ZeroOneStartScore.Z701,
                ZeroOneEndScore._901 => Types.ZeroOneStartScore.Z901,
                ZeroOneEndScore._1101 => Types.ZeroOneStartScore.Z1101,
                ZeroOneEndScore._1501 => Types.ZeroOneStartScore.Z1501,
                _ => throw new NotSupportedException($"{score} is not suported for graphql schema.")
            };
        }

        public static Types.KickDownGoalScore ToKickdownGoalScore(this ZeroOneEndScore score)
        {
            return score switch
            {
                ZeroOneEndScore._301 => Types.KickDownGoalScore.G301,
                ZeroOneEndScore._501 => Types.KickDownGoalScore.G501,
                _ => throw new NotSupportedException($"{score} is not suported for graphql schema.")
            };
        }

        public static Types.KickDownOverScorePenaltyV2 ToGraphQLModel(this KickDownOverScoreSetting setting)
        {
            return setting switch
            {
                KickDownOverScoreSetting.Random => Types.KickDownOverScorePenaltyV2.Random,
                KickDownOverScoreSetting.Minus100 => Types.KickDownOverScorePenaltyV2.Minus100,
                KickDownOverScoreSetting.Minus150 => Types.KickDownOverScorePenaltyV2.Minus150,
                _ => throw new NotSupportedException($"{setting} is not suported for graphql schema.")
            };
        }

        public static Types.FirstLegThrowOrder ToGraphQLModel(this ThrowOrderAtFirstLeg order)
        {
            return order switch
            {
                ThrowOrderAtFirstLeg.Random => Types.FirstLegThrowOrder.Random,
                ThrowOrderAtFirstLeg.Cork => Types.FirstLegThrowOrder.Cork,
                ThrowOrderAtFirstLeg.Self => Types.FirstLegThrowOrder.RequesterIsFirst,
                ThrowOrderAtFirstLeg.Opponent => Types.FirstLegThrowOrder.ReceiverIsFirst,
                //Other value is not supported because RequesterFirst, ReceiverFirst is based on the throw order of the match.
                _ => throw new NotSupportedException($"{order} is not suported for graphql schema.")
            };
        }
        
        public static Types.TargetTwentyModeV2 ToGraphQLModel(this TargetClearCondition order)
        {
            return order switch
            {
                TargetClearCondition.Hit5 => Types.TargetTwentyModeV2.HIT_5,
                TargetClearCondition.Hit10 => Types.TargetTwentyModeV2.HIT_10,
                TargetClearCondition.Hit30 => Types.TargetTwentyModeV2.HIT_30,
                TargetClearCondition.Hit50 => Types.TargetTwentyModeV2.HIT_50,
                TargetClearCondition.Hit100 => Types.TargetTwentyModeV2.HIT_100,
                TargetClearCondition.Round10 => Types.TargetTwentyModeV2.ROUND_10,
                //Other value is not supported because RequesterFirst, ReceiverFirst is based on the throw order of the match.
                _ => throw new NotSupportedException($"{order} is not suported for graphql schema.")
            };
        }
        
        public static Types.RotationModeV2 ToGraphQLModel(this RotationTargetOption option)
        {
            return option switch
            {
                RotationTargetOption.AllTarget => Types.RotationModeV2.OPEN,
                RotationTargetOption.SingleOnly => Types.RotationModeV2.ONLY_SINGLE,
                RotationTargetOption.DoubleOnly => Types.RotationModeV2.ONLY_DOUBLE,
                RotationTargetOption.TripleOnly => Types.RotationModeV2.ONLY_TRIPLE,
                //Other value is not supported because RequesterFirst, ReceiverFirst is based on the throw order of the match.
                _ => throw new NotSupportedException($"{option} is not suported for graphql schema.")
            };
        }
    }
}