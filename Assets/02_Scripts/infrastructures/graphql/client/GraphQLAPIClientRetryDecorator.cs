using System;
using System.Threading;
using com.luxza.grandarts.domains.user;
using com.luxza.granlog;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.exceptions;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.client
{
    public class GraphQLAPIClientRetryDecorator : IGraphQLAPIDecorator
    {
        private readonly RetryPolicy _retryPolicy;
        private readonly IGraphQLAPIDecorator _decorator;

        private int _retryCount = 0;

        public GraphQLAPIClientRetryDecorator(IGraphQLAPIDecorator decorator)
        {
            _decorator = decorator;
            _retryPolicy = RetryPolicy.DefaultPolicy;
        }

        public GraphQLAPIClientRetryDecorator(IGraphQLAPIDecorator decorator, RetryPolicy retryPolicy)
        {
            _decorator = decorator;
            _retryPolicy = retryPolicy;
        }

        public GraphQLAPIClient Client => _decorator.Client;

        public async UniTask<T> PostAsync<T>(
            API api,
            object args,
            AuthToken accessToken,
            CancellationToken cancellationToken)
        where T : IGraphQLResponse
        {
            try {
                return await _decorator.PostAsync<T>(api, args, accessToken, cancellationToken);
            } catch (Exception e)
                when
                (
                    e is TimeoutException ||
                    e is UnityWebRequestException
                ) {
                if (_retryPolicy.MaxRetryCount < _retryCount)
                {
                    if (e is TimeoutException)
                    {
                        Log.i("Request was timeout. We will try send again!");
                    }
                    else
                    {
                        Log.i($"Request was failed with {e.Message}. We will try send again!");
                    }
                    await UniTask.Delay(_retryPolicy.Intervals[_retryCount], cancellationToken: cancellationToken);
                    _retryCount++;
                    return await PostAsync<T>(api, args, accessToken, cancellationToken);
                }
                else
                {
                    throw;
                }
            }
        }
    }
}