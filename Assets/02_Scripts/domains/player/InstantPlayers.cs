using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.utils;
using com.luxza.granlog;
using ObservableCollections;
using R3;

namespace com.luxza.grandarts.domains.player {
    public class InstantPlayers {
        public const int MaxCount = 8;

        ObservableList<ReadonlyPlayer> _instantPlayers = new();

        public Observable<CollectionAddEvent<ReadonlyPlayer>> onAdded => _instantPlayers.ObserveAdd();
        public Observable<CollectionRemoveEvent<ReadonlyPlayer>> onRemoved => _instantPlayers.ObserveRemove();
        public Observable<CollectionReplaceEvent<ReadonlyPlayer>> onReplaced => _instantPlayers.ObserveReplace();
        public Observable<CollectionResetEvent<ReadonlyPlayer>> onCleared => _instantPlayers.ObserveReset();

        public Observable<int> onCountChanged => _instantPlayers.ObserveCountChanged();

        public IEnumerable<ReadonlyPlayer> Items => _instantPlayers;

        public int Count => _instantPlayers.Count();

        public ReadonlyPlayer Add(string name, BoardSize boardSize, PlayerSetting playerSetting = null) {
            if (_instantPlayers.Count >= MaxCount) {
                throw new InstantPlayerCountHasBeenReachedMax();
            }

            if(string.IsNullOrEmpty(name) || string.IsNullOrWhiteSpace(name)) {
                throw new ArgumentException("Player name is required.");
            }

            var id = PlayerId.IssueInstantPlayerId(_instantPlayers.Count + 1);
            var player = new ReadonlyPlayer(id, GranId.IssueInstantGranId(), name, "", boardSize, RegionInfo.CurrentRegion, setting: playerSetting);
            _instantPlayers.Add(player);
            return player;
        }

        public bool TryAdd(string name, BoardSize boardSize, out ReadonlyPlayer player, PlayerSetting playerSetting = null) {
            if (_instantPlayers.Count >= MaxCount) {
                player = null;
                return false;
            }

            if(string.IsNullOrEmpty(name)) {
                Log.w("Player name is required.");
                player = null;
                return false;
            }

            var id = PlayerId.IssueInstantPlayerId(_instantPlayers.Count + 1);
            player = new ReadonlyPlayer(id, GranId.IssueInstantGranId(), name, "", boardSize, RegionInfo.CurrentRegion, playerSetting);
            _instantPlayers.Add(player);
            return true;
        }

        public void Remove(PlayerId playerId) {
            if(_instantPlayers.Count == 0) {
                throw new InvalidOperationException("Instant player is empty.");
            }
            var i = _instantPlayers.FirstIndex(p => p.Id == playerId);
            if (i == -1) {
                throw new PlayerNotFound();
            }

            _instantPlayers.RemoveAt(i);
        }

        public void Remove(ReadonlyPlayer player) {
            if(_instantPlayers.Count == 0) {
                throw new InvalidOperationException("Instant player is empty.");
            }
            var i = _instantPlayers.FirstIndex(p => p.Id == player.Id);
            if (i == -1) {
                throw new PlayerNotFound();
            }

            _instantPlayers.RemoveAt(i);
        }

        public bool TryRemove(PlayerId playerId, out PlayerId removedPlayerId) {
            if(_instantPlayers.Count == 0) {
                removedPlayerId = null;
                return false;
            }
            var i = _instantPlayers.FirstIndex(p => p.Id == playerId);
            if (i == -1) {
                removedPlayerId = null;
                return false;
            }

            _instantPlayers.RemoveAt(i);
            removedPlayerId = playerId;
            return true;
        }

        public bool TryRemove(ReadonlyPlayer player, out PlayerId removedPlayerId) {
            if(_instantPlayers.Count == 0) {
                removedPlayerId = null;
                return false;
            }
            var i = _instantPlayers.FirstIndex(p => p.Id == player.Id);
            if (i == -1) {
                removedPlayerId = null;
                return false;
            }

            _instantPlayers.RemoveAt(i);
            removedPlayerId = player.Id;
            return true;
        }


        public void Clear() {
            _instantPlayers.Clear();
        }

        public void ReplaceAll(IEnumerable<ReadonlyPlayer> instantPlayers) {
            if(instantPlayers.Count() > MaxCount) {
                throw new InstantPlayerCountHasBeenReachedMax();
            }
            _instantPlayers.Clear();
            _instantPlayers.AddRange(instantPlayers);
        }

        public void ReplaceAllPlayerSetting(PlayerSetting playerSettings) {
            var newInstantPlayers = new List<ReadonlyPlayer>();
            foreach (var item in _instantPlayers) {
                var player = new ReadonlyPlayer(item.Id, item.GranId, item.Name, item.IconURL, item.BoardSize, item.Region, playerSettings);
                newInstantPlayers.Add(player);
            }
            _instantPlayers.Clear();
            _instantPlayers.AddRange(newInstantPlayers);
        }

        public bool IsItemsReachedTheLimit() {
            return _instantPlayers.Count >= MaxCount;
        }
    }
}