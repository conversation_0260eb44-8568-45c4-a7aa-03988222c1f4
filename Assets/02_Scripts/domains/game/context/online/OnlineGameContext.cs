using com.luxza.grandarts.domains.game.context.component;
using com.luxza.grandarts.domains.online.lobby;
using com.luxza.grandarts.domains.online.videochat;

namespace com.luxza.grandarts.domains.game.context.online
{
    public class OnlineGameContext
    {
        public readonly OnlineGameDataRetriver DataRetriver;
        public readonly IOnlineMatchClient OnlineMatchClient;
        public readonly IVideoChatClient VideoChatClient;

        public readonly OnlineMatchServerEventListener OnlineMatchServerEventListener;
        public readonly MatchRoom MatchRoom;

        public OnlineGameContext(
            MatchRoom matchRoom,
            OnlineGameDataRetriver dataRetriver,
            IOnlineMatchClient onlineMatchClient,
            IVideoChatClient videoChatClient,
            OnlineMatchServerEventListener onlineMatchServerEventListener)
        {
            MatchRoom = matchRoom;
            DataRetriver = dataRetriver;
            OnlineMatchClient = onlineMatchClient;
            VideoChatClient = videoChatClient;
            OnlineMatchServerEventListener = onlineMatchServerEventListener;
        }

        public void StartVideoChat()
        {
            //TODO: supports remote doubles.
            VideoChatClient.Join(MatchRoom.Id,
                                 DataRetriver.MyUnit.HostGranId,
                                 DataRetriver.GetMyDeviceCamFacing());
        }
    }
}