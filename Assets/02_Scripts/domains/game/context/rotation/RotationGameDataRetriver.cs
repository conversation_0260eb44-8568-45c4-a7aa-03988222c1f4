using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.game.context.rotation
{
    public class RotationGameDataRetriver : IGameDataRetriver<GameRuleRotation>
    {
        public BasicGameDataRetriver BasicGameDataRetriver { get; }

        private readonly RefereeRotation _referee;

        public RotationGameDataRetriver(RefereeRotation referee, PlayUnit[] units)
        {
            BasicGameDataRetriver = new BasicGameDataRetriver(referee, units);
            _referee = referee;
        }

        public GameRuleRotation GameRule => _referee.Match.Rule;
        
        public bool IsReachGameEnd => _referee.IsReachGameEnd;

        public void Dispose()
        {
            BasicGameDataRetriver.Dispose();
        }

        public bool TryGetLatestVirtualScoreBySegment(out int score)
        {
            if(BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                if (segmentInput.LatestThrow != null)
                {
                    if (segmentInput.LatestThrow.IsEmpty)
                    {
                        score = 0;
                        return false;
                    }
                    else
                    {
                        score = segmentInput.LatestThrow.VirtualHitArea.BasicScore;
                        return true;
                    }
                }
                score = 0;
                return false;
            }
            else
            {
                score = 0;
                return false;
            }
        }
        
        public int CurrentTargetPositionCode =>
            _referee.Match.Rule.TargetCodes[_referee.Scorer.IndexCurrentTarget(_referee.CurrentThrowingUnitId)];
        public int CurrentUnitBullSuccessCount => _referee.Scorer.BullSucceedCount(_referee.CurrentThrowingUnitId);
        public double UnitContinuousRoundRate => _referee.Scorer.UnitContinuousRoundRate(_referee.CurrentThrowingUnitId);
    }
}