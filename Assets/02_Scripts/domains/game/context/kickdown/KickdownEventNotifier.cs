using System;
using R3;

namespace com.luxza.grandarts.domains.game.context.kickdown
{
    public class KickdownEventNotifier : IDisposable
    {
        private Subject<Unit> _onKickDownReceived = new();
        public Observable<Unit> OnKickDownReceived => _onKickDownReceived;

        public void NotifyKickDownReceived()
        {
            _onKickDownReceived.OnNext(Unit.Default);
        }

        public void Dispose()
        {
            _onKickDownReceived?.Dispose();
            _onKickDownReceived = null;
        }
    }
}