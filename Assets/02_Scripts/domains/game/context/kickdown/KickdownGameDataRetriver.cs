using System.Linq;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.game.context.kickdown
{
    public sealed class KickdownGameDataRetriver : IGameDataRetriver<GameRuleKickDown>
    {
        private readonly RefereeKickDown _referee;
        public BasicGameDataRetriver BasicGameDataRetriver { get; }

        public KickdownGameDataRetriver(RefereeKickDown referee, PlayUnit[] units)
        {
            _referee = referee;
            BasicGameDataRetriver = new BasicGameDataRetriver(referee, units);
        }

        public float Latest3DartsAvg(UnitId unitId) {
            var unit = _referee.Participants.Unit(unitId.Value.ToString());
            if (!unit.Progress.HasRounds) return 0;
            return unit.AllMember.Average(m =>
            {
                return (float?)_referee.Scorer.RealTimeStatsPPR80(m.GranId) ?? 0;
            });
        }

        public bool IsOverScore => _referee.CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().IsBust;
        public float CurrentThrowingUnit3DartsAvg() => Latest3DartsAvg(BasicGameDataRetriver.CurrentThrowingUnitId);

        public GameRuleKickDown GameRule => _referee.Match.Rule;

        public bool IsWinnerDecided => _referee.AnyUnitHasReachedEndScore;

        public void Dispose()
        {
            BasicGameDataRetriver.Dispose();
        }
    }
}