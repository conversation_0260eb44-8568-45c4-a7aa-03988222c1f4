using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.cpu;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.game.context.standardcr
{
    public class StandardCrGameDataRetriver : IGameDataRetriver<GameRuleStandardCR>
    {
        private readonly RefereeStandardCR _referee;
        public BasicGameDataRetriver BasicGameDataRetriver { get; }

        public StandardCrGameDataRetriver(RefereeStandardCR referee, PlayUnit[] units)
        {
            _referee = referee;
            BasicGameDataRetriver = new BasicGameDataRetriver(referee, units);
        }

        public StandardCrGameDataRetriver(RefereeStandardCR referee, PlayUnit[] units, ICPU[] cpus)
        {
            _referee = referee;
            BasicGameDataRetriver = new BasicGameDataRetriver(referee, units, cpus);
        }

        public GameRuleStandardCR GameRule => _referee.Match.Rule;

        public CricketMarkPosition[] TargetPositions => _referee.Match.TargetPositions;

        public Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> CurrentThorwingUnitMarkDic => _referee.UnitMarkDic;

        public Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> UnitMarks(UnitId unitId)
        {
            return _referee.TotalMarksCounts(unitId.ToString());
        }

        public int TotalDisplayMarkCount(UnitId unitId)
        {
            return _referee.Scorer.DisplayMarkTotalCount(unitId.StringValue);
        }

        public int MaxTotalDisplayMarkCount
        {
            get
            {
                return _referee.Match.ParticipantTeams.AllUnits.Max(u => _referee.Scorer.DisplayMarkTotalCount(u.Id));
            }
        }

        public int CurrentRoundMarkCount => _referee.Scorer.MarkCountAtRound(_referee.CurrentThrowingUnitId, _referee.CurrentRoundAtCurrentTeam.No - 1);

        public int LatestThrowMarkCount
        {
            get
            {
                if (BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
                {
                    return _referee.Scorer.MarkCount(segmentInput.LatestThrow);
                }

                return 0;
            }
        }

        public int GetMarkCountByPosition(UnitId unitId, Segment segment)
        {
            return _referee.Scorer.MarkCountInConsiderationOfClosing(unitId.StringValue, segment);
        }

        public Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> UnitMarkDic => _referee.UnitMarkDic;
        public bool IsClosed(CricketMarkPosition pos) => _referee.Scorer.IsClosed(pos);
        public int DisplayMarkCountByPosition(UnitId unitId, CricketMarkPosition pos) => _referee.Scorer.DisplayMarkCountByPosition(unitId.ToString(), pos);
        public bool IsWinnerDesided => _referee.TotalMarkAtCurrentThrowingTeam >= 7 && _referee.TotalScoreAtCurrentThrowingTeam == _referee.Match.ParticipantTeams.AllUnits.Max(unit => _referee.CurrentScore(unit.Id));

        public bool TryGetFirstOpenedByWithoutClosed(UnitId unitId, CricketMarkPosition[] aimOrder, out CricketMarkPosition position)
        {
            foreach (var pos in aimOrder)
            {
                if (_referee.Scorer.IsOpenedBy(unitId.StringValue, pos) && !_referee.Scorer.IsClosed(pos))
                {
                    position = pos;
                    return true;
                }
            }
            position = CricketMarkPosition.None;
            return false;
        }

        public bool TryGetFirstNotOpenedBy(UnitId unitId, CricketMarkPosition[] aimOrder, out CricketMarkPosition position)
        {
            foreach (var pos in aimOrder)
            {
                if (!_referee.Scorer.IsOpenedBy(unitId.StringValue, pos))
                {
                    position = pos;
                    return true;
                }
            }
            position = CricketMarkPosition.None;
            return false;
        }

        public bool TryGetFirstOpenedByOpponentOnly(UnitId unitId, CricketMarkPosition[] aimOrder, out CricketMarkPosition position)
        {
            foreach (var pos in aimOrder)
            {
                if (_referee.Scorer.IsOpenedBy(unitId.StringValue, pos)) continue;
                foreach (var unit in _referee.Participants.AllUnits)
                {
                    if (unit.IdAsInt == unitId.Value) continue;
                    if (_referee.Scorer.IsOpenedBy(unit.Id, pos))
                    {
                        position = pos;
                        return true;
                    }
                }
            }
            position = CricketMarkPosition.None;
            return false;
        }

        public float Latest3DartsAvg(UnitId unitId)
        {
            return (float?)_referee.Scorer.RealtimeStats(_referee.Match.ParticipantTeams.AllUnits.First(u => u.IdAsInt == unitId.Value).CurrentThrower.GranId) ?? 0;
        }

        public float Latest3DartsAvg(GranId granId)
        {
            return (float?)_referee.Scorer.RealtimeStats(granId) ?? 0;
        }

        public int MaxScore()
        {
            return BasicGameDataRetriver.ScoresOfAllUnits.Max(u => u.score);
        }

        public int MaxScoreExcludeSelf(UnitId selfUnitId)
        {
            return BasicGameDataRetriver.ScoresOfAllUnits
                .Where(u => u.unitId != selfUnitId)
                .Max(u => u.score);
        }

        public int TotalDisplayMarksByOpponents(CricketMarkPosition pos, UnitId selfUnitId)
        {
            return _referee.Participants.AllUnits
                .Where(u => u.Id != selfUnitId.StringValue)
                .Sum(u => _referee.Scorer.DisplayMarkCountByPosition(u.Id, pos));
        }

        public CricketMarkPosition[] GetOpenedBy(UnitId unitId)
        {
            return _referee.Match.Rule.TargetPositions
                .Where(pos => _referee.Scorer.IsOpenedBy(unitId.StringValue, pos))
                .ToArray();
        }

        public CricketMarkPosition[] GetOpenedByExcludeSelfOpened(UnitId selfUnitId, UnitId opponentUnitId)
        {
            return _referee.Match.Rule.TargetPositions
                .Where(pos => _referee.Scorer.IsOpenedBy(opponentUnitId.StringValue, pos) && !_referee.Scorer.IsOpenedBy(selfUnitId.StringValue, pos))
                .ToArray();
        }

        public void Dispose()
        {
            BasicGameDataRetriver.Dispose();
        }
    }
}