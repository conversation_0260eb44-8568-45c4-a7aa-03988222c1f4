using com.luxza.grandarts.domains.game.entry;
using com.luxza.grandarts.domains.player;

namespace com.luxza.grandarts.domains.game.format
{
    public class HalfItPlayFormat
    {

        public UnitFormat UnitFormat => EntrySlots.UnitFormat;
        public readonly EntrySlots EntrySlots;
        public readonly HalfItRequestFormat RequestFormat;

        public HalfItPlayFormat(BoardSize boardSize)
        {
            RequestFormat = HalfItRequestFormat.Default(boardSize);
            EntrySlots = new EntrySlots();
        }

        public HalfItPlayFormat(EntrySlots slots, BoardSize boardSize)
        {
            RequestFormat = HalfItRequestFormat.Default(boardSize);
            EntrySlots = slots;
        }

        public HalfItPlayFormat(EntrySlots slots, HalfItRequestFormat HalfItRequestFormat)
        {
            RequestFormat = HalfItRequestFormat;
            EntrySlots = slots;
        }
    }
}