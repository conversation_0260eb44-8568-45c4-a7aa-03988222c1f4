using System;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandarts.domains.game.format
{
    public class KickDownRequestFormat
    {
        private Sets _sets = new(1);
        public Sets Sets
        {
            get => _sets;
            set
            {
                if (value > 1)
                {
                    //If sets is greater than 1, legs must be 3.
                    Legs = new Legs(3);
                }
                _sets = value;
            }
        }
        public Legs Legs = new(1);

        private GameCode _gameCode = GameCode._KickDown301;

        public GameCode GameCode
        {
            get => _gameCode;
            set
            {
                if (!value.IsKickDown())
                {
                    throw new ArgumentException("GameCode must be KickDown game.");
                }

                _gameCode = value;
                EndScore = _gameCode == GameCode._KickDown301 ? ZeroOneEndScore._301 : ZeroOneEndScore._501;
            }
        }

        public ZeroOneEndScore EndScore = ZeroOneEndScore._301;

        public KickDownOverScoreSetting OverScoreSetting = KickDownOverScoreSetting.Random;

        public InCondition InCondition = InCondition.OpenIn;
        public OutCondition OutCondition = OutCondition.OpenOut;

        public ThrowOrderAtFirstLeg ThrowOrderAtFirstLeg = ThrowOrderAtFirstLeg.Random;
        public ThrowOrderAtMiddleLeg ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.TakeTurn;

        public ThrowOrderAtFinalLeg ThrowOrderAtFinalLeg = ThrowOrderAtFinalLeg.TakeTurn;

        public MaxRound MaxRound = new(20);

        public static KickDownRequestFormat Default(BoardSize boardSize)
        {
            return boardSize switch
            {
                BoardSize.Inch155 => new KickDownRequestFormat()
                {
                    Legs = new Legs(1),
                    GameCode = GameCode._KickDown301,
                    OverScoreSetting = KickDownOverScoreSetting.Random,
                    InCondition = InCondition.OpenIn,
                    OutCondition = OutCondition.OpenOut,
                    ThrowOrderAtFirstLeg = ThrowOrderAtFirstLeg.Random,
                    ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.LooserFirst,
                    ThrowOrderAtFinalLeg = ThrowOrderAtFinalLeg.Cork,
                    MaxRound = new MaxRound(20)
                },
                BoardSize.Inch132 => new KickDownRequestFormat()
                {
                    Legs = new Legs(1),
                    GameCode = GameCode._KickDown301,
                    OverScoreSetting = KickDownOverScoreSetting.Random,
                    InCondition = InCondition.OpenIn,
                    OutCondition = OutCondition.DoubleOut,
                    ThrowOrderAtFirstLeg = ThrowOrderAtFirstLeg.Random,
                    ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.LooserFirst,
                    ThrowOrderAtFinalLeg = ThrowOrderAtFinalLeg.Cork,
                    MaxRound = new MaxRound(20)
                },
                BoardSize.Steel => new KickDownRequestFormat()
                {
                    Legs = new Legs(1),
                    GameCode = GameCode._KickDown501,
                    OverScoreSetting = KickDownOverScoreSetting.Random,
                    InCondition = InCondition.OpenIn,
                    OutCondition = OutCondition.OpenOut,
                    ThrowOrderAtFirstLeg = ThrowOrderAtFirstLeg.Random,
                    ThrowOrderAtMiddleLeg = ThrowOrderAtMiddleLeg.TakeTurn,
                    ThrowOrderAtFinalLeg = ThrowOrderAtFinalLeg.TakeTurn,
                    MaxRound = new MaxRound(20)
                },
                BoardSize.NonSelected => throw new ArgumentException("BoardSize is not selected."),
                _ => throw new ArgumentException("BoardSize is unexpected.")
            };
        }
    }
}