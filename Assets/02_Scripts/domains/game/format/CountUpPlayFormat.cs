using com.luxza.grandarts.domains.game.entry;
using com.luxza.grandarts.domains.player;

namespace com.luxza.grandarts.domains.game.format
{
    public class CountUpPlayFormat
    {

        public UnitFormat UnitFormat => EntrySlots.UnitFormat;
        public readonly EntrySlots EntrySlots;
        public readonly CountUpRequestFormat RequestFormat;

        public CountUpPlayFormat(BoardSize boardSize)
        {
            RequestFormat = CountUpRequestFormat.Default(boardSize);
            EntrySlots = new EntrySlots();
        }

        public CountUpPlayFormat(EntrySlots slots, BoardSize boardSize)
        {
            RequestFormat = CountUpRequestFormat.Default(boardSize);
            EntrySlots = slots;
        }

        public CountUpPlayFormat(EntrySlots slots, CountUpRequestFormat countUpRequestFormat)
        {
            RequestFormat = countUpRequestFormat;
            EntrySlots = slots;
        }
    }
}