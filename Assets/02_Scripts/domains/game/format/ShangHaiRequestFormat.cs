using System;
using System.Linq;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain.game;

namespace com.luxza.grandarts.domains.game.format
{
    public class ShangHaiRequestFormat
    {
        private Sets _sets = new(1);
        public Sets Sets
        {
            get => _sets;
            set
            {
                if (value > 1)
                {
                    //If sets is greater than 1, legs must be 3.
                    Legs = new Legs(3);
                }
                _sets = value;
            }
        }
        public Legs Legs = new(1);

        private GameCode _gameCode => GameCode._MultipleCr;

        public GameCode GameCode
        {
            get => _gameCode;
        }
        public MaxRound MaxRound = new(20);
        public int[] HalfRoundOption = new int[20];

        public static ShangHaiRequestFormat Default(BoardSize boardSize)
        {
            return boardSize switch
            {
                BoardSize.Inch155 => new ShangHaiRequestFormat()
                {
                    Legs = new Legs(1),
                    MaxRound = new MaxRound(20),
                    HalfRoundOption = new int[20]
                },
                BoardSize.Inch132 => new ShangHaiRequestFormat()
                {
                    Legs = new Legs(1),
                    MaxRound = new MaxRound(20),
                    HalfRoundOption = new int[20]
                },
                BoardSize.Steel => new ShangHaiRequestFormat()
                {
                    Legs = new Legs(1),
                    MaxRound = new MaxRound(20),
                    HalfRoundOption = new int[20]
                },
                BoardSize.NonSelected => throw new ArgumentException("BoardSize is not selected."),
                _ => throw new ArgumentException("BoardSize is unexpected.")
            };
        }

        public string HalfRoundToString()
        {
            return string.Join(",", HalfRoundOption);
        }

        public void StringToHalfRound(string value)
        {
            HalfRoundOption = value.Split(',').Select(s => int.TryParse(s, out int num) ? num : 0).ToArray();
        }
    }
}