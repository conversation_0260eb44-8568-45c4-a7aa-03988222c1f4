using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.result.match;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.online.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandarts.domains.game.factory {
    public static class MedleyRefereeFactory {
        public static (Referee01 ref01, PlayUnit[] playUnits) CreateX01Referee(
            OnlinePlayUnit owner,
            OnlinePlayUnit opponent,
            ThrowOrderAtFirstLeg orderAtFirstLeg,
            MedleyRequestFormat medleyRequestFormat,
            bool allPlayerUsingGranEye = false
        )
        {
            IEnumerable<PlayUnit> playUnits;

            //First leg.
            if (orderAtFirstLeg == ThrowOrderAtFirstLeg.Self)
            {
                playUnits =  new[] {
                    owner,
                    opponent
                };
            }
            else if (orderAtFirstLeg == ThrowOrderAtFirstLeg.Opponent)
            {
                playUnits =  new[]{
                    opponent,
                    owner
                };
            }
            else
            {
                throw new SystemException("ThrowOrderAtFirstLeg should decided on online match server.");
            }

            var targetStatsRate = owner.HostPlayer.Settings.TargetStatsRate;

            var gameCode = medleyRequestFormat.GetGameCode(1,1);
            switch (gameCode)
            {
                case GameCode._301:
                case GameCode._501:
                case GameCode._701:
                    var dartsLogicUnits = playUnits.Select(p => DartsLogicUnitFactory.Create(p, gameCode.Value, targetStatsRate, allPlayerUsingGranEye));
                    return (MatchMaker.Make01(
                        dartsLogicUnits.ToArray(),
                        GameRuleFactory.Create(medleyRequestFormat.Createx01RequestFormat(1,1)),
                        false,
                        null),
                        playUnits.ToArray());
                default: throw new NotSupportedException($"{gameCode} is not supported.");
            }
        }


        public static (Referee01 ref01, PlayUnit[] playUnits) CreateX01Referee(
            MedleyPlayFormat playFormat,
            TargetStatsRate targetStatsRate,
            MedleyMatchProgress progress,
            bool allPlayerUsingGranEye = false
        )
        {
            var playUnits = CreatePlayUnits(playFormat, progress);

            GameCode? gameCode = null;
            if (progress == null)
            {
                gameCode = playFormat.RequestFormat.GetGameCode(1, 1);
            }
            else
            {
                gameCode = playFormat.RequestFormat.GetGameCode(progress.SetNo, progress.LegNo);
            }
            switch (gameCode)
            {
                case GameCode._301:
                case GameCode._501:
                case GameCode._701:
                case GameCode._901:
                case GameCode._1101:
                case GameCode._1501:
                    var dartsLogicUnits = playUnits.Select(p => DartsLogicUnitFactory.Create(p, gameCode.Value, targetStatsRate, allPlayerUsingGranEye));
                    return (MatchMaker.Make01(
                        dartsLogicUnits.ToArray(),
                        GameRuleFactory.Create(playFormat.RequestFormat.Createx01RequestFormat(progress?.SetNo ?? 1, progress?.LegNo ?? 1)),
                        false,
                        //TODO: Arrange is not supported on steel. but if softtip supported, we will need Arrange.
                        null),
                        playUnits);
                default: throw new ArgumentException("GameCode is unexpected.");
            }
        }

        public static (RefereeStandardCR refCR, PlayUnit[] playUnits) CreateStandardCRReferee(
            OnlinePlayUnit owner,
            OnlinePlayUnit opponent,
            ThrowOrderAtFirstLeg orderAtFirstLeg,
            MedleyRequestFormat medleyRequestFormat,
            bool allPlayerUsingGranEye = false
        )
        {
            IEnumerable<PlayUnit> playUnits;

            //First leg.
            if (orderAtFirstLeg == ThrowOrderAtFirstLeg.Self)
            {
                playUnits = new[] {
                    owner,
                    opponent
                };
            }
            else if (orderAtFirstLeg == ThrowOrderAtFirstLeg.Opponent)
            {
                playUnits = new[]{
                    opponent,
                    owner
                };
            }
            else
            {
                throw new SystemException("ThrowOrderAtFirstLeg should decided on online match server.");
            }

            var targetStatsRate = owner.HostPlayer.Settings.TargetStatsRate;

            var gameCode = medleyRequestFormat.GetGameCode(1,1);
            switch (gameCode)
            {
                case GameCode._StandardCR:
                    var dartsLogicUnits = playUnits.Select(p => DartsLogicUnitFactory.Create(p, GameCode._StandardCR, targetStatsRate, allPlayerUsingGranEye));
                    return (
                        MatchMaker.MakeStandardCr(
                            dartsLogicUnits.ToArray(),
                            GameRuleFactory.Create(medleyRequestFormat.CreateCricketRequestFormat(1,1)),
                            false),
                        playUnits.ToArray());
                default: throw new NotSupportedException($"{gameCode} is not supported.");
            }
        }

        public static (RefereeStandardCR refCR, PlayUnit[] playUnits) CreateStandardCRReferee(
            MedleyPlayFormat playFormat,
            TargetStatsRate targetStatsRate,
            MedleyMatchProgress progress,
            bool allPlayerUsingGranEye = false
        )
        {
            var playUnits = CreatePlayUnits(playFormat, progress);

            GameCode? gameCode = null;
            if (progress == null)
            {
                gameCode = playFormat.RequestFormat.GetGameCode(1, 1);
            }
            else
            {
                gameCode = playFormat.RequestFormat.GetGameCode(progress.SetNo, progress.LegNo);
            }
            switch (gameCode)
            {
                case GameCode._StandardCR:
                    var dartsLogicUnits = playUnits.Select(p => DartsLogicUnitFactory.Create(p, GameCode._StandardCR, targetStatsRate, allPlayerUsingGranEye));
                    return (
                        MatchMaker.MakeStandardCr(
                            dartsLogicUnits.ToArray(),
                            GameRuleFactory.Create(playFormat.RequestFormat.CreateCricketRequestFormat(progress?.SetNo ?? 1, progress?.LegNo ?? 1)),
                            false),
                        playUnits);
                default: throw new ArgumentException("GameCode is unexpected.");
            }
        }

        private static PlayUnit[] CreatePlayUnits(MedleyPlayFormat playFormat, MedleyMatchProgress progress)
        {
            var playUnits = PlayUnitFactory.CreateUnits(playFormat.EntrySlots).ToArray();
            if (progress != null)
            {
                //Medley supports only 2 units.
                var latestGameResult = progress.LatestGameResult();
                if (latestGameResult == null)
                {
                    throw new SystemException($"It was not in first leg, but the latest game result is null.");
                }
                var latestGameThrowOrder = latestGameResult.UnitIdsOrderByThrowOrder.ToArray();
                if (latestGameThrowOrder[0] != playUnits[0].Id)
                {
                    //It means the first player threw as the second in the last leg.
                    //So there is need to change the order.
                    playUnits = playUnits.Reverse().ToArray();
                }
                else
                {
                    //It means the second player threw first in the last leg.
                    //So there is no need to change the order.
                }
                if (progress.PlayFormat.RequestFormat.ThrowOrderAtMiddleLeg == setting.ThrowOrderAtMiddleLeg.TakeTurn)
                {
                    playUnits = playUnits.Reverse().ToArray();
                }
                else if (progress.PlayFormat.RequestFormat.ThrowOrderAtMiddleLeg == setting.ThrowOrderAtMiddleLeg.LooserFirst)
                {
                    var LatestLegWinner = progress.LatestLegWinner();
                    if (LatestLegWinner == UnitId.Empty)
                    {
                        //It means a draw current leg.
                        playUnits = playUnits.Reverse().ToArray();
                    }
                    else
                    {
                        if (LatestLegWinner == playUnits[0].Id)
                        {
                            //It means the first player won the last leg.
                            //So the second player will throw first.
                            playUnits = playUnits.Reverse().ToArray();
                        }
                        else
                        {
                            //It means the second player won the last leg.
                            //So the first player will throw first.
                        }
                    }
                }
            }

            return playUnits;
        }
    }
}