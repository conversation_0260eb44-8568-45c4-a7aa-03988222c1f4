using com.luxza.grandarts.domains.game.context.x01;
using com.luxza.grandartslogic.domain.game;
using com.luxza.granlog;

namespace com.luxza.grandarts.domains.game.cpu.x01
{
    public class x01AimPositionDecideForArrangeStrategy : IAimPositionDecitionStrategy<Gamex01SessionContext>
    {
        public SegmentCode Decide(Gamex01SessionContext gameSessionContext, CPULevel level)
        {
            var rule = gameSessionContext.DataRetriver.GameRule;
            var calc = gameSessionContext.ArrangeCalculator;

            var currentThrowingUnitId = gameSessionContext.DataRetriver.BasicGameDataRetriver.CurrentThrowingUnit.Id;
            var routes = calc.GetArrangeRoutes(
                gameSessionContext.DataRetriver.BasicGameDataRetriver.CurrentThrowerScore,
                code => gameSessionContext.DataRetriver.VirtualScore(
                    Segment.FromSegmentCode(code),
                    currentThrowingUnitId
                )
            );

            if (routes.Length == 0)
            {
                Log.d($"Arrange Rounte not found. so we use {nameof(x01AimPositionDecideForReduceStrategy)}", nameof(x01AimPositionDecideForArrangeStrategy));
                return new x01AimPositionDecideForReduceStrategy().Decide(gameSessionContext, level);
            }

            var bestRoute = routes[0];
#if UNITY_EDITOR
            Log.d($"Aim: {bestRoute.First}", nameof(x01AimPositionDecideForArrangeStrategy));
#endif
            return bestRoute.First;
        }
    }
}