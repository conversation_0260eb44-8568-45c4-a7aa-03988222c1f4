using System;

namespace com.luxza.grandarts.domains.game.setting
{
    public struct MaxRound
    {
        public readonly int Value;

        public MaxRound(int value)
        {
            if (value < 1)
            {
                throw new ArgumentException("MaxRound must be greater than 0.");
            }
            else if (value >= 100)
            {
                throw new ArgumentException("MaxRound must be less than 100.");
            }
            Value = value;
        }

        public static MaxRound Free()
        {
            return new MaxRound(99);
        }

        public static implicit operator int(MaxRound maxRound)
        {
            return maxRound.Value;
        }

        public static implicit operator MaxRound(int value)
        {
            return new MaxRound(value);
        }

        public override bool Equals(object obj)
        {
            if (obj is MaxRound)
            {
                MaxRound other = (MaxRound)obj;
                return Value == other.Value;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return Value.GetHashCode();
        }

        public static bool operator ==(MaxRound left, MaxRound right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(MaxRound left, MaxRound right)
        {
            return !(left == right);
        }
    }
}