using System;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.unit;

namespace com.luxza.grandarts.domains.game.result.countup {
    public class CountUpProgress {
        public readonly CountUpPlayFormat PlayFormat;
        public readonly MatchID MatchID;

        private GameResult _gameResult;

        public bool IsCompleted { get; private set; } = false;

        public UnitId Winner { get; private set; } = UnitId.Empty;
        
        public CountUpProgress(MatchID matchId, CountUpPlayFormat playFormat){
            MatchID = matchId;
            PlayFormat = playFormat;
        }

        public GameResult GameResult => _gameResult;

        public TimeSpan TotalGameDuration
        {
            get
            {
                if(IsInProgress) throw new InvalidOperationException("Game is in progress.");
                return _gameResult.GameDuration;
            }
        }
        /// <summary>
        /// Returns whether the winner of the match is decided or not.
        /// </summary>
        /// <returns></returns>
        public bool IsWinnerDecided => Winner != UnitId.Empty;

        /// <summary>
        /// Returns whether the match finished as a draw or not.
        /// </summary>
        public bool IsDraw => !IsWinnerDecided && IsCompleted;

        public bool IsInProgress => _gameResult == null;

        /// <summary>
        /// Adds a game result to the match progress.
        /// </summary>
        /// <param name="gameResult"></param>
        public void AddGameResult(GameResult gameResult)
        {
            _gameResult = gameResult;
            Winner = gameResult.Winner;
            IsCompleted = true;
        }
    }
}