using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.award;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.game.result
{
    public class PlayDataAnalyzer
    {
        public int ThrowCount(IEnumerable<Round> rounds)
        {
            return rounds.Sum(r => r.ThrowCount);
        }

        public int HitCount(IEnumerable<Round> rounds, params SegmentCode[] segmentCode)
        {
            return rounds.Sum(r =>
            {
                if (r.TryGetRoundComponent<SegmentInput>(out var segment))
                {
                    return segment.Throws.Count(t => segmentCode.Contains(t.VirtualHitArea.Code));
                }
                else
                {
                    return 0;
                }
            });
        }

        public float HitRate(IEnumerable<Round> rounds, params SegmentCode[] segmentCode)
        {
            return (float)Math.Round(HitCount(rounds, segmentCode) / (double)ThrowCount(rounds), 3, MidpointRounding.AwayFromZero);
        }

        public int NoBullCount(IEnumerable<Round> rounds)
        {
            return rounds.Sum(r =>
            {
                if (r.TryGetRoundComponent<SegmentInput>(out var segment))
                {
                    return segment.Throws.Count(t => !t.VirtualHitArea.IsBull);
                }
                else
                {
                    return 0;
                }
            });
        }

        public float InnerBullRateInWholeBull(IEnumerable<Round> rounds)
        {
            var innerBullCount = HitCount(rounds, SegmentCode.DBull_In);
            var bullCount = HitCount(rounds, SegmentCode.SBull_Out, SegmentCode.DBull_In);
            return bullCount == 0 ? 0 : (float)Math.Round(innerBullCount / (float)bullCount, 3, MidpointRounding.AwayFromZero);
        }

        public float NoBullRate(IEnumerable<Round> rounds)
        {
            return (float)Math.Round(NoBullCount(rounds) / (float)ThrowCount(rounds), 3, MidpointRounding.AwayFromZero);
        }

        public int Ton80ChanceCount(IEnumerable<Round> rounds)
        {
            return rounds.Sum(r =>
            {
                if (r.TryGetRoundComponent<AwardChanceDetector>(out var awardChanceDetector))
                {
                    return awardChanceDetector.IsTon80Chance() ? 1 : 0;
                }
                else
                {
                    return 0;
                }
            });
        }

        public int HatChanceCount(IEnumerable<Round> rounds)
        {
            return rounds.Sum(r =>
            {
                if (r.TryGetRoundComponent<AwardChanceDetector>(out var awardChanceDetector))
                {
                    return awardChanceDetector.IsHatChance() ? 1 : 0;
                }
                else
                {
                    return 0;
                }
            });
        }

        public Dictionary<SegmentCode, int> HitCountDic(IEnumerable<Round> rounds)
        {
            Dictionary<SegmentCode, int> hitCountDic = new();

            foreach (var round in rounds)
            {
                if (round.TryGetRoundComponent<SegmentInput>(out var segment))
                {
                    foreach (var t in segment.Throws)
                    {
                        if (t.IsEmpty) continue;
                        if (hitCountDic.ContainsKey(t.ActuaryHitArea.Code))
                        {
                            hitCountDic[t.ActuaryHitArea.Code]++;
                        }
                        else
                        {
                            hitCountDic.Add(t.ActuaryHitArea.Code, 1);
                        }
                    }
                }
            }

            return hitCountDic;
        }

        public Dictionary<SegmentCode, float> HitRateDic(IEnumerable<Round> rounds)
        {
            Dictionary<SegmentCode, float> hitRateDic = new();

            foreach (var round in rounds)
            {
                if (round.TryGetRoundComponent<SegmentInput>(out var segment))
                {
                    foreach (var t in segment.Throws)
                    {
                        if (t.IsEmpty) continue;
                        if (hitRateDic.ContainsKey(t.ActuaryHitArea.Code))
                        {
                            hitRateDic[t.ActuaryHitArea.Code]++;
                        }
                        else
                        {
                            hitRateDic.Add(t.ActuaryHitArea.Code, 1);
                        }
                    }
                }
            }

            var totalThrow = ThrowCount(rounds);

            foreach (var key in hitRateDic.Keys)
            {
                hitRateDic[key] = (float)Math.Round((float)hitRateDic[key] / (float)totalThrow, 3, MidpointRounding.AwayFromZero);
            }

            return hitRateDic;
        }
    }
}