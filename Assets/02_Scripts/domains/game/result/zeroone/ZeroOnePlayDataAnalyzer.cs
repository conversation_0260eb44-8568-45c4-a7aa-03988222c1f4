using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.award;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandarts.domains.game.result
{
    public class ZeroOnePlayDataAnalyzer
    {
        public readonly PlayDataAnalyzer BasicPlayDataAnalyzer;
        public readonly ZeroOneRoundUtility RoundUtility;
        private readonly Round[] rounds;
        public ZeroOnePlayDataAnalyzer(Match01 match01, Round[] rounds)
        {
            if (rounds.Length >= 2)
            {
                if (rounds.Any(r => !r.Thrower.GranId.Equals(rounds[0].Thrower.GranId)))
                {
                    throw new ArgumentException("All rounds must have the same thrower.");
                }
            }

            BasicPlayDataAnalyzer = new PlayDataAnalyzer();
            RoundUtility = new ZeroOneRoundUtility(match01);
            this.rounds = rounds;
        }

        public float FirstNineDartsPPR()
        {
            var first3Rounds = rounds.Where(r => r.No <= 3).ToArray();
            var totalScore = RoundUtility.TotalScore(first3Rounds);

            if (first3Rounds.Length == 0) return 0;

            return (float)Math.Round(totalScore / (float)first3Rounds.Length, 3, MidpointRounding.AwayFromZero);
        }

        public int HighOffTryCount()
        {
            return RoundUtility.HighOffTryCount(rounds.ToArray());
        }

        public bool IsSucceedHighOff()
        {
            return RoundUtility.IsSucceedHighOff(rounds.ToArray());
        }

        public int BustCount()
        {
            return rounds.Sum(r =>
            {
                if (r.TryGetRoundComponent<ZeroOneBustController>(out var bustController))
                {
                    return bustController.IsBust ? 1 : 0;
                }
                else
                {
                    return 0;
                }
            });
        }

        public int CheckoutTryCount()
        {
            return RoundUtility.CheckoutTryCount(rounds.ToArray());
        }

        public int CheckoutNumber()
        {
            return RoundUtility.CheckoutNumber(rounds.ToArray());
        }

        public bool IsSucceedCheckout()
        {
            return RoundUtility.IsSucceedCheckout(rounds.ToArray());
        }

        public float CheckOutRate()
        {
            var checkoutTryCount = CheckoutTryCount();
            if (checkoutTryCount == 0) return 0;
            if (IsSucceedCheckout())
            {
                return (float)Math.Round(1 / (float)checkoutTryCount, 3, MidpointRounding.AwayFromZero);
            }
            else
            {
                return 0;
            }
        }

        public float ShootRate()
        {
            return BasicPlayDataAnalyzer.HitRate(rounds, SegmentCode.SBull_Out, SegmentCode.DBull_In, SegmentCode.T20);
        }

        public int CountOver180Score()
        {
            return rounds.Count(r =>
            {
                return RoundUtility.TotalScore(r) >= 180;
            });
        }

        public int CountOver160Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 160 && score < 180;
            });
        }

        public int CountOver140Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 140 && score < 160;
            });
        }

        public int CountOver120Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 120 && score < 140;
            });
        }

        public int CountOver100Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 100 && score < 120;
            });
        }

        public int CountOver80Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 80 && score < 100;
            });
        }

        public int CountOver60Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 60 && score < 80;
            });
        }

        public int CountOver40Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 40 && score < 60;
            });
        }

        public int CountOver20Score()
        {
            return rounds.Count(r =>
            {
                var score = RoundUtility.TotalScore(r);
                return score >= 20 && score < 40;
            });
        }

        public int CountLessThan19Score()
        {
            return rounds.Count(r =>
            {
                return RoundUtility.TotalScore(r) < 20;
            });
        }

        public Dictionary<Award, int> AwardCount()
        {
            Dictionary<Award, int> dic = new();
            if (rounds.Length == 0) return dic;
            var awards = rounds[0].Thrower.Board == grandartslogic.BoardSize.Steel ?
                        GameRule01.AchievableAwards_Steel :
                        GameRule01.AchievableAwards;
            foreach (var round in rounds)
            {
                var totalScore = RoundUtility.TotalScore(round);
                if (awards.Contains(Award.ThreeInABed) && AwardService.IsAchieve3InABed(round))
                {
                    if (dic.ContainsKey(Award.ThreeInABed))
                    {
                        dic[Award.ThreeInABed]++;
                    }
                    else
                    {
                        dic.Add(Award.ThreeInABed, 1);
                    }
                }

                if (awards.Contains(Award.ThreeInTheBlack) && AwardService.IsAchieve3InTheBlack(round))
                {
                    if (dic.ContainsKey(Award.ThreeInTheBlack))
                    {
                        dic[Award.ThreeInTheBlack]++;
                    }
                    else
                    {
                        dic.Add(Award.ThreeInTheBlack, 1);
                    }
                }

                if (awards.Contains(Award.HatTrick) && AwardService.IsAchieveHatTrick(round))
                {
                    if (dic.ContainsKey(Award.HatTrick))
                    {
                        dic[Award.HatTrick]++;
                    }
                    else
                    {
                        dic.Add(Award.HatTrick, 1);
                    }
                }

                if (awards.Contains(Award.HighTon) && AwardService.IsAchieveHighTon(RoundUtility.TotalScore(round)))
                {
                    if (dic.ContainsKey(Award.HighTon))
                    {
                        dic[Award.HighTon]++;
                    }
                    else
                    {
                        dic.Add(Award.HighTon, 1);
                    }
                }

                if (awards.Contains(Award.LowTon) && AwardService.IsAchieveLowTon(totalScore))
                {
                    if (dic.ContainsKey(Award.LowTon))
                    {
                        dic[Award.LowTon]++;
                    }
                    else
                    {
                        dic.Add(Award.LowTon, 1);
                    }
                }

                if (awards.Contains(Award.OneHundredAndAbove) && AwardService.IsAchieveOneHundredAndAbove(totalScore))
                {
                    if (dic.ContainsKey(Award.OneHundredAndAbove))
                    {
                        dic[Award.OneHundredAndAbove]++;
                    }
                    else
                    {
                        dic.Add(Award.OneHundredAndAbove, 1);
                    }
                }

                if (awards.Contains(Award.OneHundredFortyAndAbove) && AwardService.IsAchieveOneHundredAndAbove(totalScore))
                {
                    if (dic.ContainsKey(Award.OneHundredFortyAndAbove))
                    {
                        dic[Award.OneHundredFortyAndAbove]++;
                    }
                    else
                    {
                        dic.Add(Award.OneHundredFortyAndAbove, 1);
                    }
                }

                if (awards.Contains(Award.TonEighty) && AwardService.IsAchieveTon80(round))
                {
                    if (dic.ContainsKey(Award.TonEighty))
                    {
                        dic[Award.TonEighty]++;
                    }
                    else
                    {
                        dic.Add(Award.TonEighty, 1);
                    }
                }

                if (awards.Contains(Award.WhiteHorse) && AwardService.IsAchieveWhiteHorse(round))
                {
                    if (dic.ContainsKey(Award.WhiteHorse))
                    {
                        dic[Award.WhiteHorse]++;
                    }
                    else
                    {
                        dic.Add(Award.WhiteHorse, 1);
                    }
                }
            }

            return dic;
        }
    }
}