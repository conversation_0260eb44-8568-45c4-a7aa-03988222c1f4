using System;

namespace com.luxza.grandarts.domains.exceptions
{
    public enum OnlineMatchServerErrorCode
    {
        Unknown,
        /// <summary>
        /// Match not found.
        /// </summary>
        MatchNotFound,
        /// <summary>
        /// The player has GranId is already connected.
        /// </summary>
        DuplicatedSession,
    }

    public class OnlineMatchServerError : Exception
    {
        public readonly OnlineMatchServerErrorCode ErrorCode;

        public OnlineMatchServerError(OnlineMatchServerErrorCode errorCode) : base(errorCode.ToString())
        {
            ErrorCode = errorCode;
        }

        public OnlineMatchServerError(OnlineMatchServerErrorCode errorCode, string message) : base(message)
        {
            ErrorCode = errorCode;
        }

        public OnlineMatchServerError(OnlineMatchServerErrorCode errorCode, string message, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
        }

        public OnlineMatchServerError(OnlineMatchServerErrorCode errorCode, Exception innerException) : base(errorCode.ToString(), innerException)
        {
            ErrorCode = errorCode;
        }
    }
}