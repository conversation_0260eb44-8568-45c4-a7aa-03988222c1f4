// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: online_match.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace com.luxza.onlinematchserverclient.schema {

  /// <summary>Holder for reflection information generated from online_match.proto</summary>
  public static partial class OnlineMatchReflection {

    #region Descriptor
    /// <summary>File descriptor for online_match.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static OnlineMatchReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChJvbmxpbmVfbWF0Y2gucHJvdG8SC29ubGluZW1hdGNoGhtnb29nbGUvcHJv",
            "dG9idWYvZW1wdHkucHJvdG8i/QMKCUNsaWVudE1zZxIkCghzZW5kX2tleRgB",
            "IAEoCzIQLm9ubGluZW1hdGNoLktleUgAEjAKDG1zZ19yZWNlaXZlZBgCIAEo",
            "CzIYLm9ubGluZW1hdGNoLk1zZ1JlY2VpdmVkSAASMgoQcmVjb3ZlcnlfcmVx",
            "dWVzdBgDIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eUgAEjEKD2JsdWV0",
            "b290aF9lcnJvchgEIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eUgAEjMK",
            "EWJsdWV0b290aF9yZWNvdmVyGAUgASgLMhYuZ29vZ2xlLnByb3RvYnVmLkVt",
            "cHR5SAASLQoKbWlzc2VkX2tleRgGIAEoCzIXLm9ubGluZW1hdGNoLktleVJl",
            "cXVlc3RIABIkCgRjb3JrGAcgASgLMhQub25saW5lbWF0Y2guQ29ya01zZ0gA",
            "EiYKCG5leHRfbGVnGAggASgLMhIub25saW5lbWF0Y2guVW5pdHNIABIjCgVl",
            "dmVudBgJIAEoCzISLm9ubGluZW1hdGNoLkV2ZW50SAASKAoKc2VuZF9zY29y",
            "ZRgKIAEoCzISLm9ubGluZW1hdGNoLlNjb3JlSAASKQoJbWF0Y2hfZW5kGAsg",
            "ASgLMhQub25saW5lbWF0Y2guTGFzdEtleUgAQgUKA21zZyJrCgNLZXkSDgoG",
            "a2V5X2lkGAEgASgJEiQKBXZhbHVlGAIgASgOMhUub25saW5lbWF0Y2guS2V5",
            "VmFsdWUSLgoMaGl0X3Bvc2l0aW9uGAMgASgLMhgub25saW5lbWF0Y2guSGl0",
            "UG9zaXRpb24iIwoLSGl0UG9zaXRpb24SCQoBeBgBIAEoARIJCgF5GAIgASgB",
            "IlYKBVNjb3JlEg4KBmtleV9pZBgBIAEoCRINCgV2YWx1ZRgCIAEoBRIuCghj",
            "aGVja291dBgDIAEoDjIcLm9ubGluZW1hdGNoLkNoZWNrT3V0Q2hvaWNlcyJa",
            "CgpLZXlPclNjb3JlEh8KA2tleRgBIAEoCzIQLm9ubGluZW1hdGNoLktleUgA",
            "EiMKBXNjb3JlGAIgASgLMhIub25saW5lbWF0Y2guU2NvcmVIAEIGCgRkYXRh",
            "Ih0KC01zZ1JlY2VpdmVkEg4KBm1zZ19pZBgBIAEoBCItCgpLZXlSZXF1ZXN0",
            "Eg8KB2dyYW5faWQYASABKAkSDgoGa2V5X2lkGAIgASgJIkYKCU1pZGRsZUtl",
            "eRIOCgZrZXlfaWQYASABKAkSKQoFdmFsdWUYAiABKA4yGi5vbmxpbmVtYXRj",
            "aC5NaWRkbGVDaG9pY2VzIssBCgdDb3JrTXNnEiQKCGNvcmtfa2V5GAEgASgL",
            "MhAub25saW5lbWF0Y2guS2V5SAASNwoNd2lubmVyX2Nob2ljZRgCIAEoDjIe",
            "Lm9ubGluZW1hdGNoLkNvcmtXaW5uZXJDaG9pY2VzSAASLAoLZ2FtZV9jaG9p",
            "Y2UYAyABKA4yFS5vbmxpbmVtYXRjaC5HYW1lQ29kZUgAEiwKCm1pZGRsZV9r",
            "ZXkYBCABKAsyFi5vbmxpbmVtYXRjaC5NaWRkbGVLZXlIAEIFCgNtc2ciGAoE",
            "VW5pdBIQCghncmFuX2lkcxgBIAMoCSIpCgVVbml0cxIgCgV1bml0cxgBIAMo",
            "CzIRLm9ubGluZW1hdGNoLlVuaXQiFQoFRXZlbnQSDAoEY29kZRgBIAEoAyIZ",
            "CgdMYXN0S2V5Eg4KBmtleV9pZBgBIAEoCSL1BwoJU2VydmVyTXNnEg4KBm1z",
            "Z19pZBgBIAEoBBInCgVyZWFkeRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5F",
            "bXB0eUgAEioKCXJlbGF5X2tleRgDIAEoCzIVLm9ubGluZW1hdGNoLlJlbGF5",
            "S2V5SAASLAoKbWlzc2VkX2tleRgEIAEoCzIWLm9ubGluZW1hdGNoLk1pc3Nl",
            "ZEtleUgAEisKB3RpbWVvdXQYBSABKAsyGC5vbmxpbmVtYXRjaC5QbGF5ZXJF",
            "dmVudEgAEjAKDGRpc2Nvbm5lY3RlZBgGIAEoCzIYLm9ubGluZW1hdGNoLlBs",
            "YXllckV2ZW50SAASLQoJcmVjb25uZWN0GAcgASgLMhgub25saW5lbWF0Y2gu",
            "UGxheWVyRXZlbnRIABIzCg9ibHVldG9vdGhfZXJyb3IYCCABKAsyGC5vbmxp",
            "bmVtYXRjaC5QbGF5ZXJFdmVudEgAEjUKEWJsdWV0b290aF9yZWNvdmVyGAkg",
            "ASgLMhgub25saW5lbWF0Y2guUGxheWVyRXZlbnRIABImCgRpbml0GAogASgL",
            "MhYub25saW5lbWF0Y2guTWF0Y2hJbmZvSAASMwoRcmVjb3ZlcnlfcmVzcG9u",
            "c2UYCyABKAsyFi5vbmxpbmVtYXRjaC5NYXRjaEluZm9IABIvCg1ub3RfeW91",
            "cl90dXJuGAwgASgLMhYub25saW5lbWF0Y2guTWF0Y2hJbmZvSAASJwoFc3Rh",
            "cnQYDSABKAsyFi5vbmxpbmVtYXRjaC5NYXRjaEluZm9IABIqCgljb3JrX2lu",
            "Zm8YDiABKAsyFS5vbmxpbmVtYXRjaC5Db3JrSW5mb0gAEi0KCXJlY292ZXJl",
            "ZBgPIAEoCzIYLm9ubGluZW1hdGNoLlBsYXllckV2ZW50SAASLgoLcmVsYXlf",
            "ZXZlbnQYECABKAsyFy5vbmxpbmVtYXRjaC5SZWxheUV2ZW50SAASLgoLcmVs",
            "YXlfc2NvcmUYESABKAsyFy5vbmxpbmVtYXRjaC5SZWxheVNjb3JlSAASNwoQ",
            "cmVsYXlfbWlkZGxlX2tleRgSIAEoCzIbLm9ubGluZW1hdGNoLlJlbGF5TWlk",
            "ZGxlS2V5SAASMQoPY2xvc2VkX2J5X2FkbWluGBMgASgLMhYuZ29vZ2xlLnBy",
            "b3RvYnVmLkVtcHR5SAASQQoUY2xvc2VkX2J5X2Rpc3F1YWxpZnkYFCABKAsy",
            "IS5vbmxpbmVtYXRjaC5EaXNxdWFsaWZpY2F0aW9uSW5mb0gAEjMKEWNsb3Nl",
            "ZF9ieV9yZW1hdGNoGBUgASgLMhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5SABC",
            "BQoDbXNnIhsKCU1pc3NlZEtleRIOCgZrZXlfaWQYASABKAkigQEKCFJlbGF5",
            "S2V5Eg8KB2dyYW5faWQYASABKAkSDgoGa2V5X2lkGAIgASgJEiQKBXZhbHVl",
            "GAMgASgOMhUub25saW5lbWF0Y2guS2V5VmFsdWUSLgoMaGl0X3Bvc2l0aW9u",
            "GAQgASgLMhgub25saW5lbWF0Y2guSGl0UG9zaXRpb24ibAoKUmVsYXlTY29y",
            "ZRIPCgdncmFuX2lkGAEgASgJEg4KBmtleV9pZBgCIAEoCRINCgV2YWx1ZRgD",
            "IAEoBRIuCghjaGVja291dBgEIAEoDjIcLm9ubGluZW1hdGNoLkNoZWNrT3V0",
            "Q2hvaWNlcyJcCg5SZWxheU1pZGRsZUtleRIPCgdncmFuX2lkGAEgASgJEg4K",
            "BmtleV9pZBgCIAEoCRIpCgV2YWx1ZRgDIAEoDjIaLm9ubGluZW1hdGNoLk1p",
            "ZGRsZUNob2ljZXMiHgoLUGxheWVyRXZlbnQSDwoHZ3Jhbl9pZBgBIAEoCSKo",
            "AwoJTWF0Y2hJbmZvEhAKCHByb2dyZXNzGAEgASgNEjQKB3BsYXllcnMYAiAD",
            "KAsyIy5vbmxpbmVtYXRjaC5NYXRjaEluZm8uUGxheWVyc0VudHJ5EiMKBGNv",
            "ZGUYAyABKA4yFS5vbmxpbmVtYXRjaC5HYW1lQ29kZRIrCgxjb3JrX2hpc3Rv",
            "cnkYBCADKAsyFS5vbmxpbmVtYXRjaC5SZWxheUtleRIzCg5taWRkbGVfaGlz",
            "dG9yeRgFIAMoCzIbLm9ubGluZW1hdGNoLlJlbGF5TWlkZGxlS2V5EiwKC2tl",
            "eV9oaXN0b3J5GAYgAygLMhcub25saW5lbWF0Y2guS2V5T3JTY29yZRISCgps",
            "ZWdfbnVtYmVyGAcgASgNEhMKC2lzX2ZpbmlzaGVkGAggASgIEiwKC2Nvcmtf",
            "c3RhdHVzGAkgASgOMhcub25saW5lbWF0Y2guQ29ya1N0YXR1cxpHCgxQbGF5",
            "ZXJzRW50cnkSCwoDa2V5GAEgASgJEiYKBXZhbHVlGAIgASgLMhcub25saW5l",
            "bWF0Y2guUGxheWVySW5mbzoCOAEiiwMKClBsYXllckluZm8SDQoFb3JkZXIY",
            "ASABKA0SNQoHcmVzdWx0cxgCIAMoCzIkLm9ubGluZW1hdGNoLlBsYXllcklu",
            "Zm8uUmVzdWx0c0VudHJ5EjMKBnNjb3JlcxgDIAMoCzIjLm9ubGluZW1hdGNo",
            "LlBsYXllckluZm8uU2NvcmVzRW50cnkSPgoMaGl0X3Bvc2l0aW9uGAQgAygL",
            "Migub25saW5lbWF0Y2guUGxheWVySW5mby5IaXRQb3NpdGlvbkVudHJ5GkUK",
            "DFJlc3VsdHNFbnRyeRILCgNrZXkYASABKAkSJAoFdmFsdWUYAiABKA4yFS5v",
            "bmxpbmVtYXRjaC5LZXlWYWx1ZToCOAEaLQoLU2NvcmVzRW50cnkSCwoDa2V5",
            "GAEgASgJEg0KBXZhbHVlGAIgASgFOgI4ARpMChBIaXRQb3NpdGlvbkVudHJ5",
            "EgsKA2tleRgBIAEoCRInCgV2YWx1ZRgCIAEoCzIYLm9ubGluZW1hdGNoLkhp",
            "dFBvc2l0aW9uOgI4ASJzCgpDb3JrUmVzdWx0Eg4KBmtleV9pZBgBIAEoCRIu",
            "CgZyZXN1bHQYAiABKA4yHi5vbmxpbmVtYXRjaC5Db3JrUmVzdWx0LlJlc3Vs",
            "dCIlCgZSZXN1bHQSCAoERFJBVxAAEgcKA1dJThABEggKBExPU0UQAiJ6CgxN",
            "aWRkbGVSZXN1bHQSDgoGa2V5X2lkGAEgASgJEjAKBnJlc3VsdBgCIAEoDjIg",
            "Lm9ubGluZW1hdGNoLk1pZGRsZVJlc3VsdC5SZXN1bHQiKAoGUmVzdWx0EgsK",
            "B0ZBSUxVUkUQABIHCgNXSU4QARIICgRMT1NFEAIiyQIKCENvcmtJbmZvEioK",
            "CXJlbGF5X2tleRgBIAEoCzIVLm9ubGluZW1hdGNoLlJlbGF5S2V5SAASKQoG",
            "cmVzdWx0GAIgASgLMhcub25saW5lbWF0Y2guQ29ya1Jlc3VsdEgAEj0KE3Jl",
            "bGF5X3dpbm5lcl9jaG9pY2UYAyABKA4yHi5vbmxpbmVtYXRjaC5Db3JrV2lu",
            "bmVyQ2hvaWNlc0gAEjIKEXJlbGF5X2dhbWVfY2hvaWNlGAQgASgOMhUub25s",
            "aW5lbWF0Y2guR2FtZUNvZGVIABI3ChByZWxheV9taWRkbGVfa2V5GAUgASgL",
            "Mhsub25saW5lbWF0Y2guUmVsYXlNaWRkbGVLZXlIABIyCg1taWRkbGVfcmVz",
            "dWx0GAYgASgLMhkub25saW5lbWF0Y2guTWlkZGxlUmVzdWx0SABCBgoEaW5m",
            "byJACgpSZWxheUV2ZW50Eg8KB2dyYW5faWQYASABKAkSIQoFZXZlbnQYAiAB",
            "KAsyEi5vbmxpbmVtYXRjaC5FdmVudCK5AgoSQ3JlYXRlTWF0Y2hSZXF1ZXN0",
            "EhAKCGdyYW5faWRzGAEgAygJEiQKBWNvZGVzGAIgAygOMhUub25saW5lbWF0",
            "Y2guR2FtZUNvZGUSDAoEY29yaxgDIAEoCBIUCgxqb2luX3RpbWVvdXQYBCAB",
            "KAUSFAoMaW5pdF90aW1lb3V0GAUgASgFEhYKDnBsYXllcl90aW1lb3V0GAYg",
            "ASgFElEKG2FmdGVyX2ZpcnN0X2xlZ190aHJvd19vcmRlchgHIAEoDjIsLm9u",
            "bGluZW1hdGNoLkFmdGVyRmlyc3RMZWdUaHJvd09yZGVyU2V0dGluZ3MSRgoV",
            "dGllX2JyZWFrX3Rocm93X29yZGVyGAggASgOMicub25saW5lbWF0Y2guVGll",
            "QnJlYWtUaHJvd09yZGVyU2V0dGluZ3MiJwoTQ3JlYXRlTWF0Y2hSZXNwb25z",
            "ZRIQCghtYXRjaF9pZBgBIAEoCSIjCg9HZXRNYXRjaFJlcXVlc3QSEAoIbWF0",
            "Y2hfaWQYASABKAkiOAoQR2V0TWF0Y2hSZXNwb25zZRIkCgRpbmZvGAEgASgL",
            "MhYub25saW5lbWF0Y2guTWF0Y2hJbmZvIq0BCgxNYXRjaFNlc3Npb24SCgoC",
            "aWQYASABKAUSEAoIZ3Jhbl9pZHMYAiADKAkSMAoGc3RhdHVzGAMgASgOMiAu",
            "b25saW5lbWF0Y2guTWF0Y2hTZXNzaW9uLlN0YXR1cyJNCgZTdGF0dXMSCwoH",
            "VU5LTk9XThAAEhAKDERJU0NPTk5FQ1RFRBABEggKBElOSVQQAhINCglDT05O",
            "RUNURUQQAxILCgdUSU1FT1VUEAQiKwoXR2V0TWF0Y2hTZXNzaW9uc1JlcXVl",
            "c3QSEAoIbWF0Y2hfaWQYASABKAkiRwoYR2V0TWF0Y2hTZXNzaW9uc1Jlc3Bv",
            "bnNlEisKCHNlc3Npb25zGAEgAygLMhkub25saW5lbWF0Y2guTWF0Y2hTZXNz",
            "aW9uIhQKEkxpc3RNYXRjaGVzUmVxdWVzdCIoChNMaXN0TWF0Y2hlc1Jlc3Bv",
            "bnNlEhEKCW1hdGNoX2lkcxgBIAMoCSImChJEZWxldGVNYXRjaFJlcXVlc3QS",
            "EAoIbWF0Y2hfaWQYASABKAkiFQoTRGVsZXRlTWF0Y2hSZXNwb25zZSJGChRE",
            "aXNxdWFsaWZpY2F0aW9uSW5mbxIWCg53aW5uZXJfdW5pdF9pZBgBIAEoBBIW",
            "Cg5sb3Nlcl91bml0X2lkcxgCIAMoBCqPCgoIS2V5VmFsdWUSCwoHVU5LTk9X",
            "ThAAEg4KCklOX1NJTkdMRTEQARILCgdUUklQTEUxEAISDwoLT1VUX1NJTkdM",
            "RTEQAxILCgdET1VCTEUxEAQSDgoKSU5fU0lOR0xFMhAFEgsKB1RSSVBMRTIQ",
            "BhIPCgtPVVRfU0lOR0xFMhAHEgsKB0RPVUJMRTIQCBIOCgpJTl9TSU5HTEUz",
            "EAkSCwoHVFJJUExFMxAKEg8KC09VVF9TSU5HTEUzEAsSCwoHRE9VQkxFMxAM",
            "Eg4KCklOX1NJTkdMRTQQDRILCgdUUklQTEU0EA4SDwoLT1VUX1NJTkdMRTQQ",
            "DxILCgdET1VCTEU0EBASDgoKSU5fU0lOR0xFNRAREgsKB1RSSVBMRTUQEhIP",
            "CgtPVVRfU0lOR0xFNRATEgsKB0RPVUJMRTUQFBIOCgpJTl9TSU5HTEU2EBUS",
            "CwoHVFJJUExFNhAWEg8KC09VVF9TSU5HTEU2EBcSCwoHRE9VQkxFNhAYEg4K",
            "CklOX1NJTkdMRTcQGRILCgdUUklQTEU3EBoSDwoLT1VUX1NJTkdMRTcQGxIL",
            "CgdET1VCTEU3EBwSDgoKSU5fU0lOR0xFOBAdEgsKB1RSSVBMRTgQHhIPCgtP",
            "VVRfU0lOR0xFOBAfEgsKB0RPVUJMRTgQIBIOCgpJTl9TSU5HTEU5ECESCwoH",
            "VFJJUExFORAiEg8KC09VVF9TSU5HTEU5ECMSCwoHRE9VQkxFORAkEg8KC0lO",
            "X1NJTkdMRTEwECUSDAoIVFJJUExFMTAQJhIQCgxPVVRfU0lOR0xFMTAQJxIM",
            "CghET1VCTEUxMBAoEg8KC0lOX1NJTkdMRTExECkSDAoIVFJJUExFMTEQKhIQ",
            "CgxPVVRfU0lOR0xFMTEQKxIMCghET1VCTEUxMRAsEg8KC0lOX1NJTkdMRTEy",
            "EC0SDAoIVFJJUExFMTIQLhIQCgxPVVRfU0lOR0xFMTIQLxIMCghET1VCTEUx",
            "MhAwEg8KC0lOX1NJTkdMRTEzEDESDAoIVFJJUExFMTMQMhIQCgxPVVRfU0lO",
            "R0xFMTMQMxIMCghET1VCTEUxMxA0Eg8KC0lOX1NJTkdMRTE0EDUSDAoIVFJJ",
            "UExFMTQQNhIQCgxPVVRfU0lOR0xFMTQQNxIMCghET1VCTEUxNBA4Eg8KC0lO",
            "X1NJTkdMRTE1EDkSDAoIVFJJUExFMTUQOhIQCgxPVVRfU0lOR0xFMTUQOxIM",
            "CghET1VCTEUxNRA8Eg8KC0lOX1NJTkdMRTE2ED0SDAoIVFJJUExFMTYQPhIQ",
            "CgxPVVRfU0lOR0xFMTYQPxIMCghET1VCTEUxNhBAEg8KC0lOX1NJTkdMRTE3",
            "EEESDAoIVFJJUExFMTcQQhIQCgxPVVRfU0lOR0xFMTcQQxIMCghET1VCTEUx",
            "NxBEEg8KC0lOX1NJTkdMRTE4EEUSDAoIVFJJUExFMTgQRhIQCgxPVVRfU0lO",
            "R0xFMTgQRxIMCghET1VCTEUxOBBIEg8KC0lOX1NJTkdMRTE5EEkSDAoIVFJJ",
            "UExFMTkQShIQCgxPVVRfU0lOR0xFMTkQSxIMCghET1VCTEUxORBMEg8KC0lO",
            "X1NJTkdMRTIwEE0SDAoIVFJJUExFMjAQThIQCgxPVVRfU0lOR0xFMjAQTxIM",
            "CghET1VCTEUyMBBQEgsKB0lOX0JVTEwQURIMCghPVVRfQlVMTBBSEgcKA09V",
            "VBBTEgoKBkNIQU5HRRBUKigKEUNvcmtXaW5uZXJDaG9pY2VzEgkKBUZJUlNU",
            "EAASCAoER0FNRRABKm8KCEdhbWVDb2RlEg4KCklOREVGSU5JVEUQABIQCgxa",
            "RVJPX09ORV8zMDEQARIQCgxaRVJPX09ORV81MDEQAhIQCgxaRVJPX09ORV83",
            "MDEQAxIQCgxaRVJPX09ORV85MDEQBBILCgdDUklDS0VUEAUqYAoPQ2hlY2tP",
            "dXRDaG9pY2VzEhAKDE5PVF9DSEVDS09VVBAAEhIKDkNIRUNLT1VUX0ZJUlNU",
            "EAESEwoPQ0hFQ0tPVVRfU0VDT05EEAISEgoOQ0hFQ0tPVVRfVEhJUkQQAypE",
            "Cg1NaWRkbGVDaG9pY2VzEhAKDE1JRERMRV9BR0FJThAAEg8KC1RIUk9XX0ZJ",
            "UlNUEAESEAoMVEhST1dfU0VDT05EEAIqZAoKQ29ya1N0YXR1cxIPCgtVTk5F",
            "Q0VTU0FSWRAAEgwKCFdBSVRfS0VZEAESFgoSV0FJVF9XSU5ORVJfQ0hPSUNF",
            "EAISFAoQV0FJVF9HQU1FX0NIT0lDRRADEgkKBUZJWEVEEAQqgAEKH0FmdGVy",
            "Rmlyc3RMZWdUaHJvd09yZGVyU2V0dGluZ3MSJAogTk9ORV9BRlRFUl9GSVJT",
            "VF9MRUdfVEhST1dfT1JERVIQABIWChJQUkVWSU9VU19MRUdfTE9TRVIQARIf",
            "ChtQUkVWSU9VU19MRUdfU0VDT05EX1RIUk9XRVIQAiqfAQoaVGllQnJlYWtU",
            "aHJvd09yZGVyU2V0dGluZ3MSHgoaTk9ORV9USUVfQlJFQUtfVEhST1dfT1JE",
            "RVIQABIICgRDT1JLEAESCgoGUkFORE9NEAISIAocVElFX0JSRUFLX1BSRVZJ",
            "T1VTX0xFR19MT1NFUhADEikKJVRJRV9CUkVBS19QUkVWSU9VU19MRUdfU0VD",
            "T05EX1RIUk9XRVIQBDKIBAoST25saW5lTWF0Y2hTZXJ2aWNlEkkKCEdldE1h",
            "dGNoEhwub25saW5lbWF0Y2guR2V0TWF0Y2hSZXF1ZXN0Gh0ub25saW5lbWF0",
            "Y2guR2V0TWF0Y2hSZXNwb25zZSIAEmEKEEdldE1hdGNoU2Vzc2lvbnMSJC5v",
            "bmxpbmVtYXRjaC5HZXRNYXRjaFNlc3Npb25zUmVxdWVzdBolLm9ubGluZW1h",
            "dGNoLkdldE1hdGNoU2Vzc2lvbnNSZXNwb25zZSIAElIKC0xpc3RNYXRjaGVz",
            "Eh8ub25saW5lbWF0Y2guTGlzdE1hdGNoZXNSZXF1ZXN0GiAub25saW5lbWF0",
            "Y2guTGlzdE1hdGNoZXNSZXNwb25zZSIAElIKC0NyZWF0ZU1hdGNoEh8ub25s",
            "aW5lbWF0Y2guQ3JlYXRlTWF0Y2hSZXF1ZXN0GiAub25saW5lbWF0Y2guQ3Jl",
            "YXRlTWF0Y2hSZXNwb25zZSIAElIKC0RlbGV0ZU1hdGNoEh8ub25saW5lbWF0",
            "Y2guRGVsZXRlTWF0Y2hSZXF1ZXN0GiAub25saW5lbWF0Y2guRGVsZXRlTWF0",
            "Y2hSZXNwb25zZSIAEkgKEEV4Y2hhbmdlTWF0Y2hNc2cSFi5vbmxpbmVtYXRj",
            "aC5DbGllbnRNc2caFi5vbmxpbmVtYXRjaC5TZXJ2ZXJNc2ciACgBMAFCYFoz",
            "Z2l0aHViLmNvbS9sdXh6YS10ZWFtL2dyYW4tc2VydmVyLXYzL29ubGluZW1h",
            "dGNoL3BiqgIoY29tLmx1eHphLm9ubGluZW1hdGNoc2VydmVyY2xpZW50LnNj",
            "aGVtYWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Google.Protobuf.WellKnownTypes.EmptyReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::com.luxza.onlinematchserverclient.schema.KeyValue), typeof(global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices), typeof(global::com.luxza.onlinematchserverclient.schema.GameCode), typeof(global::com.luxza.onlinematchserverclient.schema.CheckOutChoices), typeof(global::com.luxza.onlinematchserverclient.schema.MiddleChoices), typeof(global::com.luxza.onlinematchserverclient.schema.CorkStatus), typeof(global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings), typeof(global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.ClientMsg), global::com.luxza.onlinematchserverclient.schema.ClientMsg.Parser, new[]{ "SendKey", "MsgReceived", "RecoveryRequest", "BluetoothError", "BluetoothRecover", "MissedKey", "Cork", "NextLeg", "Event", "SendScore", "MatchEnd" }, new[]{ "Msg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.Key), global::com.luxza.onlinematchserverclient.schema.Key.Parser, new[]{ "KeyId", "Value", "HitPosition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.HitPosition), global::com.luxza.onlinematchserverclient.schema.HitPosition.Parser, new[]{ "X", "Y" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.Score), global::com.luxza.onlinematchserverclient.schema.Score.Parser, new[]{ "KeyId", "Value", "Checkout" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.KeyOrScore), global::com.luxza.onlinematchserverclient.schema.KeyOrScore.Parser, new[]{ "Key", "Score" }, new[]{ "Data" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MsgReceived), global::com.luxza.onlinematchserverclient.schema.MsgReceived.Parser, new[]{ "MsgId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.KeyRequest), global::com.luxza.onlinematchserverclient.schema.KeyRequest.Parser, new[]{ "GranId", "KeyId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MiddleKey), global::com.luxza.onlinematchserverclient.schema.MiddleKey.Parser, new[]{ "KeyId", "Value" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.CorkMsg), global::com.luxza.onlinematchserverclient.schema.CorkMsg.Parser, new[]{ "CorkKey", "WinnerChoice", "GameChoice", "MiddleKey" }, new[]{ "Msg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.Unit), global::com.luxza.onlinematchserverclient.schema.Unit.Parser, new[]{ "GranIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.Units), global::com.luxza.onlinematchserverclient.schema.Units.Parser, new[]{ "Units_" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.Event), global::com.luxza.onlinematchserverclient.schema.Event.Parser, new[]{ "Code" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.LastKey), global::com.luxza.onlinematchserverclient.schema.LastKey.Parser, new[]{ "KeyId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.ServerMsg), global::com.luxza.onlinematchserverclient.schema.ServerMsg.Parser, new[]{ "MsgId", "Ready", "RelayKey", "MissedKey", "Timeout", "Disconnected", "Reconnect", "BluetoothError", "BluetoothRecover", "Init", "RecoveryResponse", "NotYourTurn", "Start", "CorkInfo", "Recovered", "RelayEvent", "RelayScore", "RelayMiddleKey", "ClosedByAdmin", "ClosedByDisqualify", "ClosedByRematch" }, new[]{ "Msg" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MissedKey), global::com.luxza.onlinematchserverclient.schema.MissedKey.Parser, new[]{ "KeyId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.RelayKey), global::com.luxza.onlinematchserverclient.schema.RelayKey.Parser, new[]{ "GranId", "KeyId", "Value", "HitPosition" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.RelayScore), global::com.luxza.onlinematchserverclient.schema.RelayScore.Parser, new[]{ "GranId", "KeyId", "Value", "Checkout" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey), global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey.Parser, new[]{ "GranId", "KeyId", "Value" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.PlayerEvent), global::com.luxza.onlinematchserverclient.schema.PlayerEvent.Parser, new[]{ "GranId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MatchInfo), global::com.luxza.onlinematchserverclient.schema.MatchInfo.Parser, new[]{ "Progress", "Players", "Code", "CorkHistory", "MiddleHistory", "KeyHistory", "LegNumber", "IsFinished", "CorkStatus" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.PlayerInfo), global::com.luxza.onlinematchserverclient.schema.PlayerInfo.Parser, new[]{ "Order", "Results", "Scores", "HitPosition" }, null, null, null, new pbr::GeneratedClrTypeInfo[] { null, null, null, }),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.CorkResult), global::com.luxza.onlinematchserverclient.schema.CorkResult.Parser, new[]{ "KeyId", "Result" }, null, new[]{ typeof(global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result) }, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MiddleResult), global::com.luxza.onlinematchserverclient.schema.MiddleResult.Parser, new[]{ "KeyId", "Result" }, null, new[]{ typeof(global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result) }, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.CorkInfo), global::com.luxza.onlinematchserverclient.schema.CorkInfo.Parser, new[]{ "RelayKey", "Result", "RelayWinnerChoice", "RelayGameChoice", "RelayMiddleKey", "MiddleResult" }, new[]{ "Info" }, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.RelayEvent), global::com.luxza.onlinematchserverclient.schema.RelayEvent.Parser, new[]{ "GranId", "Event" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.CreateMatchRequest), global::com.luxza.onlinematchserverclient.schema.CreateMatchRequest.Parser, new[]{ "GranIds", "Codes", "Cork", "JoinTimeout", "InitTimeout", "PlayerTimeout", "AfterFirstLegThrowOrder", "TieBreakThrowOrder" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.CreateMatchResponse), global::com.luxza.onlinematchserverclient.schema.CreateMatchResponse.Parser, new[]{ "MatchId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.GetMatchRequest), global::com.luxza.onlinematchserverclient.schema.GetMatchRequest.Parser, new[]{ "MatchId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.GetMatchResponse), global::com.luxza.onlinematchserverclient.schema.GetMatchResponse.Parser, new[]{ "Info" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.MatchSession), global::com.luxza.onlinematchserverclient.schema.MatchSession.Parser, new[]{ "Id", "GranIds", "Status" }, null, new[]{ typeof(global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status) }, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.GetMatchSessionsRequest), global::com.luxza.onlinematchserverclient.schema.GetMatchSessionsRequest.Parser, new[]{ "MatchId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.GetMatchSessionsResponse), global::com.luxza.onlinematchserverclient.schema.GetMatchSessionsResponse.Parser, new[]{ "Sessions" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.ListMatchesRequest), global::com.luxza.onlinematchserverclient.schema.ListMatchesRequest.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.ListMatchesResponse), global::com.luxza.onlinematchserverclient.schema.ListMatchesResponse.Parser, new[]{ "MatchIds" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.DeleteMatchRequest), global::com.luxza.onlinematchserverclient.schema.DeleteMatchRequest.Parser, new[]{ "MatchId" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.DeleteMatchResponse), global::com.luxza.onlinematchserverclient.schema.DeleteMatchResponse.Parser, null, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo), global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo.Parser, new[]{ "WinnerUnitId", "LoserUnitIds" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum KeyValue {
    [pbr::OriginalName("UNKNOWN")] Unknown = 0,
    [pbr::OriginalName("IN_SINGLE1")] InSingle1 = 1,
    [pbr::OriginalName("TRIPLE1")] Triple1 = 2,
    [pbr::OriginalName("OUT_SINGLE1")] OutSingle1 = 3,
    [pbr::OriginalName("DOUBLE1")] Double1 = 4,
    [pbr::OriginalName("IN_SINGLE2")] InSingle2 = 5,
    [pbr::OriginalName("TRIPLE2")] Triple2 = 6,
    [pbr::OriginalName("OUT_SINGLE2")] OutSingle2 = 7,
    [pbr::OriginalName("DOUBLE2")] Double2 = 8,
    [pbr::OriginalName("IN_SINGLE3")] InSingle3 = 9,
    [pbr::OriginalName("TRIPLE3")] Triple3 = 10,
    [pbr::OriginalName("OUT_SINGLE3")] OutSingle3 = 11,
    [pbr::OriginalName("DOUBLE3")] Double3 = 12,
    [pbr::OriginalName("IN_SINGLE4")] InSingle4 = 13,
    [pbr::OriginalName("TRIPLE4")] Triple4 = 14,
    [pbr::OriginalName("OUT_SINGLE4")] OutSingle4 = 15,
    [pbr::OriginalName("DOUBLE4")] Double4 = 16,
    [pbr::OriginalName("IN_SINGLE5")] InSingle5 = 17,
    [pbr::OriginalName("TRIPLE5")] Triple5 = 18,
    [pbr::OriginalName("OUT_SINGLE5")] OutSingle5 = 19,
    [pbr::OriginalName("DOUBLE5")] Double5 = 20,
    [pbr::OriginalName("IN_SINGLE6")] InSingle6 = 21,
    [pbr::OriginalName("TRIPLE6")] Triple6 = 22,
    [pbr::OriginalName("OUT_SINGLE6")] OutSingle6 = 23,
    [pbr::OriginalName("DOUBLE6")] Double6 = 24,
    [pbr::OriginalName("IN_SINGLE7")] InSingle7 = 25,
    [pbr::OriginalName("TRIPLE7")] Triple7 = 26,
    [pbr::OriginalName("OUT_SINGLE7")] OutSingle7 = 27,
    [pbr::OriginalName("DOUBLE7")] Double7 = 28,
    [pbr::OriginalName("IN_SINGLE8")] InSingle8 = 29,
    [pbr::OriginalName("TRIPLE8")] Triple8 = 30,
    [pbr::OriginalName("OUT_SINGLE8")] OutSingle8 = 31,
    [pbr::OriginalName("DOUBLE8")] Double8 = 32,
    [pbr::OriginalName("IN_SINGLE9")] InSingle9 = 33,
    [pbr::OriginalName("TRIPLE9")] Triple9 = 34,
    [pbr::OriginalName("OUT_SINGLE9")] OutSingle9 = 35,
    [pbr::OriginalName("DOUBLE9")] Double9 = 36,
    [pbr::OriginalName("IN_SINGLE10")] InSingle10 = 37,
    [pbr::OriginalName("TRIPLE10")] Triple10 = 38,
    [pbr::OriginalName("OUT_SINGLE10")] OutSingle10 = 39,
    [pbr::OriginalName("DOUBLE10")] Double10 = 40,
    [pbr::OriginalName("IN_SINGLE11")] InSingle11 = 41,
    [pbr::OriginalName("TRIPLE11")] Triple11 = 42,
    [pbr::OriginalName("OUT_SINGLE11")] OutSingle11 = 43,
    [pbr::OriginalName("DOUBLE11")] Double11 = 44,
    [pbr::OriginalName("IN_SINGLE12")] InSingle12 = 45,
    [pbr::OriginalName("TRIPLE12")] Triple12 = 46,
    [pbr::OriginalName("OUT_SINGLE12")] OutSingle12 = 47,
    [pbr::OriginalName("DOUBLE12")] Double12 = 48,
    [pbr::OriginalName("IN_SINGLE13")] InSingle13 = 49,
    [pbr::OriginalName("TRIPLE13")] Triple13 = 50,
    [pbr::OriginalName("OUT_SINGLE13")] OutSingle13 = 51,
    [pbr::OriginalName("DOUBLE13")] Double13 = 52,
    [pbr::OriginalName("IN_SINGLE14")] InSingle14 = 53,
    [pbr::OriginalName("TRIPLE14")] Triple14 = 54,
    [pbr::OriginalName("OUT_SINGLE14")] OutSingle14 = 55,
    [pbr::OriginalName("DOUBLE14")] Double14 = 56,
    [pbr::OriginalName("IN_SINGLE15")] InSingle15 = 57,
    [pbr::OriginalName("TRIPLE15")] Triple15 = 58,
    [pbr::OriginalName("OUT_SINGLE15")] OutSingle15 = 59,
    [pbr::OriginalName("DOUBLE15")] Double15 = 60,
    [pbr::OriginalName("IN_SINGLE16")] InSingle16 = 61,
    [pbr::OriginalName("TRIPLE16")] Triple16 = 62,
    [pbr::OriginalName("OUT_SINGLE16")] OutSingle16 = 63,
    [pbr::OriginalName("DOUBLE16")] Double16 = 64,
    [pbr::OriginalName("IN_SINGLE17")] InSingle17 = 65,
    [pbr::OriginalName("TRIPLE17")] Triple17 = 66,
    [pbr::OriginalName("OUT_SINGLE17")] OutSingle17 = 67,
    [pbr::OriginalName("DOUBLE17")] Double17 = 68,
    [pbr::OriginalName("IN_SINGLE18")] InSingle18 = 69,
    [pbr::OriginalName("TRIPLE18")] Triple18 = 70,
    [pbr::OriginalName("OUT_SINGLE18")] OutSingle18 = 71,
    [pbr::OriginalName("DOUBLE18")] Double18 = 72,
    [pbr::OriginalName("IN_SINGLE19")] InSingle19 = 73,
    [pbr::OriginalName("TRIPLE19")] Triple19 = 74,
    [pbr::OriginalName("OUT_SINGLE19")] OutSingle19 = 75,
    [pbr::OriginalName("DOUBLE19")] Double19 = 76,
    [pbr::OriginalName("IN_SINGLE20")] InSingle20 = 77,
    [pbr::OriginalName("TRIPLE20")] Triple20 = 78,
    [pbr::OriginalName("OUT_SINGLE20")] OutSingle20 = 79,
    [pbr::OriginalName("DOUBLE20")] Double20 = 80,
    [pbr::OriginalName("IN_BULL")] InBull = 81,
    [pbr::OriginalName("OUT_BULL")] OutBull = 82,
    [pbr::OriginalName("OUT")] Out = 83,
    [pbr::OriginalName("CHANGE")] Change = 84,
  }

  public enum CorkWinnerChoices {
    [pbr::OriginalName("FIRST")] First = 0,
    [pbr::OriginalName("GAME")] Game = 1,
  }

  public enum GameCode {
    /// <summary>
    /// The initial value of choice game in medley.
    /// </summary>
    [pbr::OriginalName("INDEFINITE")] Indefinite = 0,
    [pbr::OriginalName("ZERO_ONE_301")] ZeroOne301 = 1,
    [pbr::OriginalName("ZERO_ONE_501")] ZeroOne501 = 2,
    [pbr::OriginalName("ZERO_ONE_701")] ZeroOne701 = 3,
    [pbr::OriginalName("ZERO_ONE_901")] ZeroOne901 = 4,
    [pbr::OriginalName("CRICKET")] Cricket = 5,
  }

  public enum CheckOutChoices {
    [pbr::OriginalName("NOT_CHECKOUT")] NotCheckout = 0,
    [pbr::OriginalName("CHECKOUT_FIRST")] CheckoutFirst = 1,
    [pbr::OriginalName("CHECKOUT_SECOND")] CheckoutSecond = 2,
    [pbr::OriginalName("CHECKOUT_THIRD")] CheckoutThird = 3,
  }

  public enum MiddleChoices {
    [pbr::OriginalName("MIDDLE_AGAIN")] MiddleAgain = 0,
    [pbr::OriginalName("THROW_FIRST")] ThrowFirst = 1,
    [pbr::OriginalName("THROW_SECOND")] ThrowSecond = 2,
  }

  public enum CorkStatus {
    [pbr::OriginalName("UNNECESSARY")] Unnecessary = 0,
    [pbr::OriginalName("WAIT_KEY")] WaitKey = 1,
    [pbr::OriginalName("WAIT_WINNER_CHOICE")] WaitWinnerChoice = 2,
    [pbr::OriginalName("WAIT_GAME_CHOICE")] WaitGameChoice = 3,
    [pbr::OriginalName("FIXED")] Fixed = 4,
  }

  public enum AfterFirstLegThrowOrderSettings {
    [pbr::OriginalName("NONE_AFTER_FIRST_LEG_THROW_ORDER")] NoneAfterFirstLegThrowOrder = 0,
    [pbr::OriginalName("PREVIOUS_LEG_LOSER")] PreviousLegLoser = 1,
    [pbr::OriginalName("PREVIOUS_LEG_SECOND_THROWER")] PreviousLegSecondThrower = 2,
  }

  public enum TieBreakThrowOrderSettings {
    [pbr::OriginalName("NONE_TIE_BREAK_THROW_ORDER")] NoneTieBreakThrowOrder = 0,
    [pbr::OriginalName("CORK")] Cork = 1,
    [pbr::OriginalName("RANDOM")] Random = 2,
    [pbr::OriginalName("TIE_BREAK_PREVIOUS_LEG_LOSER")] TieBreakPreviousLegLoser = 3,
    [pbr::OriginalName("TIE_BREAK_PREVIOUS_LEG_SECOND_THROWER")] TieBreakPreviousLegSecondThrower = 4,
  }

  #endregion

  #region Messages
  /// <summary>
  ///*
  /// Client to server message
  /// </summary>
  public sealed partial class ClientMsg : pb::IMessage<ClientMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ClientMsg> _parser = new pb::MessageParser<ClientMsg>(() => new ClientMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ClientMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClientMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClientMsg(ClientMsg other) : this() {
      switch (other.MsgCase) {
        case MsgOneofCase.SendKey:
          SendKey = other.SendKey.Clone();
          break;
        case MsgOneofCase.MsgReceived:
          MsgReceived = other.MsgReceived.Clone();
          break;
        case MsgOneofCase.RecoveryRequest:
          RecoveryRequest = other.RecoveryRequest.Clone();
          break;
        case MsgOneofCase.BluetoothError:
          BluetoothError = other.BluetoothError.Clone();
          break;
        case MsgOneofCase.BluetoothRecover:
          BluetoothRecover = other.BluetoothRecover.Clone();
          break;
        case MsgOneofCase.MissedKey:
          MissedKey = other.MissedKey.Clone();
          break;
        case MsgOneofCase.Cork:
          Cork = other.Cork.Clone();
          break;
        case MsgOneofCase.NextLeg:
          NextLeg = other.NextLeg.Clone();
          break;
        case MsgOneofCase.Event:
          Event = other.Event.Clone();
          break;
        case MsgOneofCase.SendScore:
          SendScore = other.SendScore.Clone();
          break;
        case MsgOneofCase.MatchEnd:
          MatchEnd = other.MatchEnd.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ClientMsg Clone() {
      return new ClientMsg(this);
    }

    /// <summary>Field number for the "send_key" field.</summary>
    public const int SendKeyFieldNumber = 1;
    /// <summary>
    /// Send hit or change data. Send data are broadcast other clients participating same match.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Key SendKey {
      get { return msgCase_ == MsgOneofCase.SendKey ? (global::com.luxza.onlinematchserverclient.schema.Key) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.SendKey;
      }
    }

    /// <summary>Field number for the "msg_received" field.</summary>
    public const int MsgReceivedFieldNumber = 2;
    /// <summary>
    /// Notify to be received message successfully. Server keep sending same message until get this message.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MsgReceived MsgReceived {
      get { return msgCase_ == MsgOneofCase.MsgReceived ? (global::com.luxza.onlinematchserverclient.schema.MsgReceived) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.MsgReceived;
      }
    }

    /// <summary>Field number for the "recovery_request" field.</summary>
    public const int RecoveryRequestFieldNumber = 3;
    /// <summary>
    /// Get latest match info from server. Server return recovery_response message.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty RecoveryRequest {
      get { return msgCase_ == MsgOneofCase.RecoveryRequest ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RecoveryRequest;
      }
    }

    /// <summary>Field number for the "bluetooth_error" field.</summary>
    public const int BluetoothErrorFieldNumber = 4;
    /// <summary>
    /// Notify getting bluetooth error to other clients.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty BluetoothError {
      get { return msgCase_ == MsgOneofCase.BluetoothError ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.BluetoothError;
      }
    }

    /// <summary>Field number for the "bluetooth_recover" field.</summary>
    public const int BluetoothRecoverFieldNumber = 5;
    /// <summary>
    /// Notify recovered bluetooth error to other clients.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty BluetoothRecover {
      get { return msgCase_ == MsgOneofCase.BluetoothRecover ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.BluetoothRecover;
      }
    }

    /// <summary>Field number for the "missed_key" field.</summary>
    public const int MissedKeyFieldNumber = 6;
    /// <summary>
    /// Notify server to send specified key when the client could not receive expected key.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.KeyRequest MissedKey {
      get { return msgCase_ == MsgOneofCase.MissedKey ? (global::com.luxza.onlinematchserverclient.schema.KeyRequest) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.MissedKey;
      }
    }

    /// <summary>Field number for the "cork" field.</summary>
    public const int CorkFieldNumber = 7;
    /// <summary>
    /// Notify msg related to cork.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkMsg Cork {
      get { return msgCase_ == MsgOneofCase.Cork ? (global::com.luxza.onlinematchserverclient.schema.CorkMsg) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Cork;
      }
    }

    /// <summary>Field number for the "next_leg" field.</summary>
    public const int NextLegFieldNumber = 8;
    /// <summary>
    /// Notify to be ready next leg with sending throw order of the next leg.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Units NextLeg {
      get { return msgCase_ == MsgOneofCase.NextLeg ? (global::com.luxza.onlinematchserverclient.schema.Units) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.NextLeg;
      }
    }

    /// <summary>Field number for the "event" field.</summary>
    public const int EventFieldNumber = 9;
    /// <summary>
    /// Notify event defined by client side.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Event Event {
      get { return msgCase_ == MsgOneofCase.Event ? (global::com.luxza.onlinematchserverclient.schema.Event) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Event;
      }
    }

    /// <summary>Field number for the "send_score" field.</summary>
    public const int SendScoreFieldNumber = 10;
    /// <summary>
    /// Send score for steel 01. Send data are broadcast other clients participating same match.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Score SendScore {
      get { return msgCase_ == MsgOneofCase.SendScore ? (global::com.luxza.onlinematchserverclient.schema.Score) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.SendScore;
      }
    }

    /// <summary>Field number for the "match_end" field.</summary>
    public const int MatchEndFieldNumber = 11;
    /// <summary>
    /// Notify server that the ongoing match(leg) was ended.
    /// The server send MissedKey to client if the server does not have key.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.LastKey MatchEnd {
      get { return msgCase_ == MsgOneofCase.MatchEnd ? (global::com.luxza.onlinematchserverclient.schema.LastKey) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.MatchEnd;
      }
    }

    private object msg_;
    /// <summary>Enum of possible cases for the "msg" oneof.</summary>
    public enum MsgOneofCase {
      None = 0,
      SendKey = 1,
      MsgReceived = 2,
      RecoveryRequest = 3,
      BluetoothError = 4,
      BluetoothRecover = 5,
      MissedKey = 6,
      Cork = 7,
      NextLeg = 8,
      Event = 9,
      SendScore = 10,
      MatchEnd = 11,
    }
    private MsgOneofCase msgCase_ = MsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgOneofCase MsgCase {
      get { return msgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearMsg() {
      msgCase_ = MsgOneofCase.None;
      msg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ClientMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ClientMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(SendKey, other.SendKey)) return false;
      if (!object.Equals(MsgReceived, other.MsgReceived)) return false;
      if (!object.Equals(RecoveryRequest, other.RecoveryRequest)) return false;
      if (!object.Equals(BluetoothError, other.BluetoothError)) return false;
      if (!object.Equals(BluetoothRecover, other.BluetoothRecover)) return false;
      if (!object.Equals(MissedKey, other.MissedKey)) return false;
      if (!object.Equals(Cork, other.Cork)) return false;
      if (!object.Equals(NextLeg, other.NextLeg)) return false;
      if (!object.Equals(Event, other.Event)) return false;
      if (!object.Equals(SendScore, other.SendScore)) return false;
      if (!object.Equals(MatchEnd, other.MatchEnd)) return false;
      if (MsgCase != other.MsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (msgCase_ == MsgOneofCase.SendKey) hash ^= SendKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.MsgReceived) hash ^= MsgReceived.GetHashCode();
      if (msgCase_ == MsgOneofCase.RecoveryRequest) hash ^= RecoveryRequest.GetHashCode();
      if (msgCase_ == MsgOneofCase.BluetoothError) hash ^= BluetoothError.GetHashCode();
      if (msgCase_ == MsgOneofCase.BluetoothRecover) hash ^= BluetoothRecover.GetHashCode();
      if (msgCase_ == MsgOneofCase.MissedKey) hash ^= MissedKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.Cork) hash ^= Cork.GetHashCode();
      if (msgCase_ == MsgOneofCase.NextLeg) hash ^= NextLeg.GetHashCode();
      if (msgCase_ == MsgOneofCase.Event) hash ^= Event.GetHashCode();
      if (msgCase_ == MsgOneofCase.SendScore) hash ^= SendScore.GetHashCode();
      if (msgCase_ == MsgOneofCase.MatchEnd) hash ^= MatchEnd.GetHashCode();
      hash ^= (int) msgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (msgCase_ == MsgOneofCase.SendKey) {
        output.WriteRawTag(10);
        output.WriteMessage(SendKey);
      }
      if (msgCase_ == MsgOneofCase.MsgReceived) {
        output.WriteRawTag(18);
        output.WriteMessage(MsgReceived);
      }
      if (msgCase_ == MsgOneofCase.RecoveryRequest) {
        output.WriteRawTag(26);
        output.WriteMessage(RecoveryRequest);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        output.WriteRawTag(34);
        output.WriteMessage(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        output.WriteRawTag(42);
        output.WriteMessage(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        output.WriteRawTag(50);
        output.WriteMessage(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Cork) {
        output.WriteRawTag(58);
        output.WriteMessage(Cork);
      }
      if (msgCase_ == MsgOneofCase.NextLeg) {
        output.WriteRawTag(66);
        output.WriteMessage(NextLeg);
      }
      if (msgCase_ == MsgOneofCase.Event) {
        output.WriteRawTag(74);
        output.WriteMessage(Event);
      }
      if (msgCase_ == MsgOneofCase.SendScore) {
        output.WriteRawTag(82);
        output.WriteMessage(SendScore);
      }
      if (msgCase_ == MsgOneofCase.MatchEnd) {
        output.WriteRawTag(90);
        output.WriteMessage(MatchEnd);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (msgCase_ == MsgOneofCase.SendKey) {
        output.WriteRawTag(10);
        output.WriteMessage(SendKey);
      }
      if (msgCase_ == MsgOneofCase.MsgReceived) {
        output.WriteRawTag(18);
        output.WriteMessage(MsgReceived);
      }
      if (msgCase_ == MsgOneofCase.RecoveryRequest) {
        output.WriteRawTag(26);
        output.WriteMessage(RecoveryRequest);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        output.WriteRawTag(34);
        output.WriteMessage(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        output.WriteRawTag(42);
        output.WriteMessage(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        output.WriteRawTag(50);
        output.WriteMessage(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Cork) {
        output.WriteRawTag(58);
        output.WriteMessage(Cork);
      }
      if (msgCase_ == MsgOneofCase.NextLeg) {
        output.WriteRawTag(66);
        output.WriteMessage(NextLeg);
      }
      if (msgCase_ == MsgOneofCase.Event) {
        output.WriteRawTag(74);
        output.WriteMessage(Event);
      }
      if (msgCase_ == MsgOneofCase.SendScore) {
        output.WriteRawTag(82);
        output.WriteMessage(SendScore);
      }
      if (msgCase_ == MsgOneofCase.MatchEnd) {
        output.WriteRawTag(90);
        output.WriteMessage(MatchEnd);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (msgCase_ == MsgOneofCase.SendKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(SendKey);
      }
      if (msgCase_ == MsgOneofCase.MsgReceived) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MsgReceived);
      }
      if (msgCase_ == MsgOneofCase.RecoveryRequest) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RecoveryRequest);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Cork) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Cork);
      }
      if (msgCase_ == MsgOneofCase.NextLeg) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(NextLeg);
      }
      if (msgCase_ == MsgOneofCase.Event) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Event);
      }
      if (msgCase_ == MsgOneofCase.SendScore) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(SendScore);
      }
      if (msgCase_ == MsgOneofCase.MatchEnd) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MatchEnd);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ClientMsg other) {
      if (other == null) {
        return;
      }
      switch (other.MsgCase) {
        case MsgOneofCase.SendKey:
          if (SendKey == null) {
            SendKey = new global::com.luxza.onlinematchserverclient.schema.Key();
          }
          SendKey.MergeFrom(other.SendKey);
          break;
        case MsgOneofCase.MsgReceived:
          if (MsgReceived == null) {
            MsgReceived = new global::com.luxza.onlinematchserverclient.schema.MsgReceived();
          }
          MsgReceived.MergeFrom(other.MsgReceived);
          break;
        case MsgOneofCase.RecoveryRequest:
          if (RecoveryRequest == null) {
            RecoveryRequest = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          RecoveryRequest.MergeFrom(other.RecoveryRequest);
          break;
        case MsgOneofCase.BluetoothError:
          if (BluetoothError == null) {
            BluetoothError = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          BluetoothError.MergeFrom(other.BluetoothError);
          break;
        case MsgOneofCase.BluetoothRecover:
          if (BluetoothRecover == null) {
            BluetoothRecover = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          BluetoothRecover.MergeFrom(other.BluetoothRecover);
          break;
        case MsgOneofCase.MissedKey:
          if (MissedKey == null) {
            MissedKey = new global::com.luxza.onlinematchserverclient.schema.KeyRequest();
          }
          MissedKey.MergeFrom(other.MissedKey);
          break;
        case MsgOneofCase.Cork:
          if (Cork == null) {
            Cork = new global::com.luxza.onlinematchserverclient.schema.CorkMsg();
          }
          Cork.MergeFrom(other.Cork);
          break;
        case MsgOneofCase.NextLeg:
          if (NextLeg == null) {
            NextLeg = new global::com.luxza.onlinematchserverclient.schema.Units();
          }
          NextLeg.MergeFrom(other.NextLeg);
          break;
        case MsgOneofCase.Event:
          if (Event == null) {
            Event = new global::com.luxza.onlinematchserverclient.schema.Event();
          }
          Event.MergeFrom(other.Event);
          break;
        case MsgOneofCase.SendScore:
          if (SendScore == null) {
            SendScore = new global::com.luxza.onlinematchserverclient.schema.Score();
          }
          SendScore.MergeFrom(other.SendScore);
          break;
        case MsgOneofCase.MatchEnd:
          if (MatchEnd == null) {
            MatchEnd = new global::com.luxza.onlinematchserverclient.schema.LastKey();
          }
          MatchEnd.MergeFrom(other.MatchEnd);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (msgCase_ == MsgOneofCase.SendKey) {
              subBuilder.MergeFrom(SendKey);
            }
            input.ReadMessage(subBuilder);
            SendKey = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.MsgReceived subBuilder = new global::com.luxza.onlinematchserverclient.schema.MsgReceived();
            if (msgCase_ == MsgOneofCase.MsgReceived) {
              subBuilder.MergeFrom(MsgReceived);
            }
            input.ReadMessage(subBuilder);
            MsgReceived = subBuilder;
            break;
          }
          case 26: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.RecoveryRequest) {
              subBuilder.MergeFrom(RecoveryRequest);
            }
            input.ReadMessage(subBuilder);
            RecoveryRequest = subBuilder;
            break;
          }
          case 34: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.BluetoothError) {
              subBuilder.MergeFrom(BluetoothError);
            }
            input.ReadMessage(subBuilder);
            BluetoothError = subBuilder;
            break;
          }
          case 42: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.BluetoothRecover) {
              subBuilder.MergeFrom(BluetoothRecover);
            }
            input.ReadMessage(subBuilder);
            BluetoothRecover = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.KeyRequest subBuilder = new global::com.luxza.onlinematchserverclient.schema.KeyRequest();
            if (msgCase_ == MsgOneofCase.MissedKey) {
              subBuilder.MergeFrom(MissedKey);
            }
            input.ReadMessage(subBuilder);
            MissedKey = subBuilder;
            break;
          }
          case 58: {
            global::com.luxza.onlinematchserverclient.schema.CorkMsg subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkMsg();
            if (msgCase_ == MsgOneofCase.Cork) {
              subBuilder.MergeFrom(Cork);
            }
            input.ReadMessage(subBuilder);
            Cork = subBuilder;
            break;
          }
          case 66: {
            global::com.luxza.onlinematchserverclient.schema.Units subBuilder = new global::com.luxza.onlinematchserverclient.schema.Units();
            if (msgCase_ == MsgOneofCase.NextLeg) {
              subBuilder.MergeFrom(NextLeg);
            }
            input.ReadMessage(subBuilder);
            NextLeg = subBuilder;
            break;
          }
          case 74: {
            global::com.luxza.onlinematchserverclient.schema.Event subBuilder = new global::com.luxza.onlinematchserverclient.schema.Event();
            if (msgCase_ == MsgOneofCase.Event) {
              subBuilder.MergeFrom(Event);
            }
            input.ReadMessage(subBuilder);
            Event = subBuilder;
            break;
          }
          case 82: {
            global::com.luxza.onlinematchserverclient.schema.Score subBuilder = new global::com.luxza.onlinematchserverclient.schema.Score();
            if (msgCase_ == MsgOneofCase.SendScore) {
              subBuilder.MergeFrom(SendScore);
            }
            input.ReadMessage(subBuilder);
            SendScore = subBuilder;
            break;
          }
          case 90: {
            global::com.luxza.onlinematchserverclient.schema.LastKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.LastKey();
            if (msgCase_ == MsgOneofCase.MatchEnd) {
              subBuilder.MergeFrom(MatchEnd);
            }
            input.ReadMessage(subBuilder);
            MatchEnd = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (msgCase_ == MsgOneofCase.SendKey) {
              subBuilder.MergeFrom(SendKey);
            }
            input.ReadMessage(subBuilder);
            SendKey = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.MsgReceived subBuilder = new global::com.luxza.onlinematchserverclient.schema.MsgReceived();
            if (msgCase_ == MsgOneofCase.MsgReceived) {
              subBuilder.MergeFrom(MsgReceived);
            }
            input.ReadMessage(subBuilder);
            MsgReceived = subBuilder;
            break;
          }
          case 26: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.RecoveryRequest) {
              subBuilder.MergeFrom(RecoveryRequest);
            }
            input.ReadMessage(subBuilder);
            RecoveryRequest = subBuilder;
            break;
          }
          case 34: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.BluetoothError) {
              subBuilder.MergeFrom(BluetoothError);
            }
            input.ReadMessage(subBuilder);
            BluetoothError = subBuilder;
            break;
          }
          case 42: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.BluetoothRecover) {
              subBuilder.MergeFrom(BluetoothRecover);
            }
            input.ReadMessage(subBuilder);
            BluetoothRecover = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.KeyRequest subBuilder = new global::com.luxza.onlinematchserverclient.schema.KeyRequest();
            if (msgCase_ == MsgOneofCase.MissedKey) {
              subBuilder.MergeFrom(MissedKey);
            }
            input.ReadMessage(subBuilder);
            MissedKey = subBuilder;
            break;
          }
          case 58: {
            global::com.luxza.onlinematchserverclient.schema.CorkMsg subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkMsg();
            if (msgCase_ == MsgOneofCase.Cork) {
              subBuilder.MergeFrom(Cork);
            }
            input.ReadMessage(subBuilder);
            Cork = subBuilder;
            break;
          }
          case 66: {
            global::com.luxza.onlinematchserverclient.schema.Units subBuilder = new global::com.luxza.onlinematchserverclient.schema.Units();
            if (msgCase_ == MsgOneofCase.NextLeg) {
              subBuilder.MergeFrom(NextLeg);
            }
            input.ReadMessage(subBuilder);
            NextLeg = subBuilder;
            break;
          }
          case 74: {
            global::com.luxza.onlinematchserverclient.schema.Event subBuilder = new global::com.luxza.onlinematchserverclient.schema.Event();
            if (msgCase_ == MsgOneofCase.Event) {
              subBuilder.MergeFrom(Event);
            }
            input.ReadMessage(subBuilder);
            Event = subBuilder;
            break;
          }
          case 82: {
            global::com.luxza.onlinematchserverclient.schema.Score subBuilder = new global::com.luxza.onlinematchserverclient.schema.Score();
            if (msgCase_ == MsgOneofCase.SendScore) {
              subBuilder.MergeFrom(SendScore);
            }
            input.ReadMessage(subBuilder);
            SendScore = subBuilder;
            break;
          }
          case 90: {
            global::com.luxza.onlinematchserverclient.schema.LastKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.LastKey();
            if (msgCase_ == MsgOneofCase.MatchEnd) {
              subBuilder.MergeFrom(MatchEnd);
            }
            input.ReadMessage(subBuilder);
            MatchEnd = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// key_id syntax: [ROUND]:[NUMBER OF THROW]
  /// * Add "$" if last throw in match
  /// * Specify ROUND = 0 in case of cork
  /// EX:
  /// * Round 5, Throw 2 = "5:2"
  /// * Round 12, Throw 3, Last throw in match = "12:3$"
  /// </summary>
  public sealed partial class Key : pb::IMessage<Key>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Key> _parser = new pb::MessageParser<Key>(() => new Key());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Key> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Key() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Key(Key other) : this() {
      keyId_ = other.keyId_;
      value_ = other.value_;
      hitPosition_ = other.hitPosition_ != null ? other.hitPosition_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Key Clone() {
      return new Key(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private global::com.luxza.onlinematchserverclient.schema.KeyValue value_ = global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.KeyValue Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "hit_position" field.</summary>
    public const int HitPositionFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.HitPosition hitPosition_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.HitPosition HitPosition {
      get { return hitPosition_; }
      set {
        hitPosition_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Key);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Key other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      if (!object.Equals(HitPosition, other.HitPosition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) hash ^= Value.GetHashCode();
      if (hitPosition_ != null) hash ^= HitPosition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Value);
      }
      if (hitPosition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(HitPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Value);
      }
      if (hitPosition_ != null) {
        output.WriteRawTag(26);
        output.WriteMessage(HitPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Value);
      }
      if (hitPosition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(HitPosition);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Key other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        Value = other.Value;
      }
      if (other.hitPosition_ != null) {
        if (hitPosition_ == null) {
          HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
        }
        HitPosition.MergeFrom(other.HitPosition);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = (global::com.luxza.onlinematchserverclient.schema.KeyValue) input.ReadEnum();
            break;
          }
          case 26: {
            if (hitPosition_ == null) {
              HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
            }
            input.ReadMessage(HitPosition);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = (global::com.luxza.onlinematchserverclient.schema.KeyValue) input.ReadEnum();
            break;
          }
          case 26: {
            if (hitPosition_ == null) {
              HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
            }
            input.ReadMessage(HitPosition);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class HitPosition : pb::IMessage<HitPosition>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<HitPosition> _parser = new pb::MessageParser<HitPosition>(() => new HitPosition());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<HitPosition> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HitPosition() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HitPosition(HitPosition other) : this() {
      x_ = other.x_;
      y_ = other.y_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public HitPosition Clone() {
      return new HitPosition(this);
    }

    /// <summary>Field number for the "x" field.</summary>
    public const int XFieldNumber = 1;
    private double x_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double X {
      get { return x_; }
      set {
        x_ = value;
      }
    }

    /// <summary>Field number for the "y" field.</summary>
    public const int YFieldNumber = 2;
    private double y_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public double Y {
      get { return y_; }
      set {
        y_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as HitPosition);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(HitPosition other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(X, other.X)) return false;
      if (!pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.Equals(Y, other.Y)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (X != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(X);
      if (Y != 0D) hash ^= pbc::ProtobufEqualityComparers.BitwiseDoubleEqualityComparer.GetHashCode(Y);
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (X != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(X);
      }
      if (Y != 0D) {
        output.WriteRawTag(17);
        output.WriteDouble(Y);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (X != 0D) {
        output.WriteRawTag(9);
        output.WriteDouble(X);
      }
      if (Y != 0D) {
        output.WriteRawTag(17);
        output.WriteDouble(Y);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (X != 0D) {
        size += 1 + 8;
      }
      if (Y != 0D) {
        size += 1 + 8;
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(HitPosition other) {
      if (other == null) {
        return;
      }
      if (other.X != 0D) {
        X = other.X;
      }
      if (other.Y != 0D) {
        Y = other.Y;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 9: {
            X = input.ReadDouble();
            break;
          }
          case 17: {
            Y = input.ReadDouble();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 9: {
            X = input.ReadDouble();
            break;
          }
          case 17: {
            Y = input.ReadDouble();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// key_id syntax: [ROUND]:[NUMBER OF THROW]
  /// * Add "$" if last throw in match
  /// EX:
  /// * Round 5, Throw 2 = "5:2"
  /// </summary>
  public sealed partial class Score : pb::IMessage<Score>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Score> _parser = new pb::MessageParser<Score>(() => new Score());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Score> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Score() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Score(Score other) : this() {
      keyId_ = other.keyId_;
      value_ = other.value_;
      checkout_ = other.checkout_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Score Clone() {
      return new Score(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private int value_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "checkout" field.</summary>
    public const int CheckoutFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.CheckOutChoices checkout_ = global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CheckOutChoices Checkout {
      get { return checkout_; }
      set {
        checkout_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Score);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Score other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      if (Checkout != other.Checkout) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != 0) hash ^= Value.GetHashCode();
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) hash ^= Checkout.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Checkout);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != 0) {
        output.WriteRawTag(16);
        output.WriteInt32(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Checkout);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Checkout);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Score other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != 0) {
        Value = other.Value;
      }
      if (other.Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        Checkout = other.Checkout;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = input.ReadInt32();
            break;
          }
          case 24: {
            Checkout = (global::com.luxza.onlinematchserverclient.schema.CheckOutChoices) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = input.ReadInt32();
            break;
          }
          case 24: {
            Checkout = (global::com.luxza.onlinematchserverclient.schema.CheckOutChoices) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class KeyOrScore : pb::IMessage<KeyOrScore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<KeyOrScore> _parser = new pb::MessageParser<KeyOrScore>(() => new KeyOrScore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<KeyOrScore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyOrScore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyOrScore(KeyOrScore other) : this() {
      switch (other.DataCase) {
        case DataOneofCase.Key:
          Key = other.Key.Clone();
          break;
        case DataOneofCase.Score:
          Score = other.Score.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyOrScore Clone() {
      return new KeyOrScore(this);
    }

    /// <summary>Field number for the "key" field.</summary>
    public const int KeyFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Key Key {
      get { return dataCase_ == DataOneofCase.Key ? (global::com.luxza.onlinematchserverclient.schema.Key) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.Key;
      }
    }

    /// <summary>Field number for the "score" field.</summary>
    public const int ScoreFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Score Score {
      get { return dataCase_ == DataOneofCase.Score ? (global::com.luxza.onlinematchserverclient.schema.Score) data_ : null; }
      set {
        data_ = value;
        dataCase_ = value == null ? DataOneofCase.None : DataOneofCase.Score;
      }
    }

    private object data_;
    /// <summary>Enum of possible cases for the "data" oneof.</summary>
    public enum DataOneofCase {
      None = 0,
      Key = 1,
      Score = 2,
    }
    private DataOneofCase dataCase_ = DataOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DataOneofCase DataCase {
      get { return dataCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearData() {
      dataCase_ = DataOneofCase.None;
      data_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as KeyOrScore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(KeyOrScore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Key, other.Key)) return false;
      if (!object.Equals(Score, other.Score)) return false;
      if (DataCase != other.DataCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (dataCase_ == DataOneofCase.Key) hash ^= Key.GetHashCode();
      if (dataCase_ == DataOneofCase.Score) hash ^= Score.GetHashCode();
      hash ^= (int) dataCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (dataCase_ == DataOneofCase.Key) {
        output.WriteRawTag(10);
        output.WriteMessage(Key);
      }
      if (dataCase_ == DataOneofCase.Score) {
        output.WriteRawTag(18);
        output.WriteMessage(Score);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (dataCase_ == DataOneofCase.Key) {
        output.WriteRawTag(10);
        output.WriteMessage(Key);
      }
      if (dataCase_ == DataOneofCase.Score) {
        output.WriteRawTag(18);
        output.WriteMessage(Score);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (dataCase_ == DataOneofCase.Key) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Key);
      }
      if (dataCase_ == DataOneofCase.Score) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Score);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(KeyOrScore other) {
      if (other == null) {
        return;
      }
      switch (other.DataCase) {
        case DataOneofCase.Key:
          if (Key == null) {
            Key = new global::com.luxza.onlinematchserverclient.schema.Key();
          }
          Key.MergeFrom(other.Key);
          break;
        case DataOneofCase.Score:
          if (Score == null) {
            Score = new global::com.luxza.onlinematchserverclient.schema.Score();
          }
          Score.MergeFrom(other.Score);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (dataCase_ == DataOneofCase.Key) {
              subBuilder.MergeFrom(Key);
            }
            input.ReadMessage(subBuilder);
            Key = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.Score subBuilder = new global::com.luxza.onlinematchserverclient.schema.Score();
            if (dataCase_ == DataOneofCase.Score) {
              subBuilder.MergeFrom(Score);
            }
            input.ReadMessage(subBuilder);
            Score = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (dataCase_ == DataOneofCase.Key) {
              subBuilder.MergeFrom(Key);
            }
            input.ReadMessage(subBuilder);
            Key = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.Score subBuilder = new global::com.luxza.onlinematchserverclient.schema.Score();
            if (dataCase_ == DataOneofCase.Score) {
              subBuilder.MergeFrom(Score);
            }
            input.ReadMessage(subBuilder);
            Score = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class MsgReceived : pb::IMessage<MsgReceived>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MsgReceived> _parser = new pb::MessageParser<MsgReceived>(() => new MsgReceived());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MsgReceived> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgReceived() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgReceived(MsgReceived other) : this() {
      msgId_ = other.msgId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgReceived Clone() {
      return new MsgReceived(this);
    }

    /// <summary>Field number for the "msg_id" field.</summary>
    public const int MsgIdFieldNumber = 1;
    private ulong msgId_;
    /// <summary>
    /// msg_id of ServerMsg
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MsgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MsgReceived);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MsgReceived other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MsgId != other.MsgId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MsgId != 0UL) hash ^= MsgId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MsgId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MsgId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MsgId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MsgId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MsgId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MsgId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MsgReceived other) {
      if (other == null) {
        return;
      }
      if (other.MsgId != 0UL) {
        MsgId = other.MsgId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MsgId = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MsgId = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class KeyRequest : pb::IMessage<KeyRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<KeyRequest> _parser = new pb::MessageParser<KeyRequest>(() => new KeyRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<KeyRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyRequest(KeyRequest other) : this() {
      granId_ = other.granId_;
      keyId_ = other.keyId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public KeyRequest Clone() {
      return new KeyRequest(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 2;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as KeyRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(KeyRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      if (KeyId != other.KeyId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(KeyRequest other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// key_id syntax: "1" , "2" or "3"
  /// </summary>
  public sealed partial class MiddleKey : pb::IMessage<MiddleKey>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MiddleKey> _parser = new pb::MessageParser<MiddleKey>(() => new MiddleKey());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MiddleKey> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleKey() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleKey(MiddleKey other) : this() {
      keyId_ = other.keyId_;
      value_ = other.value_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleKey Clone() {
      return new MiddleKey(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 2;
    private global::com.luxza.onlinematchserverclient.schema.MiddleChoices value_ = global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MiddleChoices Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MiddleKey);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MiddleKey other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) hash ^= Value.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MiddleKey other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        Value = other.Value;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = (global::com.luxza.onlinematchserverclient.schema.MiddleChoices) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Value = (global::com.luxza.onlinematchserverclient.schema.MiddleChoices) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class CorkMsg : pb::IMessage<CorkMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CorkMsg> _parser = new pb::MessageParser<CorkMsg>(() => new CorkMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CorkMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkMsg(CorkMsg other) : this() {
      switch (other.MsgCase) {
        case MsgOneofCase.CorkKey:
          CorkKey = other.CorkKey.Clone();
          break;
        case MsgOneofCase.WinnerChoice:
          WinnerChoice = other.WinnerChoice;
          break;
        case MsgOneofCase.GameChoice:
          GameChoice = other.GameChoice;
          break;
        case MsgOneofCase.MiddleKey:
          MiddleKey = other.MiddleKey.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkMsg Clone() {
      return new CorkMsg(this);
    }

    /// <summary>Field number for the "cork_key" field.</summary>
    public const int CorkKeyFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Key CorkKey {
      get { return msgCase_ == MsgOneofCase.CorkKey ? (global::com.luxza.onlinematchserverclient.schema.Key) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.CorkKey;
      }
    }

    /// <summary>Field number for the "winner_choice" field.</summary>
    public const int WinnerChoiceFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices WinnerChoice {
      get { return msgCase_ == MsgOneofCase.WinnerChoice ? (global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices) msg_ : global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices.First; }
      set {
        msg_ = value;
        msgCase_ = MsgOneofCase.WinnerChoice;
      }
    }

    /// <summary>Field number for the "game_choice" field.</summary>
    public const int GameChoiceFieldNumber = 3;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.GameCode GameChoice {
      get { return msgCase_ == MsgOneofCase.GameChoice ? (global::com.luxza.onlinematchserverclient.schema.GameCode) msg_ : global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite; }
      set {
        msg_ = value;
        msgCase_ = MsgOneofCase.GameChoice;
      }
    }

    /// <summary>Field number for the "middle_key" field.</summary>
    public const int MiddleKeyFieldNumber = 4;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MiddleKey MiddleKey {
      get { return msgCase_ == MsgOneofCase.MiddleKey ? (global::com.luxza.onlinematchserverclient.schema.MiddleKey) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.MiddleKey;
      }
    }

    private object msg_;
    /// <summary>Enum of possible cases for the "msg" oneof.</summary>
    public enum MsgOneofCase {
      None = 0,
      CorkKey = 1,
      WinnerChoice = 2,
      GameChoice = 3,
      MiddleKey = 4,
    }
    private MsgOneofCase msgCase_ = MsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgOneofCase MsgCase {
      get { return msgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearMsg() {
      msgCase_ = MsgOneofCase.None;
      msg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CorkMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CorkMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(CorkKey, other.CorkKey)) return false;
      if (WinnerChoice != other.WinnerChoice) return false;
      if (GameChoice != other.GameChoice) return false;
      if (!object.Equals(MiddleKey, other.MiddleKey)) return false;
      if (MsgCase != other.MsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (msgCase_ == MsgOneofCase.CorkKey) hash ^= CorkKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.WinnerChoice) hash ^= WinnerChoice.GetHashCode();
      if (msgCase_ == MsgOneofCase.GameChoice) hash ^= GameChoice.GetHashCode();
      if (msgCase_ == MsgOneofCase.MiddleKey) hash ^= MiddleKey.GetHashCode();
      hash ^= (int) msgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (msgCase_ == MsgOneofCase.CorkKey) {
        output.WriteRawTag(10);
        output.WriteMessage(CorkKey);
      }
      if (msgCase_ == MsgOneofCase.WinnerChoice) {
        output.WriteRawTag(16);
        output.WriteEnum((int) WinnerChoice);
      }
      if (msgCase_ == MsgOneofCase.GameChoice) {
        output.WriteRawTag(24);
        output.WriteEnum((int) GameChoice);
      }
      if (msgCase_ == MsgOneofCase.MiddleKey) {
        output.WriteRawTag(34);
        output.WriteMessage(MiddleKey);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (msgCase_ == MsgOneofCase.CorkKey) {
        output.WriteRawTag(10);
        output.WriteMessage(CorkKey);
      }
      if (msgCase_ == MsgOneofCase.WinnerChoice) {
        output.WriteRawTag(16);
        output.WriteEnum((int) WinnerChoice);
      }
      if (msgCase_ == MsgOneofCase.GameChoice) {
        output.WriteRawTag(24);
        output.WriteEnum((int) GameChoice);
      }
      if (msgCase_ == MsgOneofCase.MiddleKey) {
        output.WriteRawTag(34);
        output.WriteMessage(MiddleKey);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (msgCase_ == MsgOneofCase.CorkKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(CorkKey);
      }
      if (msgCase_ == MsgOneofCase.WinnerChoice) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) WinnerChoice);
      }
      if (msgCase_ == MsgOneofCase.GameChoice) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) GameChoice);
      }
      if (msgCase_ == MsgOneofCase.MiddleKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MiddleKey);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CorkMsg other) {
      if (other == null) {
        return;
      }
      switch (other.MsgCase) {
        case MsgOneofCase.CorkKey:
          if (CorkKey == null) {
            CorkKey = new global::com.luxza.onlinematchserverclient.schema.Key();
          }
          CorkKey.MergeFrom(other.CorkKey);
          break;
        case MsgOneofCase.WinnerChoice:
          WinnerChoice = other.WinnerChoice;
          break;
        case MsgOneofCase.GameChoice:
          GameChoice = other.GameChoice;
          break;
        case MsgOneofCase.MiddleKey:
          if (MiddleKey == null) {
            MiddleKey = new global::com.luxza.onlinematchserverclient.schema.MiddleKey();
          }
          MiddleKey.MergeFrom(other.MiddleKey);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (msgCase_ == MsgOneofCase.CorkKey) {
              subBuilder.MergeFrom(CorkKey);
            }
            input.ReadMessage(subBuilder);
            CorkKey = subBuilder;
            break;
          }
          case 16: {
            msg_ = input.ReadEnum();
            msgCase_ = MsgOneofCase.WinnerChoice;
            break;
          }
          case 24: {
            msg_ = input.ReadEnum();
            msgCase_ = MsgOneofCase.GameChoice;
            break;
          }
          case 34: {
            global::com.luxza.onlinematchserverclient.schema.MiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.MiddleKey();
            if (msgCase_ == MsgOneofCase.MiddleKey) {
              subBuilder.MergeFrom(MiddleKey);
            }
            input.ReadMessage(subBuilder);
            MiddleKey = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.Key subBuilder = new global::com.luxza.onlinematchserverclient.schema.Key();
            if (msgCase_ == MsgOneofCase.CorkKey) {
              subBuilder.MergeFrom(CorkKey);
            }
            input.ReadMessage(subBuilder);
            CorkKey = subBuilder;
            break;
          }
          case 16: {
            msg_ = input.ReadEnum();
            msgCase_ = MsgOneofCase.WinnerChoice;
            break;
          }
          case 24: {
            msg_ = input.ReadEnum();
            msgCase_ = MsgOneofCase.GameChoice;
            break;
          }
          case 34: {
            global::com.luxza.onlinematchserverclient.schema.MiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.MiddleKey();
            if (msgCase_ == MsgOneofCase.MiddleKey) {
              subBuilder.MergeFrom(MiddleKey);
            }
            input.ReadMessage(subBuilder);
            MiddleKey = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class Unit : pb::IMessage<Unit>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Unit> _parser = new pb::MessageParser<Unit>(() => new Unit());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Unit> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Unit() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Unit(Unit other) : this() {
      granIds_ = other.granIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Unit Clone() {
      return new Unit(this);
    }

    /// <summary>Field number for the "gran_ids" field.</summary>
    public const int GranIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_granIds_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> granIds_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// Must be specified by throw order
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> GranIds {
      get { return granIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Unit);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Unit other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!granIds_.Equals(other.granIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= granIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      granIds_.WriteTo(output, _repeated_granIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      granIds_.WriteTo(ref output, _repeated_granIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += granIds_.CalculateSize(_repeated_granIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Unit other) {
      if (other == null) {
        return;
      }
      granIds_.Add(other.granIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            granIds_.AddEntriesFrom(input, _repeated_granIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            granIds_.AddEntriesFrom(ref input, _repeated_granIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class Units : pb::IMessage<Units>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Units> _parser = new pb::MessageParser<Units>(() => new Units());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Units> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Units() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Units(Units other) : this() {
      units_ = other.units_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Units Clone() {
      return new Units(this);
    }

    /// <summary>Field number for the "units" field.</summary>
    public const int Units_FieldNumber = 1;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.Unit> _repeated_units_codec
        = pb::FieldCodec.ForMessage(10, global::com.luxza.onlinematchserverclient.schema.Unit.Parser);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.Unit> units_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.Unit>();
    /// <summary>
    /// Must be specified by throw order
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.Unit> Units_ {
      get { return units_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Units);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Units other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!units_.Equals(other.units_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= units_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      units_.WriteTo(output, _repeated_units_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      units_.WriteTo(ref output, _repeated_units_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += units_.CalculateSize(_repeated_units_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Units other) {
      if (other == null) {
        return;
      }
      units_.Add(other.units_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            units_.AddEntriesFrom(input, _repeated_units_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            units_.AddEntriesFrom(ref input, _repeated_units_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class Event : pb::IMessage<Event>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Event> _parser = new pb::MessageParser<Event>(() => new Event());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Event> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Event() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Event(Event other) : this() {
      code_ = other.code_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Event Clone() {
      return new Event(this);
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int CodeFieldNumber = 1;
    private long code_;
    /// <summary>
    /// code is defined by client side
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public long Code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Event);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Event other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Code != other.Code) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Code != 0L) hash ^= Code.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Code != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Code != 0L) {
        output.WriteRawTag(8);
        output.WriteInt64(Code);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Code != 0L) {
        size += 1 + pb::CodedOutputStream.ComputeInt64Size(Code);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Event other) {
      if (other == null) {
        return;
      }
      if (other.Code != 0L) {
        Code = other.Code;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Code = input.ReadInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Code = input.ReadInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class LastKey : pb::IMessage<LastKey>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<LastKey> _parser = new pb::MessageParser<LastKey>(() => new LastKey());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<LastKey> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LastKey() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LastKey(LastKey other) : this() {
      keyId_ = other.keyId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public LastKey Clone() {
      return new LastKey(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as LastKey);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(LastKey other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(LastKey other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///*
  /// Server to client message
  /// </summary>
  public sealed partial class ServerMsg : pb::IMessage<ServerMsg>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ServerMsg> _parser = new pb::MessageParser<ServerMsg>(() => new ServerMsg());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ServerMsg> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerMsg() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerMsg(ServerMsg other) : this() {
      msgId_ = other.msgId_;
      switch (other.MsgCase) {
        case MsgOneofCase.Ready:
          Ready = other.Ready.Clone();
          break;
        case MsgOneofCase.RelayKey:
          RelayKey = other.RelayKey.Clone();
          break;
        case MsgOneofCase.MissedKey:
          MissedKey = other.MissedKey.Clone();
          break;
        case MsgOneofCase.Timeout:
          Timeout = other.Timeout.Clone();
          break;
        case MsgOneofCase.Disconnected:
          Disconnected = other.Disconnected.Clone();
          break;
        case MsgOneofCase.Reconnect:
          Reconnect = other.Reconnect.Clone();
          break;
        case MsgOneofCase.BluetoothError:
          BluetoothError = other.BluetoothError.Clone();
          break;
        case MsgOneofCase.BluetoothRecover:
          BluetoothRecover = other.BluetoothRecover.Clone();
          break;
        case MsgOneofCase.Init:
          Init = other.Init.Clone();
          break;
        case MsgOneofCase.RecoveryResponse:
          RecoveryResponse = other.RecoveryResponse.Clone();
          break;
        case MsgOneofCase.NotYourTurn:
          NotYourTurn = other.NotYourTurn.Clone();
          break;
        case MsgOneofCase.Start:
          Start = other.Start.Clone();
          break;
        case MsgOneofCase.CorkInfo:
          CorkInfo = other.CorkInfo.Clone();
          break;
        case MsgOneofCase.Recovered:
          Recovered = other.Recovered.Clone();
          break;
        case MsgOneofCase.RelayEvent:
          RelayEvent = other.RelayEvent.Clone();
          break;
        case MsgOneofCase.RelayScore:
          RelayScore = other.RelayScore.Clone();
          break;
        case MsgOneofCase.RelayMiddleKey:
          RelayMiddleKey = other.RelayMiddleKey.Clone();
          break;
        case MsgOneofCase.ClosedByAdmin:
          ClosedByAdmin = other.ClosedByAdmin.Clone();
          break;
        case MsgOneofCase.ClosedByDisqualify:
          ClosedByDisqualify = other.ClosedByDisqualify.Clone();
          break;
        case MsgOneofCase.ClosedByRematch:
          ClosedByRematch = other.ClosedByRematch.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ServerMsg Clone() {
      return new ServerMsg(this);
    }

    /// <summary>Field number for the "msg_id" field.</summary>
    public const int MsgIdFieldNumber = 1;
    private ulong msgId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong MsgId {
      get { return msgId_; }
      set {
        msgId_ = value;
      }
    }

    /// <summary>Field number for the "ready" field.</summary>
    public const int ReadyFieldNumber = 2;
    /// <summary>
    /// Notify that all clients connected server.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty Ready {
      get { return msgCase_ == MsgOneofCase.Ready ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Ready;
      }
    }

    /// <summary>Field number for the "relay_key" field.</summary>
    public const int RelayKeyFieldNumber = 3;
    /// <summary>
    /// Relay key data sent by the client to the others.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayKey RelayKey {
      get { return msgCase_ == MsgOneofCase.RelayKey ? (global::com.luxza.onlinematchserverclient.schema.RelayKey) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RelayKey;
      }
    }

    /// <summary>Field number for the "missed_key" field.</summary>
    public const int MissedKeyFieldNumber = 4;
    /// <summary>
    /// Notify sender client to send specified key when server could not receive expected key.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MissedKey MissedKey {
      get { return msgCase_ == MsgOneofCase.MissedKey ? (global::com.luxza.onlinematchserverclient.schema.MissedKey) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.MissedKey;
      }
    }

    /// <summary>Field number for the "timeout" field.</summary>
    public const int TimeoutFieldNumber = 5;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent Timeout {
      get { return msgCase_ == MsgOneofCase.Timeout ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Timeout;
      }
    }

    /// <summary>Field number for the "disconnected" field.</summary>
    public const int DisconnectedFieldNumber = 6;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent Disconnected {
      get { return msgCase_ == MsgOneofCase.Disconnected ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Disconnected;
      }
    }

    /// <summary>Field number for the "reconnect" field.</summary>
    public const int ReconnectFieldNumber = 7;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent Reconnect {
      get { return msgCase_ == MsgOneofCase.Reconnect ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Reconnect;
      }
    }

    /// <summary>Field number for the "bluetooth_error" field.</summary>
    public const int BluetoothErrorFieldNumber = 8;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent BluetoothError {
      get { return msgCase_ == MsgOneofCase.BluetoothError ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.BluetoothError;
      }
    }

    /// <summary>Field number for the "bluetooth_recover" field.</summary>
    public const int BluetoothRecoverFieldNumber = 9;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent BluetoothRecover {
      get { return msgCase_ == MsgOneofCase.BluetoothRecover ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.BluetoothRecover;
      }
    }

    /// <summary>Field number for the "init" field.</summary>
    public const int InitFieldNumber = 10;
    /// <summary>
    /// Send latest match info when connection initialized.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchInfo Init {
      get { return msgCase_ == MsgOneofCase.Init ? (global::com.luxza.onlinematchserverclient.schema.MatchInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Init;
      }
    }

    /// <summary>Field number for the "recovery_response" field.</summary>
    public const int RecoveryResponseFieldNumber = 11;
    /// <summary>
    /// Response of recovery_request.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchInfo RecoveryResponse {
      get { return msgCase_ == MsgOneofCase.RecoveryResponse ? (global::com.luxza.onlinematchserverclient.schema.MatchInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RecoveryResponse;
      }
    }

    /// <summary>Field number for the "not_your_turn" field.</summary>
    public const int NotYourTurnFieldNumber = 12;
    /// <summary>
    /// Send latest match info when the client sent data at incorrect timing.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchInfo NotYourTurn {
      get { return msgCase_ == MsgOneofCase.NotYourTurn ? (global::com.luxza.onlinematchserverclient.schema.MatchInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.NotYourTurn;
      }
    }

    /// <summary>Field number for the "start" field.</summary>
    public const int StartFieldNumber = 13;
    /// <summary>
    /// Notify to be decided order and to be ready match.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchInfo Start {
      get { return msgCase_ == MsgOneofCase.Start ? (global::com.luxza.onlinematchserverclient.schema.MatchInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Start;
      }
    }

    /// <summary>Field number for the "cork_info" field.</summary>
    public const int CorkInfoFieldNumber = 14;
    /// <summary>
    /// Notify info related to cork.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkInfo CorkInfo {
      get { return msgCase_ == MsgOneofCase.CorkInfo ? (global::com.luxza.onlinematchserverclient.schema.CorkInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.CorkInfo;
      }
    }

    /// <summary>Field number for the "recovered" field.</summary>
    public const int RecoveredFieldNumber = 15;
    /// <summary>
    /// Notify recovered from crash (It is send at recovery_response is dequeued).
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.PlayerEvent Recovered {
      get { return msgCase_ == MsgOneofCase.Recovered ? (global::com.luxza.onlinematchserverclient.schema.PlayerEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.Recovered;
      }
    }

    /// <summary>Field number for the "relay_event" field.</summary>
    public const int RelayEventFieldNumber = 16;
    /// <summary>
    /// Relay event defined by client side.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayEvent RelayEvent {
      get { return msgCase_ == MsgOneofCase.RelayEvent ? (global::com.luxza.onlinematchserverclient.schema.RelayEvent) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RelayEvent;
      }
    }

    /// <summary>Field number for the "relay_score" field.</summary>
    public const int RelayScoreFieldNumber = 17;
    /// <summary>
    /// Relay score sent by the client to the others.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayScore RelayScore {
      get { return msgCase_ == MsgOneofCase.RelayScore ? (global::com.luxza.onlinematchserverclient.schema.RelayScore) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RelayScore;
      }
    }

    /// <summary>Field number for the "relay_middle_key" field.</summary>
    public const int RelayMiddleKeyFieldNumber = 18;
    /// <summary>
    /// Relay middle key sent by the client to the others.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey RelayMiddleKey {
      get { return msgCase_ == MsgOneofCase.RelayMiddleKey ? (global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.RelayMiddleKey;
      }
    }

    /// <summary>Field number for the "closed_by_admin" field.</summary>
    public const int ClosedByAdminFieldNumber = 19;
    /// <summary>
    /// Closed match by administrator (or autonomous system)
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty ClosedByAdmin {
      get { return msgCase_ == MsgOneofCase.ClosedByAdmin ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.ClosedByAdmin;
      }
    }

    /// <summary>Field number for the "closed_by_disqualify" field.</summary>
    public const int ClosedByDisqualifyFieldNumber = 20;
    /// <summary>
    /// Closed match by administrator (or autonomous system) for disqualification.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo ClosedByDisqualify {
      get { return msgCase_ == MsgOneofCase.ClosedByDisqualify ? (global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.ClosedByDisqualify;
      }
    }

    /// <summary>Field number for the "closed_by_rematch" field.</summary>
    public const int ClosedByRematchFieldNumber = 21;
    /// <summary>
    /// Closed match by administrator for a rematch.
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Google.Protobuf.WellKnownTypes.Empty ClosedByRematch {
      get { return msgCase_ == MsgOneofCase.ClosedByRematch ? (global::Google.Protobuf.WellKnownTypes.Empty) msg_ : null; }
      set {
        msg_ = value;
        msgCase_ = value == null ? MsgOneofCase.None : MsgOneofCase.ClosedByRematch;
      }
    }

    private object msg_;
    /// <summary>Enum of possible cases for the "msg" oneof.</summary>
    public enum MsgOneofCase {
      None = 0,
      Ready = 2,
      RelayKey = 3,
      MissedKey = 4,
      Timeout = 5,
      Disconnected = 6,
      Reconnect = 7,
      BluetoothError = 8,
      BluetoothRecover = 9,
      Init = 10,
      RecoveryResponse = 11,
      NotYourTurn = 12,
      Start = 13,
      CorkInfo = 14,
      Recovered = 15,
      RelayEvent = 16,
      RelayScore = 17,
      RelayMiddleKey = 18,
      ClosedByAdmin = 19,
      ClosedByDisqualify = 20,
      ClosedByRematch = 21,
    }
    private MsgOneofCase msgCase_ = MsgOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MsgOneofCase MsgCase {
      get { return msgCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearMsg() {
      msgCase_ = MsgOneofCase.None;
      msg_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ServerMsg);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ServerMsg other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MsgId != other.MsgId) return false;
      if (!object.Equals(Ready, other.Ready)) return false;
      if (!object.Equals(RelayKey, other.RelayKey)) return false;
      if (!object.Equals(MissedKey, other.MissedKey)) return false;
      if (!object.Equals(Timeout, other.Timeout)) return false;
      if (!object.Equals(Disconnected, other.Disconnected)) return false;
      if (!object.Equals(Reconnect, other.Reconnect)) return false;
      if (!object.Equals(BluetoothError, other.BluetoothError)) return false;
      if (!object.Equals(BluetoothRecover, other.BluetoothRecover)) return false;
      if (!object.Equals(Init, other.Init)) return false;
      if (!object.Equals(RecoveryResponse, other.RecoveryResponse)) return false;
      if (!object.Equals(NotYourTurn, other.NotYourTurn)) return false;
      if (!object.Equals(Start, other.Start)) return false;
      if (!object.Equals(CorkInfo, other.CorkInfo)) return false;
      if (!object.Equals(Recovered, other.Recovered)) return false;
      if (!object.Equals(RelayEvent, other.RelayEvent)) return false;
      if (!object.Equals(RelayScore, other.RelayScore)) return false;
      if (!object.Equals(RelayMiddleKey, other.RelayMiddleKey)) return false;
      if (!object.Equals(ClosedByAdmin, other.ClosedByAdmin)) return false;
      if (!object.Equals(ClosedByDisqualify, other.ClosedByDisqualify)) return false;
      if (!object.Equals(ClosedByRematch, other.ClosedByRematch)) return false;
      if (MsgCase != other.MsgCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MsgId != 0UL) hash ^= MsgId.GetHashCode();
      if (msgCase_ == MsgOneofCase.Ready) hash ^= Ready.GetHashCode();
      if (msgCase_ == MsgOneofCase.RelayKey) hash ^= RelayKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.MissedKey) hash ^= MissedKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.Timeout) hash ^= Timeout.GetHashCode();
      if (msgCase_ == MsgOneofCase.Disconnected) hash ^= Disconnected.GetHashCode();
      if (msgCase_ == MsgOneofCase.Reconnect) hash ^= Reconnect.GetHashCode();
      if (msgCase_ == MsgOneofCase.BluetoothError) hash ^= BluetoothError.GetHashCode();
      if (msgCase_ == MsgOneofCase.BluetoothRecover) hash ^= BluetoothRecover.GetHashCode();
      if (msgCase_ == MsgOneofCase.Init) hash ^= Init.GetHashCode();
      if (msgCase_ == MsgOneofCase.RecoveryResponse) hash ^= RecoveryResponse.GetHashCode();
      if (msgCase_ == MsgOneofCase.NotYourTurn) hash ^= NotYourTurn.GetHashCode();
      if (msgCase_ == MsgOneofCase.Start) hash ^= Start.GetHashCode();
      if (msgCase_ == MsgOneofCase.CorkInfo) hash ^= CorkInfo.GetHashCode();
      if (msgCase_ == MsgOneofCase.Recovered) hash ^= Recovered.GetHashCode();
      if (msgCase_ == MsgOneofCase.RelayEvent) hash ^= RelayEvent.GetHashCode();
      if (msgCase_ == MsgOneofCase.RelayScore) hash ^= RelayScore.GetHashCode();
      if (msgCase_ == MsgOneofCase.RelayMiddleKey) hash ^= RelayMiddleKey.GetHashCode();
      if (msgCase_ == MsgOneofCase.ClosedByAdmin) hash ^= ClosedByAdmin.GetHashCode();
      if (msgCase_ == MsgOneofCase.ClosedByDisqualify) hash ^= ClosedByDisqualify.GetHashCode();
      if (msgCase_ == MsgOneofCase.ClosedByRematch) hash ^= ClosedByRematch.GetHashCode();
      hash ^= (int) msgCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MsgId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MsgId);
      }
      if (msgCase_ == MsgOneofCase.Ready) {
        output.WriteRawTag(18);
        output.WriteMessage(Ready);
      }
      if (msgCase_ == MsgOneofCase.RelayKey) {
        output.WriteRawTag(26);
        output.WriteMessage(RelayKey);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        output.WriteRawTag(34);
        output.WriteMessage(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Timeout) {
        output.WriteRawTag(42);
        output.WriteMessage(Timeout);
      }
      if (msgCase_ == MsgOneofCase.Disconnected) {
        output.WriteRawTag(50);
        output.WriteMessage(Disconnected);
      }
      if (msgCase_ == MsgOneofCase.Reconnect) {
        output.WriteRawTag(58);
        output.WriteMessage(Reconnect);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        output.WriteRawTag(66);
        output.WriteMessage(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        output.WriteRawTag(74);
        output.WriteMessage(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.Init) {
        output.WriteRawTag(82);
        output.WriteMessage(Init);
      }
      if (msgCase_ == MsgOneofCase.RecoveryResponse) {
        output.WriteRawTag(90);
        output.WriteMessage(RecoveryResponse);
      }
      if (msgCase_ == MsgOneofCase.NotYourTurn) {
        output.WriteRawTag(98);
        output.WriteMessage(NotYourTurn);
      }
      if (msgCase_ == MsgOneofCase.Start) {
        output.WriteRawTag(106);
        output.WriteMessage(Start);
      }
      if (msgCase_ == MsgOneofCase.CorkInfo) {
        output.WriteRawTag(114);
        output.WriteMessage(CorkInfo);
      }
      if (msgCase_ == MsgOneofCase.Recovered) {
        output.WriteRawTag(122);
        output.WriteMessage(Recovered);
      }
      if (msgCase_ == MsgOneofCase.RelayEvent) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(RelayEvent);
      }
      if (msgCase_ == MsgOneofCase.RelayScore) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(RelayScore);
      }
      if (msgCase_ == MsgOneofCase.RelayMiddleKey) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(RelayMiddleKey);
      }
      if (msgCase_ == MsgOneofCase.ClosedByAdmin) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(ClosedByAdmin);
      }
      if (msgCase_ == MsgOneofCase.ClosedByDisqualify) {
        output.WriteRawTag(162, 1);
        output.WriteMessage(ClosedByDisqualify);
      }
      if (msgCase_ == MsgOneofCase.ClosedByRematch) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(ClosedByRematch);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MsgId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(MsgId);
      }
      if (msgCase_ == MsgOneofCase.Ready) {
        output.WriteRawTag(18);
        output.WriteMessage(Ready);
      }
      if (msgCase_ == MsgOneofCase.RelayKey) {
        output.WriteRawTag(26);
        output.WriteMessage(RelayKey);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        output.WriteRawTag(34);
        output.WriteMessage(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Timeout) {
        output.WriteRawTag(42);
        output.WriteMessage(Timeout);
      }
      if (msgCase_ == MsgOneofCase.Disconnected) {
        output.WriteRawTag(50);
        output.WriteMessage(Disconnected);
      }
      if (msgCase_ == MsgOneofCase.Reconnect) {
        output.WriteRawTag(58);
        output.WriteMessage(Reconnect);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        output.WriteRawTag(66);
        output.WriteMessage(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        output.WriteRawTag(74);
        output.WriteMessage(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.Init) {
        output.WriteRawTag(82);
        output.WriteMessage(Init);
      }
      if (msgCase_ == MsgOneofCase.RecoveryResponse) {
        output.WriteRawTag(90);
        output.WriteMessage(RecoveryResponse);
      }
      if (msgCase_ == MsgOneofCase.NotYourTurn) {
        output.WriteRawTag(98);
        output.WriteMessage(NotYourTurn);
      }
      if (msgCase_ == MsgOneofCase.Start) {
        output.WriteRawTag(106);
        output.WriteMessage(Start);
      }
      if (msgCase_ == MsgOneofCase.CorkInfo) {
        output.WriteRawTag(114);
        output.WriteMessage(CorkInfo);
      }
      if (msgCase_ == MsgOneofCase.Recovered) {
        output.WriteRawTag(122);
        output.WriteMessage(Recovered);
      }
      if (msgCase_ == MsgOneofCase.RelayEvent) {
        output.WriteRawTag(130, 1);
        output.WriteMessage(RelayEvent);
      }
      if (msgCase_ == MsgOneofCase.RelayScore) {
        output.WriteRawTag(138, 1);
        output.WriteMessage(RelayScore);
      }
      if (msgCase_ == MsgOneofCase.RelayMiddleKey) {
        output.WriteRawTag(146, 1);
        output.WriteMessage(RelayMiddleKey);
      }
      if (msgCase_ == MsgOneofCase.ClosedByAdmin) {
        output.WriteRawTag(154, 1);
        output.WriteMessage(ClosedByAdmin);
      }
      if (msgCase_ == MsgOneofCase.ClosedByDisqualify) {
        output.WriteRawTag(162, 1);
        output.WriteMessage(ClosedByDisqualify);
      }
      if (msgCase_ == MsgOneofCase.ClosedByRematch) {
        output.WriteRawTag(170, 1);
        output.WriteMessage(ClosedByRematch);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MsgId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(MsgId);
      }
      if (msgCase_ == MsgOneofCase.Ready) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Ready);
      }
      if (msgCase_ == MsgOneofCase.RelayKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RelayKey);
      }
      if (msgCase_ == MsgOneofCase.MissedKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MissedKey);
      }
      if (msgCase_ == MsgOneofCase.Timeout) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Timeout);
      }
      if (msgCase_ == MsgOneofCase.Disconnected) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Disconnected);
      }
      if (msgCase_ == MsgOneofCase.Reconnect) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Reconnect);
      }
      if (msgCase_ == MsgOneofCase.BluetoothError) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(BluetoothError);
      }
      if (msgCase_ == MsgOneofCase.BluetoothRecover) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(BluetoothRecover);
      }
      if (msgCase_ == MsgOneofCase.Init) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Init);
      }
      if (msgCase_ == MsgOneofCase.RecoveryResponse) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RecoveryResponse);
      }
      if (msgCase_ == MsgOneofCase.NotYourTurn) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(NotYourTurn);
      }
      if (msgCase_ == MsgOneofCase.Start) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Start);
      }
      if (msgCase_ == MsgOneofCase.CorkInfo) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(CorkInfo);
      }
      if (msgCase_ == MsgOneofCase.Recovered) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Recovered);
      }
      if (msgCase_ == MsgOneofCase.RelayEvent) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RelayEvent);
      }
      if (msgCase_ == MsgOneofCase.RelayScore) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RelayScore);
      }
      if (msgCase_ == MsgOneofCase.RelayMiddleKey) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(RelayMiddleKey);
      }
      if (msgCase_ == MsgOneofCase.ClosedByAdmin) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(ClosedByAdmin);
      }
      if (msgCase_ == MsgOneofCase.ClosedByDisqualify) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(ClosedByDisqualify);
      }
      if (msgCase_ == MsgOneofCase.ClosedByRematch) {
        size += 2 + pb::CodedOutputStream.ComputeMessageSize(ClosedByRematch);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ServerMsg other) {
      if (other == null) {
        return;
      }
      if (other.MsgId != 0UL) {
        MsgId = other.MsgId;
      }
      switch (other.MsgCase) {
        case MsgOneofCase.Ready:
          if (Ready == null) {
            Ready = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          Ready.MergeFrom(other.Ready);
          break;
        case MsgOneofCase.RelayKey:
          if (RelayKey == null) {
            RelayKey = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
          }
          RelayKey.MergeFrom(other.RelayKey);
          break;
        case MsgOneofCase.MissedKey:
          if (MissedKey == null) {
            MissedKey = new global::com.luxza.onlinematchserverclient.schema.MissedKey();
          }
          MissedKey.MergeFrom(other.MissedKey);
          break;
        case MsgOneofCase.Timeout:
          if (Timeout == null) {
            Timeout = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          Timeout.MergeFrom(other.Timeout);
          break;
        case MsgOneofCase.Disconnected:
          if (Disconnected == null) {
            Disconnected = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          Disconnected.MergeFrom(other.Disconnected);
          break;
        case MsgOneofCase.Reconnect:
          if (Reconnect == null) {
            Reconnect = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          Reconnect.MergeFrom(other.Reconnect);
          break;
        case MsgOneofCase.BluetoothError:
          if (BluetoothError == null) {
            BluetoothError = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          BluetoothError.MergeFrom(other.BluetoothError);
          break;
        case MsgOneofCase.BluetoothRecover:
          if (BluetoothRecover == null) {
            BluetoothRecover = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          BluetoothRecover.MergeFrom(other.BluetoothRecover);
          break;
        case MsgOneofCase.Init:
          if (Init == null) {
            Init = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
          }
          Init.MergeFrom(other.Init);
          break;
        case MsgOneofCase.RecoveryResponse:
          if (RecoveryResponse == null) {
            RecoveryResponse = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
          }
          RecoveryResponse.MergeFrom(other.RecoveryResponse);
          break;
        case MsgOneofCase.NotYourTurn:
          if (NotYourTurn == null) {
            NotYourTurn = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
          }
          NotYourTurn.MergeFrom(other.NotYourTurn);
          break;
        case MsgOneofCase.Start:
          if (Start == null) {
            Start = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
          }
          Start.MergeFrom(other.Start);
          break;
        case MsgOneofCase.CorkInfo:
          if (CorkInfo == null) {
            CorkInfo = new global::com.luxza.onlinematchserverclient.schema.CorkInfo();
          }
          CorkInfo.MergeFrom(other.CorkInfo);
          break;
        case MsgOneofCase.Recovered:
          if (Recovered == null) {
            Recovered = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
          }
          Recovered.MergeFrom(other.Recovered);
          break;
        case MsgOneofCase.RelayEvent:
          if (RelayEvent == null) {
            RelayEvent = new global::com.luxza.onlinematchserverclient.schema.RelayEvent();
          }
          RelayEvent.MergeFrom(other.RelayEvent);
          break;
        case MsgOneofCase.RelayScore:
          if (RelayScore == null) {
            RelayScore = new global::com.luxza.onlinematchserverclient.schema.RelayScore();
          }
          RelayScore.MergeFrom(other.RelayScore);
          break;
        case MsgOneofCase.RelayMiddleKey:
          if (RelayMiddleKey == null) {
            RelayMiddleKey = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
          }
          RelayMiddleKey.MergeFrom(other.RelayMiddleKey);
          break;
        case MsgOneofCase.ClosedByAdmin:
          if (ClosedByAdmin == null) {
            ClosedByAdmin = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          ClosedByAdmin.MergeFrom(other.ClosedByAdmin);
          break;
        case MsgOneofCase.ClosedByDisqualify:
          if (ClosedByDisqualify == null) {
            ClosedByDisqualify = new global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo();
          }
          ClosedByDisqualify.MergeFrom(other.ClosedByDisqualify);
          break;
        case MsgOneofCase.ClosedByRematch:
          if (ClosedByRematch == null) {
            ClosedByRematch = new global::Google.Protobuf.WellKnownTypes.Empty();
          }
          ClosedByRematch.MergeFrom(other.ClosedByRematch);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            MsgId = input.ReadUInt64();
            break;
          }
          case 18: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.Ready) {
              subBuilder.MergeFrom(Ready);
            }
            input.ReadMessage(subBuilder);
            Ready = subBuilder;
            break;
          }
          case 26: {
            global::com.luxza.onlinematchserverclient.schema.RelayKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
            if (msgCase_ == MsgOneofCase.RelayKey) {
              subBuilder.MergeFrom(RelayKey);
            }
            input.ReadMessage(subBuilder);
            RelayKey = subBuilder;
            break;
          }
          case 34: {
            global::com.luxza.onlinematchserverclient.schema.MissedKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.MissedKey();
            if (msgCase_ == MsgOneofCase.MissedKey) {
              subBuilder.MergeFrom(MissedKey);
            }
            input.ReadMessage(subBuilder);
            MissedKey = subBuilder;
            break;
          }
          case 42: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Timeout) {
              subBuilder.MergeFrom(Timeout);
            }
            input.ReadMessage(subBuilder);
            Timeout = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Disconnected) {
              subBuilder.MergeFrom(Disconnected);
            }
            input.ReadMessage(subBuilder);
            Disconnected = subBuilder;
            break;
          }
          case 58: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Reconnect) {
              subBuilder.MergeFrom(Reconnect);
            }
            input.ReadMessage(subBuilder);
            Reconnect = subBuilder;
            break;
          }
          case 66: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.BluetoothError) {
              subBuilder.MergeFrom(BluetoothError);
            }
            input.ReadMessage(subBuilder);
            BluetoothError = subBuilder;
            break;
          }
          case 74: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.BluetoothRecover) {
              subBuilder.MergeFrom(BluetoothRecover);
            }
            input.ReadMessage(subBuilder);
            BluetoothRecover = subBuilder;
            break;
          }
          case 82: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.Init) {
              subBuilder.MergeFrom(Init);
            }
            input.ReadMessage(subBuilder);
            Init = subBuilder;
            break;
          }
          case 90: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.RecoveryResponse) {
              subBuilder.MergeFrom(RecoveryResponse);
            }
            input.ReadMessage(subBuilder);
            RecoveryResponse = subBuilder;
            break;
          }
          case 98: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.NotYourTurn) {
              subBuilder.MergeFrom(NotYourTurn);
            }
            input.ReadMessage(subBuilder);
            NotYourTurn = subBuilder;
            break;
          }
          case 106: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.Start) {
              subBuilder.MergeFrom(Start);
            }
            input.ReadMessage(subBuilder);
            Start = subBuilder;
            break;
          }
          case 114: {
            global::com.luxza.onlinematchserverclient.schema.CorkInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkInfo();
            if (msgCase_ == MsgOneofCase.CorkInfo) {
              subBuilder.MergeFrom(CorkInfo);
            }
            input.ReadMessage(subBuilder);
            CorkInfo = subBuilder;
            break;
          }
          case 122: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Recovered) {
              subBuilder.MergeFrom(Recovered);
            }
            input.ReadMessage(subBuilder);
            Recovered = subBuilder;
            break;
          }
          case 130: {
            global::com.luxza.onlinematchserverclient.schema.RelayEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayEvent();
            if (msgCase_ == MsgOneofCase.RelayEvent) {
              subBuilder.MergeFrom(RelayEvent);
            }
            input.ReadMessage(subBuilder);
            RelayEvent = subBuilder;
            break;
          }
          case 138: {
            global::com.luxza.onlinematchserverclient.schema.RelayScore subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayScore();
            if (msgCase_ == MsgOneofCase.RelayScore) {
              subBuilder.MergeFrom(RelayScore);
            }
            input.ReadMessage(subBuilder);
            RelayScore = subBuilder;
            break;
          }
          case 146: {
            global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
            if (msgCase_ == MsgOneofCase.RelayMiddleKey) {
              subBuilder.MergeFrom(RelayMiddleKey);
            }
            input.ReadMessage(subBuilder);
            RelayMiddleKey = subBuilder;
            break;
          }
          case 154: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.ClosedByAdmin) {
              subBuilder.MergeFrom(ClosedByAdmin);
            }
            input.ReadMessage(subBuilder);
            ClosedByAdmin = subBuilder;
            break;
          }
          case 162: {
            global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo();
            if (msgCase_ == MsgOneofCase.ClosedByDisqualify) {
              subBuilder.MergeFrom(ClosedByDisqualify);
            }
            input.ReadMessage(subBuilder);
            ClosedByDisqualify = subBuilder;
            break;
          }
          case 170: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.ClosedByRematch) {
              subBuilder.MergeFrom(ClosedByRematch);
            }
            input.ReadMessage(subBuilder);
            ClosedByRematch = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            MsgId = input.ReadUInt64();
            break;
          }
          case 18: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.Ready) {
              subBuilder.MergeFrom(Ready);
            }
            input.ReadMessage(subBuilder);
            Ready = subBuilder;
            break;
          }
          case 26: {
            global::com.luxza.onlinematchserverclient.schema.RelayKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
            if (msgCase_ == MsgOneofCase.RelayKey) {
              subBuilder.MergeFrom(RelayKey);
            }
            input.ReadMessage(subBuilder);
            RelayKey = subBuilder;
            break;
          }
          case 34: {
            global::com.luxza.onlinematchserverclient.schema.MissedKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.MissedKey();
            if (msgCase_ == MsgOneofCase.MissedKey) {
              subBuilder.MergeFrom(MissedKey);
            }
            input.ReadMessage(subBuilder);
            MissedKey = subBuilder;
            break;
          }
          case 42: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Timeout) {
              subBuilder.MergeFrom(Timeout);
            }
            input.ReadMessage(subBuilder);
            Timeout = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Disconnected) {
              subBuilder.MergeFrom(Disconnected);
            }
            input.ReadMessage(subBuilder);
            Disconnected = subBuilder;
            break;
          }
          case 58: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Reconnect) {
              subBuilder.MergeFrom(Reconnect);
            }
            input.ReadMessage(subBuilder);
            Reconnect = subBuilder;
            break;
          }
          case 66: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.BluetoothError) {
              subBuilder.MergeFrom(BluetoothError);
            }
            input.ReadMessage(subBuilder);
            BluetoothError = subBuilder;
            break;
          }
          case 74: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.BluetoothRecover) {
              subBuilder.MergeFrom(BluetoothRecover);
            }
            input.ReadMessage(subBuilder);
            BluetoothRecover = subBuilder;
            break;
          }
          case 82: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.Init) {
              subBuilder.MergeFrom(Init);
            }
            input.ReadMessage(subBuilder);
            Init = subBuilder;
            break;
          }
          case 90: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.RecoveryResponse) {
              subBuilder.MergeFrom(RecoveryResponse);
            }
            input.ReadMessage(subBuilder);
            RecoveryResponse = subBuilder;
            break;
          }
          case 98: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.NotYourTurn) {
              subBuilder.MergeFrom(NotYourTurn);
            }
            input.ReadMessage(subBuilder);
            NotYourTurn = subBuilder;
            break;
          }
          case 106: {
            global::com.luxza.onlinematchserverclient.schema.MatchInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            if (msgCase_ == MsgOneofCase.Start) {
              subBuilder.MergeFrom(Start);
            }
            input.ReadMessage(subBuilder);
            Start = subBuilder;
            break;
          }
          case 114: {
            global::com.luxza.onlinematchserverclient.schema.CorkInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkInfo();
            if (msgCase_ == MsgOneofCase.CorkInfo) {
              subBuilder.MergeFrom(CorkInfo);
            }
            input.ReadMessage(subBuilder);
            CorkInfo = subBuilder;
            break;
          }
          case 122: {
            global::com.luxza.onlinematchserverclient.schema.PlayerEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.PlayerEvent();
            if (msgCase_ == MsgOneofCase.Recovered) {
              subBuilder.MergeFrom(Recovered);
            }
            input.ReadMessage(subBuilder);
            Recovered = subBuilder;
            break;
          }
          case 130: {
            global::com.luxza.onlinematchserverclient.schema.RelayEvent subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayEvent();
            if (msgCase_ == MsgOneofCase.RelayEvent) {
              subBuilder.MergeFrom(RelayEvent);
            }
            input.ReadMessage(subBuilder);
            RelayEvent = subBuilder;
            break;
          }
          case 138: {
            global::com.luxza.onlinematchserverclient.schema.RelayScore subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayScore();
            if (msgCase_ == MsgOneofCase.RelayScore) {
              subBuilder.MergeFrom(RelayScore);
            }
            input.ReadMessage(subBuilder);
            RelayScore = subBuilder;
            break;
          }
          case 146: {
            global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
            if (msgCase_ == MsgOneofCase.RelayMiddleKey) {
              subBuilder.MergeFrom(RelayMiddleKey);
            }
            input.ReadMessage(subBuilder);
            RelayMiddleKey = subBuilder;
            break;
          }
          case 154: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.ClosedByAdmin) {
              subBuilder.MergeFrom(ClosedByAdmin);
            }
            input.ReadMessage(subBuilder);
            ClosedByAdmin = subBuilder;
            break;
          }
          case 162: {
            global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo subBuilder = new global::com.luxza.onlinematchserverclient.schema.DisqualificationInfo();
            if (msgCase_ == MsgOneofCase.ClosedByDisqualify) {
              subBuilder.MergeFrom(ClosedByDisqualify);
            }
            input.ReadMessage(subBuilder);
            ClosedByDisqualify = subBuilder;
            break;
          }
          case 170: {
            global::Google.Protobuf.WellKnownTypes.Empty subBuilder = new global::Google.Protobuf.WellKnownTypes.Empty();
            if (msgCase_ == MsgOneofCase.ClosedByRematch) {
              subBuilder.MergeFrom(ClosedByRematch);
            }
            input.ReadMessage(subBuilder);
            ClosedByRematch = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class MissedKey : pb::IMessage<MissedKey>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MissedKey> _parser = new pb::MessageParser<MissedKey>(() => new MissedKey());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MissedKey> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MissedKey() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MissedKey(MissedKey other) : this() {
      keyId_ = other.keyId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MissedKey Clone() {
      return new MissedKey(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MissedKey);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MissedKey other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MissedKey other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RelayKey : pb::IMessage<RelayKey>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RelayKey> _parser = new pb::MessageParser<RelayKey>(() => new RelayKey());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RelayKey> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayKey() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayKey(RelayKey other) : this() {
      granId_ = other.granId_;
      keyId_ = other.keyId_;
      value_ = other.value_;
      hitPosition_ = other.hitPosition_ != null ? other.hitPosition_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayKey Clone() {
      return new RelayKey(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 2;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.KeyValue value_ = global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.KeyValue Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "hit_position" field.</summary>
    public const int HitPositionFieldNumber = 4;
    private global::com.luxza.onlinematchserverclient.schema.HitPosition hitPosition_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.HitPosition HitPosition {
      get { return hitPosition_; }
      set {
        hitPosition_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RelayKey);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RelayKey other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      if (!object.Equals(HitPosition, other.HitPosition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) hash ^= Value.GetHashCode();
      if (hitPosition_ != null) hash ^= HitPosition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Value);
      }
      if (hitPosition_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(HitPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Value);
      }
      if (hitPosition_ != null) {
        output.WriteRawTag(34);
        output.WriteMessage(HitPosition);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Value);
      }
      if (hitPosition_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(HitPosition);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RelayKey other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown) {
        Value = other.Value;
      }
      if (other.hitPosition_ != null) {
        if (hitPosition_ == null) {
          HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
        }
        HitPosition.MergeFrom(other.HitPosition);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = (global::com.luxza.onlinematchserverclient.schema.KeyValue) input.ReadEnum();
            break;
          }
          case 34: {
            if (hitPosition_ == null) {
              HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
            }
            input.ReadMessage(HitPosition);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = (global::com.luxza.onlinematchserverclient.schema.KeyValue) input.ReadEnum();
            break;
          }
          case 34: {
            if (hitPosition_ == null) {
              HitPosition = new global::com.luxza.onlinematchserverclient.schema.HitPosition();
            }
            input.ReadMessage(HitPosition);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RelayScore : pb::IMessage<RelayScore>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RelayScore> _parser = new pb::MessageParser<RelayScore>(() => new RelayScore());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RelayScore> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayScore() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayScore(RelayScore other) : this() {
      granId_ = other.granId_;
      keyId_ = other.keyId_;
      value_ = other.value_;
      checkout_ = other.checkout_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayScore Clone() {
      return new RelayScore(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 2;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 3;
    private int value_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    /// <summary>Field number for the "checkout" field.</summary>
    public const int CheckoutFieldNumber = 4;
    private global::com.luxza.onlinematchserverclient.schema.CheckOutChoices checkout_ = global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CheckOutChoices Checkout {
      get { return checkout_; }
      set {
        checkout_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RelayScore);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RelayScore other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      if (Checkout != other.Checkout) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != 0) hash ^= Value.GetHashCode();
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) hash ^= Checkout.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Checkout);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != 0) {
        output.WriteRawTag(24);
        output.WriteInt32(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        output.WriteRawTag(32);
        output.WriteEnum((int) Checkout);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Value);
      }
      if (Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Checkout);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RelayScore other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != 0) {
        Value = other.Value;
      }
      if (other.Checkout != global::com.luxza.onlinematchserverclient.schema.CheckOutChoices.NotCheckout) {
        Checkout = other.Checkout;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = input.ReadInt32();
            break;
          }
          case 32: {
            Checkout = (global::com.luxza.onlinematchserverclient.schema.CheckOutChoices) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = input.ReadInt32();
            break;
          }
          case 32: {
            Checkout = (global::com.luxza.onlinematchserverclient.schema.CheckOutChoices) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RelayMiddleKey : pb::IMessage<RelayMiddleKey>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RelayMiddleKey> _parser = new pb::MessageParser<RelayMiddleKey>(() => new RelayMiddleKey());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RelayMiddleKey> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayMiddleKey() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayMiddleKey(RelayMiddleKey other) : this() {
      granId_ = other.granId_;
      keyId_ = other.keyId_;
      value_ = other.value_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayMiddleKey Clone() {
      return new RelayMiddleKey(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 2;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "value" field.</summary>
    public const int ValueFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.MiddleChoices value_ = global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MiddleChoices Value {
      get { return value_; }
      set {
        value_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RelayMiddleKey);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RelayMiddleKey other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      if (KeyId != other.KeyId) return false;
      if (Value != other.Value) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) hash ^= Value.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (KeyId.Length != 0) {
        output.WriteRawTag(18);
        output.WriteString(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Value);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Value);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RelayMiddleKey other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Value != global::com.luxza.onlinematchserverclient.schema.MiddleChoices.MiddleAgain) {
        Value = other.Value;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = (global::com.luxza.onlinematchserverclient.schema.MiddleChoices) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            KeyId = input.ReadString();
            break;
          }
          case 24: {
            Value = (global::com.luxza.onlinematchserverclient.schema.MiddleChoices) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class PlayerEvent : pb::IMessage<PlayerEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PlayerEvent> _parser = new pb::MessageParser<PlayerEvent>(() => new PlayerEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PlayerEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerEvent(PlayerEvent other) : this() {
      granId_ = other.granId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerEvent Clone() {
      return new PlayerEvent(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PlayerEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PlayerEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PlayerEvent other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class MatchInfo : pb::IMessage<MatchInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MatchInfo> _parser = new pb::MessageParser<MatchInfo>(() => new MatchInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MatchInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[19]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchInfo(MatchInfo other) : this() {
      progress_ = other.progress_;
      players_ = other.players_.Clone();
      code_ = other.code_;
      corkHistory_ = other.corkHistory_.Clone();
      middleHistory_ = other.middleHistory_.Clone();
      keyHistory_ = other.keyHistory_.Clone();
      legNumber_ = other.legNumber_;
      isFinished_ = other.isFinished_;
      corkStatus_ = other.corkStatus_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchInfo Clone() {
      return new MatchInfo(this);
    }

    /// <summary>Field number for the "progress" field.</summary>
    public const int ProgressFieldNumber = 1;
    private uint progress_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Progress {
      get { return progress_; }
      set {
        progress_ = value;
      }
    }

    /// <summary>Field number for the "players" field.</summary>
    public const int PlayersFieldNumber = 2;
    private static readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.PlayerInfo>.Codec _map_players_codec
        = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.PlayerInfo>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::com.luxza.onlinematchserverclient.schema.PlayerInfo.Parser), 18);
    private readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.PlayerInfo> players_ = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.PlayerInfo>();
    /// <summary>
    /// key: gran_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.PlayerInfo> Players {
      get { return players_; }
    }

    /// <summary>Field number for the "code" field.</summary>
    public const int CodeFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.GameCode code_ = global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.GameCode Code {
      get { return code_; }
      set {
        code_ = value;
      }
    }

    /// <summary>Field number for the "cork_history" field.</summary>
    public const int CorkHistoryFieldNumber = 4;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.RelayKey> _repeated_corkHistory_codec
        = pb::FieldCodec.ForMessage(34, global::com.luxza.onlinematchserverclient.schema.RelayKey.Parser);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayKey> corkHistory_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayKey>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayKey> CorkHistory {
      get { return corkHistory_; }
    }

    /// <summary>Field number for the "middle_history" field.</summary>
    public const int MiddleHistoryFieldNumber = 5;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey> _repeated_middleHistory_codec
        = pb::FieldCodec.ForMessage(42, global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey.Parser);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey> middleHistory_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey> MiddleHistory {
      get { return middleHistory_; }
    }

    /// <summary>Field number for the "key_history" field.</summary>
    public const int KeyHistoryFieldNumber = 6;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.KeyOrScore> _repeated_keyHistory_codec
        = pb::FieldCodec.ForMessage(50, global::com.luxza.onlinematchserverclient.schema.KeyOrScore.Parser);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.KeyOrScore> keyHistory_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.KeyOrScore>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.KeyOrScore> KeyHistory {
      get { return keyHistory_; }
    }

    /// <summary>Field number for the "leg_number" field.</summary>
    public const int LegNumberFieldNumber = 7;
    private uint legNumber_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint LegNumber {
      get { return legNumber_; }
      set {
        legNumber_ = value;
      }
    }

    /// <summary>Field number for the "is_finished" field.</summary>
    public const int IsFinishedFieldNumber = 8;
    private bool isFinished_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool IsFinished {
      get { return isFinished_; }
      set {
        isFinished_ = value;
      }
    }

    /// <summary>Field number for the "cork_status" field.</summary>
    public const int CorkStatusFieldNumber = 9;
    private global::com.luxza.onlinematchserverclient.schema.CorkStatus corkStatus_ = global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkStatus CorkStatus {
      get { return corkStatus_; }
      set {
        corkStatus_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MatchInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MatchInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Progress != other.Progress) return false;
      if (!Players.Equals(other.Players)) return false;
      if (Code != other.Code) return false;
      if(!corkHistory_.Equals(other.corkHistory_)) return false;
      if(!middleHistory_.Equals(other.middleHistory_)) return false;
      if(!keyHistory_.Equals(other.keyHistory_)) return false;
      if (LegNumber != other.LegNumber) return false;
      if (IsFinished != other.IsFinished) return false;
      if (CorkStatus != other.CorkStatus) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Progress != 0) hash ^= Progress.GetHashCode();
      hash ^= Players.GetHashCode();
      if (Code != global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite) hash ^= Code.GetHashCode();
      hash ^= corkHistory_.GetHashCode();
      hash ^= middleHistory_.GetHashCode();
      hash ^= keyHistory_.GetHashCode();
      if (LegNumber != 0) hash ^= LegNumber.GetHashCode();
      if (IsFinished != false) hash ^= IsFinished.GetHashCode();
      if (CorkStatus != global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary) hash ^= CorkStatus.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Progress != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Progress);
      }
      players_.WriteTo(output, _map_players_codec);
      if (Code != global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Code);
      }
      corkHistory_.WriteTo(output, _repeated_corkHistory_codec);
      middleHistory_.WriteTo(output, _repeated_middleHistory_codec);
      keyHistory_.WriteTo(output, _repeated_keyHistory_codec);
      if (LegNumber != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(LegNumber);
      }
      if (IsFinished != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsFinished);
      }
      if (CorkStatus != global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary) {
        output.WriteRawTag(72);
        output.WriteEnum((int) CorkStatus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Progress != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Progress);
      }
      players_.WriteTo(ref output, _map_players_codec);
      if (Code != global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Code);
      }
      corkHistory_.WriteTo(ref output, _repeated_corkHistory_codec);
      middleHistory_.WriteTo(ref output, _repeated_middleHistory_codec);
      keyHistory_.WriteTo(ref output, _repeated_keyHistory_codec);
      if (LegNumber != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(LegNumber);
      }
      if (IsFinished != false) {
        output.WriteRawTag(64);
        output.WriteBool(IsFinished);
      }
      if (CorkStatus != global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary) {
        output.WriteRawTag(72);
        output.WriteEnum((int) CorkStatus);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Progress != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Progress);
      }
      size += players_.CalculateSize(_map_players_codec);
      if (Code != global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Code);
      }
      size += corkHistory_.CalculateSize(_repeated_corkHistory_codec);
      size += middleHistory_.CalculateSize(_repeated_middleHistory_codec);
      size += keyHistory_.CalculateSize(_repeated_keyHistory_codec);
      if (LegNumber != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(LegNumber);
      }
      if (IsFinished != false) {
        size += 1 + 1;
      }
      if (CorkStatus != global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) CorkStatus);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MatchInfo other) {
      if (other == null) {
        return;
      }
      if (other.Progress != 0) {
        Progress = other.Progress;
      }
      players_.Add(other.players_);
      if (other.Code != global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite) {
        Code = other.Code;
      }
      corkHistory_.Add(other.corkHistory_);
      middleHistory_.Add(other.middleHistory_);
      keyHistory_.Add(other.keyHistory_);
      if (other.LegNumber != 0) {
        LegNumber = other.LegNumber;
      }
      if (other.IsFinished != false) {
        IsFinished = other.IsFinished;
      }
      if (other.CorkStatus != global::com.luxza.onlinematchserverclient.schema.CorkStatus.Unnecessary) {
        CorkStatus = other.CorkStatus;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Progress = input.ReadUInt32();
            break;
          }
          case 18: {
            players_.AddEntriesFrom(input, _map_players_codec);
            break;
          }
          case 24: {
            Code = (global::com.luxza.onlinematchserverclient.schema.GameCode) input.ReadEnum();
            break;
          }
          case 34: {
            corkHistory_.AddEntriesFrom(input, _repeated_corkHistory_codec);
            break;
          }
          case 42: {
            middleHistory_.AddEntriesFrom(input, _repeated_middleHistory_codec);
            break;
          }
          case 50: {
            keyHistory_.AddEntriesFrom(input, _repeated_keyHistory_codec);
            break;
          }
          case 56: {
            LegNumber = input.ReadUInt32();
            break;
          }
          case 64: {
            IsFinished = input.ReadBool();
            break;
          }
          case 72: {
            CorkStatus = (global::com.luxza.onlinematchserverclient.schema.CorkStatus) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Progress = input.ReadUInt32();
            break;
          }
          case 18: {
            players_.AddEntriesFrom(ref input, _map_players_codec);
            break;
          }
          case 24: {
            Code = (global::com.luxza.onlinematchserverclient.schema.GameCode) input.ReadEnum();
            break;
          }
          case 34: {
            corkHistory_.AddEntriesFrom(ref input, _repeated_corkHistory_codec);
            break;
          }
          case 42: {
            middleHistory_.AddEntriesFrom(ref input, _repeated_middleHistory_codec);
            break;
          }
          case 50: {
            keyHistory_.AddEntriesFrom(ref input, _repeated_keyHistory_codec);
            break;
          }
          case 56: {
            LegNumber = input.ReadUInt32();
            break;
          }
          case 64: {
            IsFinished = input.ReadBool();
            break;
          }
          case 72: {
            CorkStatus = (global::com.luxza.onlinematchserverclient.schema.CorkStatus) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class PlayerInfo : pb::IMessage<PlayerInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PlayerInfo> _parser = new pb::MessageParser<PlayerInfo>(() => new PlayerInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PlayerInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[20]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerInfo(PlayerInfo other) : this() {
      order_ = other.order_;
      results_ = other.results_.Clone();
      scores_ = other.scores_.Clone();
      hitPosition_ = other.hitPosition_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PlayerInfo Clone() {
      return new PlayerInfo(this);
    }

    /// <summary>Field number for the "order" field.</summary>
    public const int OrderFieldNumber = 1;
    private uint order_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Order {
      get { return order_; }
      set {
        order_ = value;
      }
    }

    /// <summary>Field number for the "results" field.</summary>
    public const int ResultsFieldNumber = 2;
    private static readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.KeyValue>.Codec _map_results_codec
        = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.KeyValue>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForEnum(16, x => (int) x, x => (global::com.luxza.onlinematchserverclient.schema.KeyValue) x, global::com.luxza.onlinematchserverclient.schema.KeyValue.Unknown), 18);
    private readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.KeyValue> results_ = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.KeyValue>();
    /// <summary>
    /// key: key_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.KeyValue> Results {
      get { return results_; }
    }

    /// <summary>Field number for the "scores" field.</summary>
    public const int ScoresFieldNumber = 3;
    private static readonly pbc::MapField<string, int>.Codec _map_scores_codec
        = new pbc::MapField<string, int>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForInt32(16, 0), 26);
    private readonly pbc::MapField<string, int> scores_ = new pbc::MapField<string, int>();
    /// <summary>
    /// key: round
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, int> Scores {
      get { return scores_; }
    }

    /// <summary>Field number for the "hit_position" field.</summary>
    public const int HitPositionFieldNumber = 4;
    private static readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.HitPosition>.Codec _map_hitPosition_codec
        = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.HitPosition>.Codec(pb::FieldCodec.ForString(10, ""), pb::FieldCodec.ForMessage(18, global::com.luxza.onlinematchserverclient.schema.HitPosition.Parser), 34);
    private readonly pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.HitPosition> hitPosition_ = new pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.HitPosition>();
    /// <summary>
    /// key: key_id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::MapField<string, global::com.luxza.onlinematchserverclient.schema.HitPosition> HitPosition {
      get { return hitPosition_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PlayerInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PlayerInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Order != other.Order) return false;
      if (!Results.Equals(other.Results)) return false;
      if (!Scores.Equals(other.Scores)) return false;
      if (!HitPosition.Equals(other.HitPosition)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Order != 0) hash ^= Order.GetHashCode();
      hash ^= Results.GetHashCode();
      hash ^= Scores.GetHashCode();
      hash ^= HitPosition.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Order != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Order);
      }
      results_.WriteTo(output, _map_results_codec);
      scores_.WriteTo(output, _map_scores_codec);
      hitPosition_.WriteTo(output, _map_hitPosition_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Order != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Order);
      }
      results_.WriteTo(ref output, _map_results_codec);
      scores_.WriteTo(ref output, _map_scores_codec);
      hitPosition_.WriteTo(ref output, _map_hitPosition_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Order != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Order);
      }
      size += results_.CalculateSize(_map_results_codec);
      size += scores_.CalculateSize(_map_scores_codec);
      size += hitPosition_.CalculateSize(_map_hitPosition_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PlayerInfo other) {
      if (other == null) {
        return;
      }
      if (other.Order != 0) {
        Order = other.Order;
      }
      results_.Add(other.results_);
      scores_.Add(other.scores_);
      hitPosition_.Add(other.hitPosition_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Order = input.ReadUInt32();
            break;
          }
          case 18: {
            results_.AddEntriesFrom(input, _map_results_codec);
            break;
          }
          case 26: {
            scores_.AddEntriesFrom(input, _map_scores_codec);
            break;
          }
          case 34: {
            hitPosition_.AddEntriesFrom(input, _map_hitPosition_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Order = input.ReadUInt32();
            break;
          }
          case 18: {
            results_.AddEntriesFrom(ref input, _map_results_codec);
            break;
          }
          case 26: {
            scores_.AddEntriesFrom(ref input, _map_scores_codec);
            break;
          }
          case 34: {
            hitPosition_.AddEntriesFrom(ref input, _map_hitPosition_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class CorkResult : pb::IMessage<CorkResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CorkResult> _parser = new pb::MessageParser<CorkResult>(() => new CorkResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CorkResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[21]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkResult(CorkResult other) : this() {
      keyId_ = other.keyId_;
      result_ = other.result_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkResult Clone() {
      return new CorkResult(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 2;
    private global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result result_ = global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CorkResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CorkResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      if (Result != other.Result) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Result != global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CorkResult other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Result != global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result.Draw) {
        Result = other.Result;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::com.luxza.onlinematchserverclient.schema.CorkResult.Types.Result) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the CorkResult message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Result {
        [pbr::OriginalName("DRAW")] Draw = 0,
        [pbr::OriginalName("WIN")] Win = 1,
        [pbr::OriginalName("LOSE")] Lose = 2,
      }

    }
    #endregion

  }

  public sealed partial class MiddleResult : pb::IMessage<MiddleResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MiddleResult> _parser = new pb::MessageParser<MiddleResult>(() => new MiddleResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MiddleResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[22]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleResult(MiddleResult other) : this() {
      keyId_ = other.keyId_;
      result_ = other.result_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MiddleResult Clone() {
      return new MiddleResult(this);
    }

    /// <summary>Field number for the "key_id" field.</summary>
    public const int KeyIdFieldNumber = 1;
    private string keyId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string KeyId {
      get { return keyId_; }
      set {
        keyId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 2;
    private global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result result_ = global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MiddleResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MiddleResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (KeyId != other.KeyId) return false;
      if (Result != other.Result) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (KeyId.Length != 0) hash ^= KeyId.GetHashCode();
      if (Result != global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (KeyId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure) {
        output.WriteRawTag(16);
        output.WriteEnum((int) Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (KeyId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(KeyId);
      }
      if (Result != global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MiddleResult other) {
      if (other == null) {
        return;
      }
      if (other.KeyId.Length != 0) {
        KeyId = other.KeyId;
      }
      if (other.Result != global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result.Failure) {
        Result = other.Result;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            KeyId = input.ReadString();
            break;
          }
          case 16: {
            Result = (global::com.luxza.onlinematchserverclient.schema.MiddleResult.Types.Result) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the MiddleResult message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Result {
        [pbr::OriginalName("FAILURE")] Failure = 0,
        [pbr::OriginalName("WIN")] Win = 1,
        [pbr::OriginalName("LOSE")] Lose = 2,
      }

    }
    #endregion

  }

  public sealed partial class CorkInfo : pb::IMessage<CorkInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CorkInfo> _parser = new pb::MessageParser<CorkInfo>(() => new CorkInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CorkInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[23]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkInfo(CorkInfo other) : this() {
      switch (other.InfoCase) {
        case InfoOneofCase.RelayKey:
          RelayKey = other.RelayKey.Clone();
          break;
        case InfoOneofCase.Result:
          Result = other.Result.Clone();
          break;
        case InfoOneofCase.RelayWinnerChoice:
          RelayWinnerChoice = other.RelayWinnerChoice;
          break;
        case InfoOneofCase.RelayGameChoice:
          RelayGameChoice = other.RelayGameChoice;
          break;
        case InfoOneofCase.RelayMiddleKey:
          RelayMiddleKey = other.RelayMiddleKey.Clone();
          break;
        case InfoOneofCase.MiddleResult:
          MiddleResult = other.MiddleResult.Clone();
          break;
      }

      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CorkInfo Clone() {
      return new CorkInfo(this);
    }

    /// <summary>Field number for the "relay_key" field.</summary>
    public const int RelayKeyFieldNumber = 1;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayKey RelayKey {
      get { return infoCase_ == InfoOneofCase.RelayKey ? (global::com.luxza.onlinematchserverclient.schema.RelayKey) info_ : null; }
      set {
        info_ = value;
        infoCase_ = value == null ? InfoOneofCase.None : InfoOneofCase.RelayKey;
      }
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 2;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkResult Result {
      get { return infoCase_ == InfoOneofCase.Result ? (global::com.luxza.onlinematchserverclient.schema.CorkResult) info_ : null; }
      set {
        info_ = value;
        infoCase_ = value == null ? InfoOneofCase.None : InfoOneofCase.Result;
      }
    }

    /// <summary>Field number for the "relay_winner_choice" field.</summary>
    public const int RelayWinnerChoiceFieldNumber = 3;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices RelayWinnerChoice {
      get { return infoCase_ == InfoOneofCase.RelayWinnerChoice ? (global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices) info_ : global::com.luxza.onlinematchserverclient.schema.CorkWinnerChoices.First; }
      set {
        info_ = value;
        infoCase_ = InfoOneofCase.RelayWinnerChoice;
      }
    }

    /// <summary>Field number for the "relay_game_choice" field.</summary>
    public const int RelayGameChoiceFieldNumber = 4;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.GameCode RelayGameChoice {
      get { return infoCase_ == InfoOneofCase.RelayGameChoice ? (global::com.luxza.onlinematchserverclient.schema.GameCode) info_ : global::com.luxza.onlinematchserverclient.schema.GameCode.Indefinite; }
      set {
        info_ = value;
        infoCase_ = InfoOneofCase.RelayGameChoice;
      }
    }

    /// <summary>Field number for the "relay_middle_key" field.</summary>
    public const int RelayMiddleKeyFieldNumber = 5;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey RelayMiddleKey {
      get { return infoCase_ == InfoOneofCase.RelayMiddleKey ? (global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey) info_ : null; }
      set {
        info_ = value;
        infoCase_ = value == null ? InfoOneofCase.None : InfoOneofCase.RelayMiddleKey;
      }
    }

    /// <summary>Field number for the "middle_result" field.</summary>
    public const int MiddleResultFieldNumber = 6;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MiddleResult MiddleResult {
      get { return infoCase_ == InfoOneofCase.MiddleResult ? (global::com.luxza.onlinematchserverclient.schema.MiddleResult) info_ : null; }
      set {
        info_ = value;
        infoCase_ = value == null ? InfoOneofCase.None : InfoOneofCase.MiddleResult;
      }
    }

    private object info_;
    /// <summary>Enum of possible cases for the "info" oneof.</summary>
    public enum InfoOneofCase {
      None = 0,
      RelayKey = 1,
      Result = 2,
      RelayWinnerChoice = 3,
      RelayGameChoice = 4,
      RelayMiddleKey = 5,
      MiddleResult = 6,
    }
    private InfoOneofCase infoCase_ = InfoOneofCase.None;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public InfoOneofCase InfoCase {
      get { return infoCase_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void ClearInfo() {
      infoCase_ = InfoOneofCase.None;
      info_ = null;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CorkInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CorkInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(RelayKey, other.RelayKey)) return false;
      if (!object.Equals(Result, other.Result)) return false;
      if (RelayWinnerChoice != other.RelayWinnerChoice) return false;
      if (RelayGameChoice != other.RelayGameChoice) return false;
      if (!object.Equals(RelayMiddleKey, other.RelayMiddleKey)) return false;
      if (!object.Equals(MiddleResult, other.MiddleResult)) return false;
      if (InfoCase != other.InfoCase) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (infoCase_ == InfoOneofCase.RelayKey) hash ^= RelayKey.GetHashCode();
      if (infoCase_ == InfoOneofCase.Result) hash ^= Result.GetHashCode();
      if (infoCase_ == InfoOneofCase.RelayWinnerChoice) hash ^= RelayWinnerChoice.GetHashCode();
      if (infoCase_ == InfoOneofCase.RelayGameChoice) hash ^= RelayGameChoice.GetHashCode();
      if (infoCase_ == InfoOneofCase.RelayMiddleKey) hash ^= RelayMiddleKey.GetHashCode();
      if (infoCase_ == InfoOneofCase.MiddleResult) hash ^= MiddleResult.GetHashCode();
      hash ^= (int) infoCase_;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (infoCase_ == InfoOneofCase.RelayKey) {
        output.WriteRawTag(10);
        output.WriteMessage(RelayKey);
      }
      if (infoCase_ == InfoOneofCase.Result) {
        output.WriteRawTag(18);
        output.WriteMessage(Result);
      }
      if (infoCase_ == InfoOneofCase.RelayWinnerChoice) {
        output.WriteRawTag(24);
        output.WriteEnum((int) RelayWinnerChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayGameChoice) {
        output.WriteRawTag(32);
        output.WriteEnum((int) RelayGameChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayMiddleKey) {
        output.WriteRawTag(42);
        output.WriteMessage(RelayMiddleKey);
      }
      if (infoCase_ == InfoOneofCase.MiddleResult) {
        output.WriteRawTag(50);
        output.WriteMessage(MiddleResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (infoCase_ == InfoOneofCase.RelayKey) {
        output.WriteRawTag(10);
        output.WriteMessage(RelayKey);
      }
      if (infoCase_ == InfoOneofCase.Result) {
        output.WriteRawTag(18);
        output.WriteMessage(Result);
      }
      if (infoCase_ == InfoOneofCase.RelayWinnerChoice) {
        output.WriteRawTag(24);
        output.WriteEnum((int) RelayWinnerChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayGameChoice) {
        output.WriteRawTag(32);
        output.WriteEnum((int) RelayGameChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayMiddleKey) {
        output.WriteRawTag(42);
        output.WriteMessage(RelayMiddleKey);
      }
      if (infoCase_ == InfoOneofCase.MiddleResult) {
        output.WriteRawTag(50);
        output.WriteMessage(MiddleResult);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (infoCase_ == InfoOneofCase.RelayKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RelayKey);
      }
      if (infoCase_ == InfoOneofCase.Result) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (infoCase_ == InfoOneofCase.RelayWinnerChoice) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) RelayWinnerChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayGameChoice) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) RelayGameChoice);
      }
      if (infoCase_ == InfoOneofCase.RelayMiddleKey) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(RelayMiddleKey);
      }
      if (infoCase_ == InfoOneofCase.MiddleResult) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(MiddleResult);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CorkInfo other) {
      if (other == null) {
        return;
      }
      switch (other.InfoCase) {
        case InfoOneofCase.RelayKey:
          if (RelayKey == null) {
            RelayKey = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
          }
          RelayKey.MergeFrom(other.RelayKey);
          break;
        case InfoOneofCase.Result:
          if (Result == null) {
            Result = new global::com.luxza.onlinematchserverclient.schema.CorkResult();
          }
          Result.MergeFrom(other.Result);
          break;
        case InfoOneofCase.RelayWinnerChoice:
          RelayWinnerChoice = other.RelayWinnerChoice;
          break;
        case InfoOneofCase.RelayGameChoice:
          RelayGameChoice = other.RelayGameChoice;
          break;
        case InfoOneofCase.RelayMiddleKey:
          if (RelayMiddleKey == null) {
            RelayMiddleKey = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
          }
          RelayMiddleKey.MergeFrom(other.RelayMiddleKey);
          break;
        case InfoOneofCase.MiddleResult:
          if (MiddleResult == null) {
            MiddleResult = new global::com.luxza.onlinematchserverclient.schema.MiddleResult();
          }
          MiddleResult.MergeFrom(other.MiddleResult);
          break;
      }

      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.RelayKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
            if (infoCase_ == InfoOneofCase.RelayKey) {
              subBuilder.MergeFrom(RelayKey);
            }
            input.ReadMessage(subBuilder);
            RelayKey = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.CorkResult subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkResult();
            if (infoCase_ == InfoOneofCase.Result) {
              subBuilder.MergeFrom(Result);
            }
            input.ReadMessage(subBuilder);
            Result = subBuilder;
            break;
          }
          case 24: {
            info_ = input.ReadEnum();
            infoCase_ = InfoOneofCase.RelayWinnerChoice;
            break;
          }
          case 32: {
            info_ = input.ReadEnum();
            infoCase_ = InfoOneofCase.RelayGameChoice;
            break;
          }
          case 42: {
            global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
            if (infoCase_ == InfoOneofCase.RelayMiddleKey) {
              subBuilder.MergeFrom(RelayMiddleKey);
            }
            input.ReadMessage(subBuilder);
            RelayMiddleKey = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.MiddleResult subBuilder = new global::com.luxza.onlinematchserverclient.schema.MiddleResult();
            if (infoCase_ == InfoOneofCase.MiddleResult) {
              subBuilder.MergeFrom(MiddleResult);
            }
            input.ReadMessage(subBuilder);
            MiddleResult = subBuilder;
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            global::com.luxza.onlinematchserverclient.schema.RelayKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayKey();
            if (infoCase_ == InfoOneofCase.RelayKey) {
              subBuilder.MergeFrom(RelayKey);
            }
            input.ReadMessage(subBuilder);
            RelayKey = subBuilder;
            break;
          }
          case 18: {
            global::com.luxza.onlinematchserverclient.schema.CorkResult subBuilder = new global::com.luxza.onlinematchserverclient.schema.CorkResult();
            if (infoCase_ == InfoOneofCase.Result) {
              subBuilder.MergeFrom(Result);
            }
            input.ReadMessage(subBuilder);
            Result = subBuilder;
            break;
          }
          case 24: {
            info_ = input.ReadEnum();
            infoCase_ = InfoOneofCase.RelayWinnerChoice;
            break;
          }
          case 32: {
            info_ = input.ReadEnum();
            infoCase_ = InfoOneofCase.RelayGameChoice;
            break;
          }
          case 42: {
            global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey subBuilder = new global::com.luxza.onlinematchserverclient.schema.RelayMiddleKey();
            if (infoCase_ == InfoOneofCase.RelayMiddleKey) {
              subBuilder.MergeFrom(RelayMiddleKey);
            }
            input.ReadMessage(subBuilder);
            RelayMiddleKey = subBuilder;
            break;
          }
          case 50: {
            global::com.luxza.onlinematchserverclient.schema.MiddleResult subBuilder = new global::com.luxza.onlinematchserverclient.schema.MiddleResult();
            if (infoCase_ == InfoOneofCase.MiddleResult) {
              subBuilder.MergeFrom(MiddleResult);
            }
            input.ReadMessage(subBuilder);
            MiddleResult = subBuilder;
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class RelayEvent : pb::IMessage<RelayEvent>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<RelayEvent> _parser = new pb::MessageParser<RelayEvent>(() => new RelayEvent());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<RelayEvent> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[24]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayEvent() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayEvent(RelayEvent other) : this() {
      granId_ = other.granId_;
      event_ = other.event_ != null ? other.event_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public RelayEvent Clone() {
      return new RelayEvent(this);
    }

    /// <summary>Field number for the "gran_id" field.</summary>
    public const int GranIdFieldNumber = 1;
    private string granId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string GranId {
      get { return granId_; }
      set {
        granId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    /// <summary>Field number for the "event" field.</summary>
    public const int EventFieldNumber = 2;
    private global::com.luxza.onlinematchserverclient.schema.Event event_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.Event Event {
      get { return event_; }
      set {
        event_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as RelayEvent);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(RelayEvent other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (GranId != other.GranId) return false;
      if (!object.Equals(Event, other.Event)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (GranId.Length != 0) hash ^= GranId.GetHashCode();
      if (event_ != null) hash ^= Event.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (event_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Event);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (GranId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(GranId);
      }
      if (event_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Event);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (GranId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(GranId);
      }
      if (event_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Event);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(RelayEvent other) {
      if (other == null) {
        return;
      }
      if (other.GranId.Length != 0) {
        GranId = other.GranId;
      }
      if (other.event_ != null) {
        if (event_ == null) {
          Event = new global::com.luxza.onlinematchserverclient.schema.Event();
        }
        Event.MergeFrom(other.Event);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            if (event_ == null) {
              Event = new global::com.luxza.onlinematchserverclient.schema.Event();
            }
            input.ReadMessage(Event);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            GranId = input.ReadString();
            break;
          }
          case 18: {
            if (event_ == null) {
              Event = new global::com.luxza.onlinematchserverclient.schema.Event();
            }
            input.ReadMessage(Event);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class CreateMatchRequest : pb::IMessage<CreateMatchRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CreateMatchRequest> _parser = new pb::MessageParser<CreateMatchRequest>(() => new CreateMatchRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CreateMatchRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[25]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchRequest(CreateMatchRequest other) : this() {
      granIds_ = other.granIds_.Clone();
      codes_ = other.codes_.Clone();
      cork_ = other.cork_;
      joinTimeout_ = other.joinTimeout_;
      initTimeout_ = other.initTimeout_;
      playerTimeout_ = other.playerTimeout_;
      afterFirstLegThrowOrder_ = other.afterFirstLegThrowOrder_;
      tieBreakThrowOrder_ = other.tieBreakThrowOrder_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchRequest Clone() {
      return new CreateMatchRequest(this);
    }

    /// <summary>Field number for the "gran_ids" field.</summary>
    public const int GranIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_granIds_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> granIds_ = new pbc::RepeatedField<string>();
    /// <summary>
    /// Must be specified by throw order
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> GranIds {
      get { return granIds_; }
    }

    /// <summary>Field number for the "codes" field.</summary>
    public const int CodesFieldNumber = 2;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.GameCode> _repeated_codes_codec
        = pb::FieldCodec.ForEnum(18, x => (int) x, x => (global::com.luxza.onlinematchserverclient.schema.GameCode) x);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.GameCode> codes_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.GameCode>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.GameCode> Codes {
      get { return codes_; }
    }

    /// <summary>Field number for the "cork" field.</summary>
    public const int CorkFieldNumber = 3;
    private bool cork_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Cork {
      get { return cork_; }
      set {
        cork_ = value;
      }
    }

    /// <summary>Field number for the "join_timeout" field.</summary>
    public const int JoinTimeoutFieldNumber = 4;
    private int joinTimeout_;
    /// <summary>
    ///*
    /// specify by seconds
    /// use default value (or env ONLINE_MATCH_JOIN_TIMEOUT) if 0 specified or not specified
    /// disable join timeout if -1 (or other negative values) specified
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int JoinTimeout {
      get { return joinTimeout_; }
      set {
        joinTimeout_ = value;
      }
    }

    /// <summary>Field number for the "init_timeout" field.</summary>
    public const int InitTimeoutFieldNumber = 5;
    private int initTimeout_;
    /// <summary>
    ///*
    /// specify by seconds
    /// use default value (or env ONLINE_MATCH_INIT_TIMEOUT) if 0 specified or not specified
    /// disable init timeout if -1 (or other negative values) specified
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int InitTimeout {
      get { return initTimeout_; }
      set {
        initTimeout_ = value;
      }
    }

    /// <summary>Field number for the "player_timeout" field.</summary>
    public const int PlayerTimeoutFieldNumber = 6;
    private int playerTimeout_;
    /// <summary>
    ///*
    /// specify by seconds
    /// use default value (or env ONLINE_MATCH_PLAYER_TIMEOUT) if 0 specified or not specified
    /// cannot disable player timeout if -1 (or other negative values) specified
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int PlayerTimeout {
      get { return playerTimeout_; }
      set {
        playerTimeout_ = value;
      }
    }

    /// <summary>Field number for the "after_first_leg_throw_order" field.</summary>
    public const int AfterFirstLegThrowOrderFieldNumber = 7;
    private global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings afterFirstLegThrowOrder_ = global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings AfterFirstLegThrowOrder {
      get { return afterFirstLegThrowOrder_; }
      set {
        afterFirstLegThrowOrder_ = value;
      }
    }

    /// <summary>Field number for the "tie_break_throw_order" field.</summary>
    public const int TieBreakThrowOrderFieldNumber = 8;
    private global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings tieBreakThrowOrder_ = global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings TieBreakThrowOrder {
      get { return tieBreakThrowOrder_; }
      set {
        tieBreakThrowOrder_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CreateMatchRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CreateMatchRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!granIds_.Equals(other.granIds_)) return false;
      if(!codes_.Equals(other.codes_)) return false;
      if (Cork != other.Cork) return false;
      if (JoinTimeout != other.JoinTimeout) return false;
      if (InitTimeout != other.InitTimeout) return false;
      if (PlayerTimeout != other.PlayerTimeout) return false;
      if (AfterFirstLegThrowOrder != other.AfterFirstLegThrowOrder) return false;
      if (TieBreakThrowOrder != other.TieBreakThrowOrder) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= granIds_.GetHashCode();
      hash ^= codes_.GetHashCode();
      if (Cork != false) hash ^= Cork.GetHashCode();
      if (JoinTimeout != 0) hash ^= JoinTimeout.GetHashCode();
      if (InitTimeout != 0) hash ^= InitTimeout.GetHashCode();
      if (PlayerTimeout != 0) hash ^= PlayerTimeout.GetHashCode();
      if (AfterFirstLegThrowOrder != global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder) hash ^= AfterFirstLegThrowOrder.GetHashCode();
      if (TieBreakThrowOrder != global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder) hash ^= TieBreakThrowOrder.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      granIds_.WriteTo(output, _repeated_granIds_codec);
      codes_.WriteTo(output, _repeated_codes_codec);
      if (Cork != false) {
        output.WriteRawTag(24);
        output.WriteBool(Cork);
      }
      if (JoinTimeout != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(JoinTimeout);
      }
      if (InitTimeout != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(InitTimeout);
      }
      if (PlayerTimeout != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PlayerTimeout);
      }
      if (AfterFirstLegThrowOrder != global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder) {
        output.WriteRawTag(56);
        output.WriteEnum((int) AfterFirstLegThrowOrder);
      }
      if (TieBreakThrowOrder != global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder) {
        output.WriteRawTag(64);
        output.WriteEnum((int) TieBreakThrowOrder);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      granIds_.WriteTo(ref output, _repeated_granIds_codec);
      codes_.WriteTo(ref output, _repeated_codes_codec);
      if (Cork != false) {
        output.WriteRawTag(24);
        output.WriteBool(Cork);
      }
      if (JoinTimeout != 0) {
        output.WriteRawTag(32);
        output.WriteInt32(JoinTimeout);
      }
      if (InitTimeout != 0) {
        output.WriteRawTag(40);
        output.WriteInt32(InitTimeout);
      }
      if (PlayerTimeout != 0) {
        output.WriteRawTag(48);
        output.WriteInt32(PlayerTimeout);
      }
      if (AfterFirstLegThrowOrder != global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder) {
        output.WriteRawTag(56);
        output.WriteEnum((int) AfterFirstLegThrowOrder);
      }
      if (TieBreakThrowOrder != global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder) {
        output.WriteRawTag(64);
        output.WriteEnum((int) TieBreakThrowOrder);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += granIds_.CalculateSize(_repeated_granIds_codec);
      size += codes_.CalculateSize(_repeated_codes_codec);
      if (Cork != false) {
        size += 1 + 1;
      }
      if (JoinTimeout != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(JoinTimeout);
      }
      if (InitTimeout != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(InitTimeout);
      }
      if (PlayerTimeout != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(PlayerTimeout);
      }
      if (AfterFirstLegThrowOrder != global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) AfterFirstLegThrowOrder);
      }
      if (TieBreakThrowOrder != global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) TieBreakThrowOrder);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CreateMatchRequest other) {
      if (other == null) {
        return;
      }
      granIds_.Add(other.granIds_);
      codes_.Add(other.codes_);
      if (other.Cork != false) {
        Cork = other.Cork;
      }
      if (other.JoinTimeout != 0) {
        JoinTimeout = other.JoinTimeout;
      }
      if (other.InitTimeout != 0) {
        InitTimeout = other.InitTimeout;
      }
      if (other.PlayerTimeout != 0) {
        PlayerTimeout = other.PlayerTimeout;
      }
      if (other.AfterFirstLegThrowOrder != global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings.NoneAfterFirstLegThrowOrder) {
        AfterFirstLegThrowOrder = other.AfterFirstLegThrowOrder;
      }
      if (other.TieBreakThrowOrder != global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings.NoneTieBreakThrowOrder) {
        TieBreakThrowOrder = other.TieBreakThrowOrder;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            granIds_.AddEntriesFrom(input, _repeated_granIds_codec);
            break;
          }
          case 18:
          case 16: {
            codes_.AddEntriesFrom(input, _repeated_codes_codec);
            break;
          }
          case 24: {
            Cork = input.ReadBool();
            break;
          }
          case 32: {
            JoinTimeout = input.ReadInt32();
            break;
          }
          case 40: {
            InitTimeout = input.ReadInt32();
            break;
          }
          case 48: {
            PlayerTimeout = input.ReadInt32();
            break;
          }
          case 56: {
            AfterFirstLegThrowOrder = (global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings) input.ReadEnum();
            break;
          }
          case 64: {
            TieBreakThrowOrder = (global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            granIds_.AddEntriesFrom(ref input, _repeated_granIds_codec);
            break;
          }
          case 18:
          case 16: {
            codes_.AddEntriesFrom(ref input, _repeated_codes_codec);
            break;
          }
          case 24: {
            Cork = input.ReadBool();
            break;
          }
          case 32: {
            JoinTimeout = input.ReadInt32();
            break;
          }
          case 40: {
            InitTimeout = input.ReadInt32();
            break;
          }
          case 48: {
            PlayerTimeout = input.ReadInt32();
            break;
          }
          case 56: {
            AfterFirstLegThrowOrder = (global::com.luxza.onlinematchserverclient.schema.AfterFirstLegThrowOrderSettings) input.ReadEnum();
            break;
          }
          case 64: {
            TieBreakThrowOrder = (global::com.luxza.onlinematchserverclient.schema.TieBreakThrowOrderSettings) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class CreateMatchResponse : pb::IMessage<CreateMatchResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<CreateMatchResponse> _parser = new pb::MessageParser<CreateMatchResponse>(() => new CreateMatchResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<CreateMatchResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[26]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchResponse(CreateMatchResponse other) : this() {
      matchId_ = other.matchId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public CreateMatchResponse Clone() {
      return new CreateMatchResponse(this);
    }

    /// <summary>Field number for the "match_id" field.</summary>
    public const int MatchIdFieldNumber = 1;
    private string matchId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string MatchId {
      get { return matchId_; }
      set {
        matchId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as CreateMatchResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(CreateMatchResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MatchId != other.MatchId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MatchId.Length != 0) hash ^= MatchId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MatchId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(MatchId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(CreateMatchResponse other) {
      if (other == null) {
        return;
      }
      if (other.MatchId.Length != 0) {
        MatchId = other.MatchId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class GetMatchRequest : pb::IMessage<GetMatchRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetMatchRequest> _parser = new pb::MessageParser<GetMatchRequest>(() => new GetMatchRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetMatchRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[27]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchRequest(GetMatchRequest other) : this() {
      matchId_ = other.matchId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchRequest Clone() {
      return new GetMatchRequest(this);
    }

    /// <summary>Field number for the "match_id" field.</summary>
    public const int MatchIdFieldNumber = 1;
    private string matchId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string MatchId {
      get { return matchId_; }
      set {
        matchId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetMatchRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetMatchRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MatchId != other.MatchId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MatchId.Length != 0) hash ^= MatchId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MatchId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(MatchId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetMatchRequest other) {
      if (other == null) {
        return;
      }
      if (other.MatchId.Length != 0) {
        MatchId = other.MatchId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class GetMatchResponse : pb::IMessage<GetMatchResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetMatchResponse> _parser = new pb::MessageParser<GetMatchResponse>(() => new GetMatchResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetMatchResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[28]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchResponse(GetMatchResponse other) : this() {
      info_ = other.info_ != null ? other.info_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchResponse Clone() {
      return new GetMatchResponse(this);
    }

    /// <summary>Field number for the "info" field.</summary>
    public const int InfoFieldNumber = 1;
    private global::com.luxza.onlinematchserverclient.schema.MatchInfo info_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchInfo Info {
      get { return info_; }
      set {
        info_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetMatchResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetMatchResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Info, other.Info)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (info_ != null) hash ^= Info.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (info_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Info);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (info_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Info);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetMatchResponse other) {
      if (other == null) {
        return;
      }
      if (other.info_ != null) {
        if (info_ == null) {
          Info = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
        }
        Info.MergeFrom(other.Info);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (info_ == null) {
              Info = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            }
            input.ReadMessage(Info);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (info_ == null) {
              Info = new global::com.luxza.onlinematchserverclient.schema.MatchInfo();
            }
            input.ReadMessage(Info);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class MatchSession : pb::IMessage<MatchSession>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<MatchSession> _parser = new pb::MessageParser<MatchSession>(() => new MatchSession());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<MatchSession> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[29]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchSession() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchSession(MatchSession other) : this() {
      id_ = other.id_;
      granIds_ = other.granIds_.Clone();
      status_ = other.status_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public MatchSession Clone() {
      return new MatchSession(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private int id_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "gran_ids" field.</summary>
    public const int GranIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<string> _repeated_granIds_codec
        = pb::FieldCodec.ForString(18);
    private readonly pbc::RepeatedField<string> granIds_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> GranIds {
      get { return granIds_; }
    }

    /// <summary>Field number for the "status" field.</summary>
    public const int StatusFieldNumber = 3;
    private global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status status_ = global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status Status {
      get { return status_; }
      set {
        status_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as MatchSession);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(MatchSession other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if(!granIds_.Equals(other.granIds_)) return false;
      if (Status != other.Status) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      hash ^= granIds_.GetHashCode();
      if (Status != global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown) hash ^= Status.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      granIds_.WriteTo(output, _repeated_granIds_codec);
      if (Status != global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteInt32(Id);
      }
      granIds_.WriteTo(ref output, _repeated_granIds_codec);
      if (Status != global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown) {
        output.WriteRawTag(24);
        output.WriteEnum((int) Status);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeInt32Size(Id);
      }
      size += granIds_.CalculateSize(_repeated_granIds_codec);
      if (Status != global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) Status);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(MatchSession other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      granIds_.Add(other.granIds_);
      if (other.Status != global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status.Unknown) {
        Status = other.Status;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            granIds_.AddEntriesFrom(input, _repeated_granIds_codec);
            break;
          }
          case 24: {
            Status = (global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadInt32();
            break;
          }
          case 18: {
            granIds_.AddEntriesFrom(ref input, _repeated_granIds_codec);
            break;
          }
          case 24: {
            Status = (global::com.luxza.onlinematchserverclient.schema.MatchSession.Types.Status) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

    #region Nested types
    /// <summary>Container for nested types declared in the MatchSession message type.</summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static partial class Types {
      public enum Status {
        [pbr::OriginalName("UNKNOWN")] Unknown = 0,
        [pbr::OriginalName("DISCONNECTED")] Disconnected = 1,
        [pbr::OriginalName("INIT")] Init = 2,
        [pbr::OriginalName("CONNECTED")] Connected = 3,
        [pbr::OriginalName("TIMEOUT")] Timeout = 4,
      }

    }
    #endregion

  }

  public sealed partial class GetMatchSessionsRequest : pb::IMessage<GetMatchSessionsRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetMatchSessionsRequest> _parser = new pb::MessageParser<GetMatchSessionsRequest>(() => new GetMatchSessionsRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetMatchSessionsRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[30]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsRequest(GetMatchSessionsRequest other) : this() {
      matchId_ = other.matchId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsRequest Clone() {
      return new GetMatchSessionsRequest(this);
    }

    /// <summary>Field number for the "match_id" field.</summary>
    public const int MatchIdFieldNumber = 1;
    private string matchId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string MatchId {
      get { return matchId_; }
      set {
        matchId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetMatchSessionsRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetMatchSessionsRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MatchId != other.MatchId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MatchId.Length != 0) hash ^= MatchId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MatchId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(MatchId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetMatchSessionsRequest other) {
      if (other == null) {
        return;
      }
      if (other.MatchId.Length != 0) {
        MatchId = other.MatchId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class GetMatchSessionsResponse : pb::IMessage<GetMatchSessionsResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<GetMatchSessionsResponse> _parser = new pb::MessageParser<GetMatchSessionsResponse>(() => new GetMatchSessionsResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<GetMatchSessionsResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[31]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsResponse(GetMatchSessionsResponse other) : this() {
      sessions_ = other.sessions_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public GetMatchSessionsResponse Clone() {
      return new GetMatchSessionsResponse(this);
    }

    /// <summary>Field number for the "sessions" field.</summary>
    public const int SessionsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::com.luxza.onlinematchserverclient.schema.MatchSession> _repeated_sessions_codec
        = pb::FieldCodec.ForMessage(10, global::com.luxza.onlinematchserverclient.schema.MatchSession.Parser);
    private readonly pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.MatchSession> sessions_ = new pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.MatchSession>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::com.luxza.onlinematchserverclient.schema.MatchSession> Sessions {
      get { return sessions_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as GetMatchSessionsResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(GetMatchSessionsResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!sessions_.Equals(other.sessions_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= sessions_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      sessions_.WriteTo(output, _repeated_sessions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      sessions_.WriteTo(ref output, _repeated_sessions_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += sessions_.CalculateSize(_repeated_sessions_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(GetMatchSessionsResponse other) {
      if (other == null) {
        return;
      }
      sessions_.Add(other.sessions_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            sessions_.AddEntriesFrom(input, _repeated_sessions_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            sessions_.AddEntriesFrom(ref input, _repeated_sessions_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class ListMatchesRequest : pb::IMessage<ListMatchesRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ListMatchesRequest> _parser = new pb::MessageParser<ListMatchesRequest>(() => new ListMatchesRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ListMatchesRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[32]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesRequest(ListMatchesRequest other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesRequest Clone() {
      return new ListMatchesRequest(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ListMatchesRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ListMatchesRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ListMatchesRequest other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  public sealed partial class ListMatchesResponse : pb::IMessage<ListMatchesResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<ListMatchesResponse> _parser = new pb::MessageParser<ListMatchesResponse>(() => new ListMatchesResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<ListMatchesResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[33]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesResponse(ListMatchesResponse other) : this() {
      matchIds_ = other.matchIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ListMatchesResponse Clone() {
      return new ListMatchesResponse(this);
    }

    /// <summary>Field number for the "match_ids" field.</summary>
    public const int MatchIdsFieldNumber = 1;
    private static readonly pb::FieldCodec<string> _repeated_matchIds_codec
        = pb::FieldCodec.ForString(10);
    private readonly pbc::RepeatedField<string> matchIds_ = new pbc::RepeatedField<string>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<string> MatchIds {
      get { return matchIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as ListMatchesResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(ListMatchesResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!matchIds_.Equals(other.matchIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= matchIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      matchIds_.WriteTo(output, _repeated_matchIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      matchIds_.WriteTo(ref output, _repeated_matchIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += matchIds_.CalculateSize(_repeated_matchIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(ListMatchesResponse other) {
      if (other == null) {
        return;
      }
      matchIds_.Add(other.matchIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            matchIds_.AddEntriesFrom(input, _repeated_matchIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            matchIds_.AddEntriesFrom(ref input, _repeated_matchIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class DeleteMatchRequest : pb::IMessage<DeleteMatchRequest>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DeleteMatchRequest> _parser = new pb::MessageParser<DeleteMatchRequest>(() => new DeleteMatchRequest());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DeleteMatchRequest> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[34]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchRequest() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchRequest(DeleteMatchRequest other) : this() {
      matchId_ = other.matchId_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchRequest Clone() {
      return new DeleteMatchRequest(this);
    }

    /// <summary>Field number for the "match_id" field.</summary>
    public const int MatchIdFieldNumber = 1;
    private string matchId_ = "";
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public string MatchId {
      get { return matchId_; }
      set {
        matchId_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DeleteMatchRequest);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DeleteMatchRequest other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (MatchId != other.MatchId) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (MatchId.Length != 0) hash ^= MatchId.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (MatchId.Length != 0) {
        output.WriteRawTag(10);
        output.WriteString(MatchId);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (MatchId.Length != 0) {
        size += 1 + pb::CodedOutputStream.ComputeStringSize(MatchId);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DeleteMatchRequest other) {
      if (other == null) {
        return;
      }
      if (other.MatchId.Length != 0) {
        MatchId = other.MatchId;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            MatchId = input.ReadString();
            break;
          }
        }
      }
    }
    #endif

  }

  public sealed partial class DeleteMatchResponse : pb::IMessage<DeleteMatchResponse>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DeleteMatchResponse> _parser = new pb::MessageParser<DeleteMatchResponse>(() => new DeleteMatchResponse());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DeleteMatchResponse> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[35]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchResponse() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchResponse(DeleteMatchResponse other) : this() {
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DeleteMatchResponse Clone() {
      return new DeleteMatchResponse(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DeleteMatchResponse);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DeleteMatchResponse other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DeleteMatchResponse other) {
      if (other == null) {
        return;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
        }
      }
    }
    #endif

  }

  public sealed partial class DisqualificationInfo : pb::IMessage<DisqualificationInfo>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<DisqualificationInfo> _parser = new pb::MessageParser<DisqualificationInfo>(() => new DisqualificationInfo());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<DisqualificationInfo> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::com.luxza.onlinematchserverclient.schema.OnlineMatchReflection.Descriptor.MessageTypes[36]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DisqualificationInfo() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DisqualificationInfo(DisqualificationInfo other) : this() {
      winnerUnitId_ = other.winnerUnitId_;
      loserUnitIds_ = other.loserUnitIds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public DisqualificationInfo Clone() {
      return new DisqualificationInfo(this);
    }

    /// <summary>Field number for the "winner_unit_id" field.</summary>
    public const int WinnerUnitIdFieldNumber = 1;
    private ulong winnerUnitId_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong WinnerUnitId {
      get { return winnerUnitId_; }
      set {
        winnerUnitId_ = value;
      }
    }

    /// <summary>Field number for the "loser_unit_ids" field.</summary>
    public const int LoserUnitIdsFieldNumber = 2;
    private static readonly pb::FieldCodec<ulong> _repeated_loserUnitIds_codec
        = pb::FieldCodec.ForUInt64(18);
    private readonly pbc::RepeatedField<ulong> loserUnitIds_ = new pbc::RepeatedField<ulong>();
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<ulong> LoserUnitIds {
      get { return loserUnitIds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as DisqualificationInfo);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(DisqualificationInfo other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (WinnerUnitId != other.WinnerUnitId) return false;
      if(!loserUnitIds_.Equals(other.loserUnitIds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (WinnerUnitId != 0UL) hash ^= WinnerUnitId.GetHashCode();
      hash ^= loserUnitIds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (WinnerUnitId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(WinnerUnitId);
      }
      loserUnitIds_.WriteTo(output, _repeated_loserUnitIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (WinnerUnitId != 0UL) {
        output.WriteRawTag(8);
        output.WriteUInt64(WinnerUnitId);
      }
      loserUnitIds_.WriteTo(ref output, _repeated_loserUnitIds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (WinnerUnitId != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(WinnerUnitId);
      }
      size += loserUnitIds_.CalculateSize(_repeated_loserUnitIds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(DisqualificationInfo other) {
      if (other == null) {
        return;
      }
      if (other.WinnerUnitId != 0UL) {
        WinnerUnitId = other.WinnerUnitId;
      }
      loserUnitIds_.Add(other.loserUnitIds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            WinnerUnitId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            loserUnitIds_.AddEntriesFrom(input, _repeated_loserUnitIds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
        switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            WinnerUnitId = input.ReadUInt64();
            break;
          }
          case 18:
          case 16: {
            loserUnitIds_.AddEntriesFrom(ref input, _repeated_loserUnitIds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
