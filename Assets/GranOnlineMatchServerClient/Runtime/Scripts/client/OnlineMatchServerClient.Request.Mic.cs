using com.luxza.onlinematchserverclient.interfaces;

namespace com.luxza.onlinematchserverclient.client
{
    public partial class OnlineMatchServerClient
    {
        /// <summary>
        /// MicをMuteにしたことを通知します。
        /// </summary>
        public void SendMicMute()
        {
            _queue.Enqueue(CustomEvent.MicMute.CreateClientMsg());
        }

        /// <summary>
        /// MicをOnにしたことを通知します。
        /// </summary>
        public void SendMicOn()
        {
            _queue.Enqueue(CustomEvent.MicOn.CreateClientMsg());
        }
    }
}