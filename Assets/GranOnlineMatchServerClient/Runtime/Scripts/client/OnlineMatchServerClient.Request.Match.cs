using System;
using System.Linq;
using System.Numerics;
using System.Threading;
using com.luxza.granlog;
using com.luxza.onlinematchserverclient.interfaces;
using com.luxza.onlinematchserverclient.schema;
using com.luxza.onlinematchserverclient.util;
using Cysharp.Threading.Tasks;
using GameCode = com.luxza.onlinematchserverclient.interfaces.GameCode;

namespace com.luxza.onlinematchserverclient.client
{
    public partial class OnlineMatchServerClient
    {
        /// <summary>
        /// Matchを作成します。
        /// これはテスト用に用意されたAPIで、GranBoardアプリから明示的に呼ぶことはありません。
        /// Matchはサーバ側で作成されます。
        ///
        /// テストでMatchを作成したい時にのみ使用してください。
        /// </summary>
        /// <param name="participantGranIds"></param>
        /// <param name="codes"></param>
        /// <param name="needCork"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async UniTask<string> CreateMatchAsync(string[] participantGranIds, GameCode[] codes, bool needCork,
            CancellationToken cancellationToken)
        {
            var req = new CreateMatchRequest()
            {
                GranIds = { participantGranIds },
                Codes = { codes.Select(c => c.ToGRPCTypeGameCode()) },
                Cork = needCork
            };

            var cts = CancellationTokenSource.CreateLinkedTokenSource(_cts.Token, cancellationToken);
            try
            {
                var res = await _client.CreateMatchAsync(req, cancellationToken: cts.Token);
                return res.MatchId;
            }
            catch (OperationCanceledException ex)
            {
                Log.e($"Cancel {nameof(CreateMatchAsync)}");
                throw;
            }
        }

        /// <summary>
        /// 試合から退出したことを通知します。
        /// </summary>
        public void SendExitMatch()
        {
            _queue.Enqueue(CustomEvent.ExitMatch.CreateClientMsg());
        }

        /// <summary>
        ///　指定したUnitが次のLEGに行くことを通知します。
        ///  試合の参加者全員を指定する必要があり、各UnitごとのGranIdを指定する必要があります。
        /// </summary>
        /// <param name="granIdsPerOnlineUnits">各UnitのメンバのGranId</param>
        public void SendToNextLeg(string[][] granIdsPerOnlineUnits)
        {
            var msg = new ClientMsg()
            {
                NextLeg = new Units()
                {
                    Units_ =
                    {
                        granIdsPerOnlineUnits.Select(g => {
                            var u = new Unit();
                            u.GranIds.AddRange(g);
                            return u;
                        })
                    }
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// 相手にChangeを送信します。
        /// </summary>
        /// <param name="roundNo"></param>
        public void SendChangeKey(int roundNo)
        {
            var msg = new ClientMsg()
            {
                SendKey = new Key()
                {
                    KeyId = $"{roundNo}:4",
                    Value = KeyValue.Change
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// Gameが終了したことをサーバに通知します。
        /// </summary>
        /// <param name="roundNo"></param>
        /// <param name="throwNo"></param>
        public void SendGameEnd(int roundNo, int throwNo)
        {
            var msg = new ClientMsg()
            {
                MatchEnd = new LastKey()
                {
                    KeyId = $"{roundNo}:{throwNo}"
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// HitしたSegmentを送信します。
        /// </summary>
        /// <param name="roundNo"></param>
        /// <param name="throwNo"></param>
        /// <param name="segment"></param>
        public void SendKey(int roundNo, int throwNo, Segment segment, Vector2 hitPosition = default)
        {
            var msg = new ClientMsg()
            {
                SendKey = new Key()
                {
                    KeyId = $"{roundNo}:{throwNo}",
                    Value = segment.TogRPCSegment(),
                    HitPosition = new HitPosition()
                    {
                        X = hitPosition.X,
                        Y = hitPosition.Y
                    }
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// Visit入力の時、Scoreを送信します。
        /// </summary>
        /// <param name="roundNo"></param>
        /// <param name="score"></param>
        /// <param name="checkOut">Checkoutできた場合、CheckOutした投擲を指定します。2投目でCheckoutした場合、2を指定します。デフォルトは0(CheckOutしていない）です。</param>
        public void SendScore(int roundNo, int score, int checkOut = 0)
        {
            var msg = new ClientMsg()
            {
                SendScore = new Score()
                {
                    Value = score,
                    KeyId = $"{roundNo.ToString()}:1",
                    Checkout = (CheckOutChoices)checkOut
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// Visit入力の時、Changeを送信します。
        /// </summary>
        /// <param name="roundNo"></param>
        /// <param name="score"></param>
        /// <param name="checkOut">Checkoutできた場合、CheckOutした投擲を指定します。2投目でCheckoutした場合、2を指定します。デフォルトは0(CheckOutしていない）です。</param>
        public void SendChangeKeyAsVisit(int roundNo)
        {
            var msg = new ClientMsg()
            {
                SendScore = new Score()
                {
                    Value = -1,
                    KeyId = $"{roundNo.ToString()}:2"
                }
            };

            _queue.Enqueue(msg);
        }

        /// <summary>
        /// Awardをスキップしたことを相手に通知します。
        /// </summary>
        public void SendAwardCut()
        {
            _queue.Enqueue(CustomEvent.AwardCut.CreateClientMsg());
        }

        public void SendGameConfirmed()
        {
            _queue.Enqueue(CustomEvent.GameConfirmed.CreateClientMsg());
        }
    }
}