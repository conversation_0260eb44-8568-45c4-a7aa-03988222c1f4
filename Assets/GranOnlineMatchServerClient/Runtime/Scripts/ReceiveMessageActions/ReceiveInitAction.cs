using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using com.luxza.onlinematchserverclient.interfaces;
using com.luxza.onlinematchserverclient.util;
using com.luxza.onlinematchserverclient.schema;
using Cysharp.Threading.Tasks;
using CorkStatus = com.luxza.onlinematchserverclient.schema.CorkStatus;

namespace com.luxza.onlinematchserverclient.receiveMessageActions
{
    internal class ReceiveInitAction : IReceiveServerMessageAction
    {
        private ServerMsg _msg;
        ConcurrentDictionary<IOnlineMatchServerClientReadyEventListener, byte> _listeners;

        public ReceiveInitAction(ServerMsg msg, ConcurrentDictionary<IOnlineMatchServerClientReadyEventListener, byte> listeners)
        {
            _msg = msg;
            _listeners = listeners;
        }

        public bool Run()
        {
            var msg = _msg.Init;
            if (msg == null)
            {
                throw new ArgumentException($"{_msg} has no {nameof(ServerMsg.Init)}");
            }
            
            if (_listeners.Count == 0) return false;

            var orderedGranIds = msg.Players.OrderBy(p => p.Value.Order).Select(p => p.Key);

            List<CorkHistoryItem> histories = new List<CorkHistoryItem>();
            if (msg.CorkHistory.Count > 0)
            {
                //CorkHistoryは順番保証                
                foreach (var relayKeys in msg.CorkHistory.GroupBy(key => key.GranId))
                {
                    var k = relayKeys.Last();
                    histories.Add(new CorkHistoryItem()
                    {
                        GranId = k.GranId,
                        LastHit = k.Value.ToSegment(),
                        LastThrowIndex = int.Parse(k.KeyId.Split(':').Last())
                    });
                }
            }

            UniTask.Post(
                () =>
                {
                    var historyItems = histories.ToList();
                    foreach (var kv in _listeners)
                    {
                        kv.Key.OnEntry(orderedGranIds.ToArray(),
                            msg.Code.ToIFGameCodeWithNull(),
                            (int)msg.LegNumber,
                            msg.IsFinished,
                            msg.CorkStatus.ToIFCorkStatus(),
                            msg.CorkStatus is CorkStatus.Unnecessary or CorkStatus.Fixed
                                ? null
                                : historyItems);
                    }
                });

            return true;
        }
    }
}