using System;
using System.Collections.Concurrent;
using com.luxza.onlinematchserverclient.interfaces;
using com.luxza.onlinematchserverclient.schema;
using Cysharp.Threading.Tasks;

namespace com.luxza.onlinematchserverclient.receiveMessageActions
{
    public class ReceiveBluetoothRecoveredAction : IReceiveServerMessageAction
    {
        private readonly ServerMsg _msg;
        readonly ConcurrentDictionary<IOnlineMatchServerClientGranBoardEventListener, byte> _listeners;

        public ReceiveBluetoothRecoveredAction
        (
            ServerMsg msg,
            ConcurrentDictionary<IOnlineMatchServerClientGranBoardEventListener, byte> listeners
        )
        {
            _msg = msg;
            _listeners = listeners;
        }

        public bool Run()
        {
            var msg = _msg.BluetoothRecover;

            if (msg == null)
            {
                throw new ArgumentException($"{_msg} has no {nameof(ServerMsg.BluetoothRecover)}");
            }
            if (_listeners.Count == 0) return false;
            UniTask.Post
            (
                () =>
                {
                    foreach (var kv in _listeners)
                    {
                        kv.Key.OnGranBoardConnectionRecovered(msg.GranId);
                    }
                }
            );

            return true;
        }
    }
}