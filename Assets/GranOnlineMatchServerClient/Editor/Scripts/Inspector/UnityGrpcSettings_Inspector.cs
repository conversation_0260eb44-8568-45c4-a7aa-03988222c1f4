using com.luxza.onlinematchserverclient.Editor.Settings;
using UnityEditor;
using UnityEngine.UIElements;

namespace com.luxza.onlinematchserverclient.Editor.Scripts.Inspector
{
    [CustomEditor(typeof(UnityGrpcSettings))]
    public class UnityGrpcSettings_Inspector : UnityEditor.Editor
    {
        public override VisualElement CreateInspectorGUI()
        {
            var e = new VisualElement();
            e.Add(new TextField(""));
            var bt = new Button(() => {
                var select = EditorUtility.OpenFilePanel("Choose your proto file.", "Assets/", ".proto");
                if (select.Length == 0) return;
                if (target is UnityGrpcSettings settings)
                {
                    settings.ProtoFileLocation = select;
                }
                ((TextField) e[0]).value = select;
            });
            bt.Add(new Label("..."));
            e.Add(bt);
            return e;
        }
    }
}