using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using TensorFlowLite;
using UnityEngine;
using com.luxza.granlog;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        #region 镖靶模型相关
        private Board4PointPredictor m_Board4PointPredictor;

        private CalibrationTool[] m_CalibrationTools;

        public CalibrationTool GetCalibrationTool(CameraPos pos)
        {
            return m_CalibrationTools[(int)pos];
        }

        /// <summary>
        /// Explicitly initialize the board detector when needed
        /// </summary>
        /// <returns>A task representing the initialization process</returns>
        public async Task InitializeBoardDetector()
        {
            if (m_Board4PointPredictor != null)
            {
                Log.d("Board detector already initialized.");
                return;
            }

            await InitBoardDetector();
            Log.d("Board detector initialized successfully.");
        }

        /// <summary>
        /// Terminate the board detector when no longer needed
        /// </summary>
        public void TerminateBoardDetector()
        {
            if (m_Board4PointPredictor == null)
            {
                Log.d("Board detector is not initialized.");
                return;
            }

            // 先调用Dispose方法释放TensorFlow资源
            try
            {
                // 尝试调用IDeepLearnDetector接口的Dispose方法来释放TensorFlow资源
                // 这确保了底层的TensorFlow解释器和相关资源被正确释放
                var detector = m_Board4PointPredictor.GetComponent<IDeepLearnDetector>();
                if (detector != null)
                {
                    detector.Dispose();
                    Log.d("Board detector resources disposed successfully.");
                }
            }
            catch (Exception ex)
            {
                Log.e($"Error disposing board detector resources: {ex.Message}");
            }

            // 然后销毁 Board4PointPredictorByOD 游戏对象
            if (m_Board4PointPredictor.gameObject != null)
            {
                GameObject.Destroy(m_Board4PointPredictor.gameObject);

                // 检查容器是否为空，如果为空则销毁容器
                GameObject containerObj = GameObject.Find("GranEyeSDK_DontDestroy");
                if (containerObj != null && containerObj.transform.childCount == 0)
                {
                    GameObject.Destroy(containerObj);
                }
            }

            // 清空引用
            m_Board4PointPredictor = null;

            Log.d("Board detector terminated successfully.");
        }

        public async Task UpdateBoardDetector(float conf, float iou)
        {
            await m_Board4PointPredictor.Init("Calibration_pose.tflite",m_Accelerator);
        }

        private async Task UpdateBoardDetectorAccelerator(TfLiteDelegateType accelerator)
        {
            var t_Conf = Get_Board4Point_Conf();
            var t_Iou = Get_Board4Point_IoU();
            await m_Board4PointPredictor.Init("Calibration_pose.tflite", accelerator);
        }

        private async Task InitBoardDetector()
        {
            var t_Conf = Get_Board4Point_Conf();
            var t_Iou = Get_Board4Point_IoU();

            // 先查找已存在的DontDestroy容器
            var containerObj = GameObject.Find("GranEyeSDK_DontDestroy");

            if (containerObj == null)
            {
                // 创建容器并设置为DontDestroyOnLoad
                containerObj = new GameObject("GranEyeSDK_DontDestroy");
                UnityEngine.Object.DontDestroyOnLoad(containerObj);
            }

            // 查找已有的检测器（在DontDestroyOnLoad容器下查找）
            var existingDetector = containerObj.GetComponentInChildren<Board4PointPredictor>(true);

            if (existingDetector != null)
            {
                // 使用已存在的检测器
                m_Board4PointPredictor = existingDetector;
            }
            else
            {
                // 销毁场景中可能存在的其他检测器实例
                Board4PointPredictor[] otherDetectors = UnityEngine.Object.FindObjectsByType<Board4PointPredictor>(FindObjectsSortMode.None);
                foreach (var detector in otherDetectors)
                {
                    if (detector.transform.parent == null || detector.transform.parent.name != "GranEyeSDK_DontDestroy")
                    {
                        GameObject.Destroy(detector.gameObject);
                    }
                }

                // 创建新的检测器作为容器的子对象
                var detectorObj = new GameObject("Board4PointPredictor");
                detectorObj.transform.SetParent(containerObj.transform);

                m_Board4PointPredictor = detectorObj.AddComponent<Board4PointPredictor>();
            }

            // 初始化检测器
            await m_Board4PointPredictor.Init("Calibration_pose.tflite", TfLiteDelegateType.XNNPACK);
        }

        public async Task<List<TFEntities.PredictResultPointEntity>> PredictBoard(Texture texture)
        {
            var tPoints = await m_Board4PointPredictor.Predict(texture,
                CancellationToken.None);
            return tPoints;
        }

        public async Task CalibrateBothCameras()
        {
            this.m_CalibrationTools = new CalibrationTool[] { new CalibrationTool(), new CalibrationTool() };

            var leftTask = Task.FromResult(Calibration(CameraPos.Left));
            var rightTask = Task.FromResult(Calibration(CameraPos.Right));

            await Task.WhenAll(leftTask, rightTask);
        }

        public (bool,List<Vector3>) Calibration(CameraPos pos)
        {
            CultureInfo culture = CultureInfo.InvariantCulture;
            var isOk = false;
            var posList = new List<Vector3>();

            // Log.d($"{cameraIndex},Start Calibration");
            for (int i = 0; i < 4; i++)
            {
                var dot = Get_Dot(pos,CalibrationType.Auto,i);
                if (dot != Vector2.zero || dot != null)
                {
                    Log.d($"{dot.x}, {dot.y}");
                    var dotPos = new Vector3(dot.x, dot.y, 0);
                    posList.Add(dotPos);
                }
            }

            isOk = this.Calibration(pos, posList);

            return (isOk,posList);
        }
        public bool Calibration(CameraPos pos, List<Vector3> posList)
        {
            var isOk = false;
            if (posList.Count == 4)
            {
                Log.d($"{pos}, Calibration");
                var boardType = Get_BoardBrand();

                this.GetCalibrationTool(pos).Init(
                    posList.ToArray(),
                    (VirtualBoard.CameraPos)pos,
                    boardType);

                isOk = true;
            }
            return isOk;
        }
        #endregion
    }
}