using UnityEngine;
using UnityEngine.UI;

public class MLPDemo : MonoBehaviour
{
    [SerializeField] private Button _Btn_Test;
    private MLPModeler m_MLPModeler;
    void Start()
    {
        m_MLPModeler = new MLPModeler("mlp_model_float32.tflite", new []{1, 9});
        this._Btn_Test.onClick.AddListener(async () =>
        {
            // 输入数据
            var input = new [,]
            {
                {
                    1, 0.4703125f, 0.6241744791666667f, 0, 0.56105322265625f, 0.0894728515625f, 2, 0.2290916015625f, 0.2374375f
                }
            };
            var output = await m_MLPModeler.Predict<float>(input, 3);
            
            // 输出
            Debug.Log($"预测类别: {output.GetValue(0)}"); 
            Debug.Log($"预测x: {output.GetValue(1)}");
            Debug.Log($"预测y: {output.GetValue(2)}");
        });
    }

    private void OnDestroy()
    {
        m_MLPModeler.Dispose();
    }
}
