using System;
using System.Collections.Generic;
using System.Linq;

public class ConcaveHull
{
    // k-nearest neighborsを使った凹包計算
    public static double ComputeConcaveHull(List<double[]> points, int k)
    {
        if (points.Count < 3)
            return 0;

        // k値の検証（少なくとも3以上、点数以下）
        k = Math.Max(3, Math.Min(k, points.Count - 1));

        // 最初の点を見つける（x座標が最小の点）
        var firstPoint = points.OrderBy(p => p[0]).First();
        var hull = new List<double[]> { firstPoint };
        var currentPoint = firstPoint;
        var candidates = new List<double[]>(points);
        candidates.Remove(firstPoint);
        
        double[] previousPoint = new double[] { currentPoint[0], currentPoint[1] - 1 }; // 下向きの仮想点から始める
        
        while ((currentPoint != firstPoint || hull.Count == 1) && candidates.Count > 0)
        {
            if (hull.Count == 3)
            {
                // 最初の点に戻れるかチェック
                var idx = candidates.IndexOf(firstPoint);
                if (idx != -1)
                    candidates.RemoveAt(idx);
            }
            
            // 現在の点からk個の最近傍点を探す
            var kNearest = FindKNearestPoints(currentPoint, candidates, k);
            
            // 角度でソート（前の点からの角度）
            var sortedByAngle = SortByAngle(kNearest, currentPoint, previousPoint);
            
            // 最良の次の点を見つける
            var found = false;
            foreach (var nextPoint in sortedByAngle)
            {
                // 交差チェック
                var intersects = false;
                for (int i = 0; i < hull.Count - 1; i++)
                {
                    if (DoIntersect(hull[i], hull[i + 1], currentPoint, nextPoint))
                    {
                        intersects = true;
                        break;
                    }
                }
                
                if (!intersects)
                {
                    previousPoint = currentPoint;
                    currentPoint = nextPoint;
                    hull.Add(currentPoint);
                    candidates.Remove(currentPoint);
                    found = true;
                    break;
                }
            }
            
            // 次の点が見つからなければ終了
            if (!found)
                break;
        }
        
        return CalculateArea(hull);
    }
    
    // 現在の点からk個の最近傍点を探す
    private static List<double[]> FindKNearestPoints(double[] point, List<double[]> points, int k)
    {
        return points
            .OrderBy(p => Math.Pow(p[0] - point[0], 2) + Math.Pow(p[1] - point[1], 2))
            .Take(k)
            .ToList();
    }
    
    // 角度でソート
    private static List<double[]> SortByAngle(List<double[]> points, double[] center, double[] previous)
    {
        var angles = new Dictionary<double[], double>();
        
        // 前の点からの角度を計算
        double prevAngle = Math.Atan2(previous[1] - center[1], previous[0] - center[0]);
        
        foreach (var point in points)
        {
            double angle = Math.Atan2(point[1] - center[1], point[0] - center[0]);
            // 相対角度を計算（前の点との間）
            double relativeAngle = angle - prevAngle;
            if (relativeAngle < 0)
                relativeAngle += 2 * Math.PI;
            
            angles[point] = relativeAngle;
        }
        
        return points.OrderBy(p => angles[p]).ToList();
    }
    
    // 2線分が交差するかチェック
    private static bool DoIntersect(double[] p1, double[] p2, double[] p3, double[] p4)
    {
        // 線分(p1, p2)と(p3, p4)の交差判定
        double d1 = Cross(p3, p4, p1);
        double d2 = Cross(p3, p4, p2);
        double d3 = Cross(p1, p2, p3);
        double d4 = Cross(p1, p2, p4);
        
        // 交差しているかチェック
        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0)))
            return true;
            
        // 線分上にある場合
        if (d1 == 0 && OnSegment(p3, p4, p1)) return true;
        if (d2 == 0 && OnSegment(p3, p4, p2)) return true;
        if (d3 == 0 && OnSegment(p1, p2, p3)) return true;
        if (d4 == 0 && OnSegment(p1, p2, p4)) return true;
            
        return false;
    }
    
    // 点が線分上にあるかチェック
    private static bool OnSegment(double[] p, double[] q, double[] r)
    {
        return r[0] <= Math.Max(p[0], q[0]) && r[0] >= Math.Min(p[0], q[0]) &&
            r[1] <= Math.Max(p[1], q[1]) && r[1] >= Math.Min(p[1], q[1]);
    }
    
    // 外積計算
    private static double Cross(double[] p, double[] q, double[] r)
    {
        return (q[0] - p[0]) * (r[1] - p[1]) - (q[1] - p[1]) * (r[0] - p[0]);
    }
    
    // 凹包の面積を計算
    public static double CalculateArea(List<double[]> hull)
    {
        double area = 0;
        int n = hull.Count;
        
        for (int i = 0; i < n; i++)
        {
            double[] current = hull[i];
            double[] next = hull[(i + 1) % n];
            
            area += (current[0] * next[1]) - (current[1] * next[0]);
        }
        
        return Math.Abs(area) / 2.0;
    }
}