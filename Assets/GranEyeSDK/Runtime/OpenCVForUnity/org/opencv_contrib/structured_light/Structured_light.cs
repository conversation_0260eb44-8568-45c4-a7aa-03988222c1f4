#if !UNITY_WEBGL

using OpenCVForUnity.CoreModule;
using OpenCVForUnity.UtilsModule;
using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace OpenCVForUnity.Structured_lightModule
{
    // C++: class Structured_light


    public class Structured_light
    {

        // C++: enum <unnamed>
        public const int FTP = 0;
        public const int PSP = 1;
        public const int FAPS = 2;
        public const int DECODE_3D_UNDERWORLD = 0;


    }
}

#endif