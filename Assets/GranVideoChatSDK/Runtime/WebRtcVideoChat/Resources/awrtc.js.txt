!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.awrtc=t():e.awrtc=t()}(self,(()=>(()=>{"use strict";var e={985:e=>{const t={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){const n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter((e=>0===e.indexOf(n)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const n={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":n.relatedAddress=t[e+1];break;case"rport":n.relatedPort=parseInt(t[e+1],10);break;case"tcptype":n.tcpType=t[e+1];break;case"ufrag":n.ufrag=t[e+1],n.usernameFragment=t[e+1];break;default:void 0===n[t[e]]&&(n[t[e]]=t[e+1])}return n},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const n=e.component;"rtp"===n?t.push(1):"rtcp"===n?t.push(2):t.push(n),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const i=e.type;return t.push("typ"),t.push(i),"host"!==i&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){let t=e.substring(9).split(" ");const n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){const t={};let n;const i=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<i.length;e++)n=i[e].trim().split("="),t[n[0].trim()]=n[1];return t},t.writeFmtp=function(e){let t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const i=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?i.push(t+"="+e.parameters[t]):i.push(t)})),t+="a=fmtp:"+n+" "+i.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),n={ssrc:parseInt(e.substring(7,t),10)},i=e.indexOf(":",t);return i>-1?(n.attribute=e.substring(t+1,i),n.value=e.substring(i+1)):n.attribute=e.substring(t+1),n},t.parseSsrcGroup=function(e){const t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substring(6)},t.parseFingerprint=function(e){const t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let n="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),n},t.parseCryptoLine=function(e){const t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,n){return t.matchPrefix(e+n,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,n){const i=t.matchPrefix(e+n,"a=ice-ufrag:")[0],r=t.matchPrefix(e+n,"a=ice-pwd:")[0];return i&&r?{usernameFragment:i.substring(12),password:r.substring(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=t.splitLines(e)[0].split(" ");n.profile=i[2];for(let r=3;r<i.length;r++){const o=i[r],a=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(a){const i=t.parseRtpMap(a),r=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(i.parameters=r.length?t.parseFmtp(r[0]):{},i.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),n.codecs.push(i),i.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(i.name.toUpperCase())}}}t.matchPrefix(e,"a=extmap:").forEach((e=>{n.headerExtensions.push(t.parseExtmap(e))}));const r=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return n.codecs.forEach((e=>{r.forEach((t=>{e.rtcpFeedback.find((e=>e.type===t.type&&e.parameter===t.parameter))||e.rtcpFeedback.push(t)}))})),n},t.writeRtpDescription=function(e,n){let i="";i+="m="+e+" ",i+=n.codecs.length>0?"9":"0",i+=" "+(n.profile||"UDP/TLS/RTP/SAVPF")+" ",i+=n.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",i+="c=IN IP4 0.0.0.0\r\n",i+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach((e=>{i+=t.writeRtpMap(e),i+=t.writeFmtp(e),i+=t.writeRtcpFb(e)}));let r=0;return n.codecs.forEach((e=>{e.maxptime>r&&(r=e.maxptime)})),r>0&&(i+="a=maxptime:"+r+"\r\n"),n.headerExtensions&&n.headerExtensions.forEach((e=>{i+=t.writeExtmap(e)})),i},t.parseRtpEncodingParameters=function(e){const n=[],i=t.parseRtpParameters(e),r=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),a=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),s=a.length>0&&a[0].ssrc;let c;const u=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substring(17).split(" ").map((e=>parseInt(e,10)))));u.length>0&&u[0].length>1&&u[0][0]===s&&(c=u[0][1]),i.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:s,codecPayloadType:parseInt(e.parameters.apt,10)};s&&c&&(t.rtx={ssrc:c}),n.push(t),r&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:s,mechanism:o?"red+ulpfec":"red"},n.push(t))}})),0===n.length&&s&&n.push({ssrc:s});let l=t.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substring(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substring(5),10)*.95-16e3:void 0,n.forEach((e=>{e.maxBitrate=l}))),n},t.parseRtcpParameters=function(e){const n={},i=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];i&&(n.cname=i.value,n.ssrc=i.ssrc);const r=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=r.length>0,n.compound=0===r.length;const o=t.matchPrefix(e,"a=rtcp-mux");return n.mux=o.length>0,n},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let n;const i=t.matchPrefix(e,"a=msid:");if(1===i.length)return n=i[0].substring(7).split(" "),{stream:n[0],track:n[1]};const r=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return r.length>0?(n=r[0].value.split(" "),{stream:n[0],track:n[1]}):void 0},t.parseSctpDescription=function(e){const n=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");let r;i.length>0&&(r=parseInt(i[0].substring(19),10)),isNaN(r)&&(r=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:n.fmt,maxMessageSize:r};const a=t.matchPrefix(e,"a=sctpmap:");if(a.length>0){const e=a[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:r}}},t.writeSctpDescription=function(e,t){let n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,n,i){let r;const o=void 0!==n?n:2;return r=e||t.generateSessionId(),"v=0\r\no="+(i||"thisisadapterortc")+" "+r+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,n){const i=t.splitLines(e);for(let e=0;e<i.length;e++)switch(i[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return i[e].substring(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const n=t.splitLines(e)[0].substring(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){const n=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const n=t.splitLines(e);for(let e=0;e<n.length;e++)if(n[e].length<2||"="!==n[e].charAt(1))return!1;return!0},e.exports=t},20:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ue});var i={};n.r(i),n.d(i,{fixNegotiationNeeded:()=>k,shimAddTrackRemoveTrack:()=>D,shimAddTrackRemoveTrackWithNative:()=>P,shimGetDisplayMedia:()=>b,shimGetSendersWithDtmf:()=>w,shimGetStats:()=>E,shimGetUserMedia:()=>S,shimMediaStream:()=>I,shimOnTrack:()=>R,shimPeerConnection:()=>A,shimSenderReceiverGetStats:()=>T});var r={};n.r(r),n.d(r,{shimAddTransceiver:()=>V,shimCreateAnswer:()=>W,shimCreateOffer:()=>G,shimGetDisplayMedia:()=>_,shimGetParameters:()=>j,shimGetUserMedia:()=>O,shimOnTrack:()=>L,shimPeerConnection:()=>N,shimRTCDataChannel:()=>U,shimReceiverGetStats:()=>x,shimRemoveStream:()=>F,shimSenderGetStats:()=>M});var o={};n.r(o),n.d(o,{shimAudioContext:()=>Y,shimCallbacksAPI:()=>q,shimConstraints:()=>K,shimCreateOfferLegacy:()=>X,shimGetUserMedia:()=>z,shimLocalStreamsAPI:()=>H,shimRTCIceServerUrls:()=>J,shimRemoteStreamsAPI:()=>B,shimTrackEventTransceiver:()=>Q});var a={};n.r(a),n.d(a,{removeExtmapAllowMixed:()=>oe,shimAddIceCandidateNullOrEmpty:()=>ae,shimConnectionState:()=>re,shimMaxMessageSize:()=>ne,shimParameterlessSetLocalDescription:()=>se,shimRTCIceCandidate:()=>ee,shimRTCIceCandidateRelayProtocol:()=>te,shimSendThrowTypeError:()=>ie});let s=!0,c=!0;function u(e,t,n){const i=e.match(t);return i&&i.length>=n&&parseInt(i[n],10)}function l(e,t,n){if(!e.RTCPeerConnection)return;const i=e.RTCPeerConnection.prototype,r=i.addEventListener;i.addEventListener=function(e,i){if(e!==t)return r.apply(this,arguments);const o=e=>{const t=n(e);t&&(i.handleEvent?i.handleEvent(t):i(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(i,o),r.apply(this,[e,o])};const o=i.removeEventListener;i.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(n))return o.apply(this,arguments);const i=this._eventMap[t].get(n);return this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,i])},Object.defineProperty(i,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function d(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(s=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function p(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(c=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function h(){if("object"==typeof window){if(s)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function f(e,t){c&&console.warn(e+" is deprecated, please use "+t+" instead.")}function m(e){return"[object Object]"===Object.prototype.toString.call(e)}function g(e){return m(e)?Object.keys(e).reduce((function(t,n){const i=m(e[n]),r=i?g(e[n]):e[n],o=i&&!Object.keys(r).length;return void 0===r||o?t:Object.assign(t,{[n]:r})}),{}):e}function v(e,t,n){t&&!n.has(t.id)&&(n.set(t.id,t),Object.keys(t).forEach((i=>{i.endsWith("Id")?v(e,e.get(t[i]),n):i.endsWith("Ids")&&t[i].forEach((t=>{v(e,e.get(t),n)}))})))}function y(e,t,n){const i=n?"outbound-rtp":"inbound-rtp",r=new Map;if(null===t)return r;const o=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)})),o.forEach((t=>{e.forEach((n=>{n.type===i&&n.trackId===t.id&&v(e,n,r)}))})),r}const C=h;function S(e,t){const n=e&&e.navigator;if(!n.mediaDevices)return;const i=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const i="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==i.exact&&"number"==typeof i.exact&&(i.min=i.max=i.exact);const r=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==i.ideal){t.optional=t.optional||[];let e={};"number"==typeof i.ideal?(e[r("min",n)]=i.ideal,t.optional.push(e),e={},e[r("max",n)]=i.ideal,t.optional.push(e)):(e[r("",n)]=i.ideal,t.optional.push(e))}void 0!==i.exact&&"number"!=typeof i.exact?(t.mandatory=t.mandatory||{},t.mandatory[r("",n)]=i.exact):["min","max"].forEach((e=>{void 0!==i[e]&&(t.mandatory=t.mandatory||{},t.mandatory[r(e,n)]=i[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},r=function(e,r){if(t.version>=61)return r(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=i(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const a=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||a)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return n.mediaDevices.enumerateDevices().then((n=>{let a=(n=n.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!a&&n.length&&t.includes("back")&&(a=n[n.length-1]),a&&(e.video.deviceId=o.exact?{exact:a.deviceId}:{ideal:a.deviceId}),e.video=i(e.video),C("chrome: "+JSON.stringify(e)),r(e)}))}e.video=i(e.video)}return C("chrome: "+JSON.stringify(e)),r(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(n.getUserMedia=function(e,t,i){r(e,(e=>{n.webkitGetUserMedia(e,t,(e=>{i&&i(o(e))}))}))}.bind(n),n.mediaDevices.getUserMedia){const e=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(t){return r(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(o(e))))))}}}function b(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&("function"==typeof t?e.navigator.mediaDevices.getDisplayMedia=function(n){return t(n).then((t=>{const i=n.video&&n.video.width,r=n.video&&n.video.height,o=n.video&&n.video.frameRate;return n.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:t,maxFrameRate:o||3}},i&&(n.video.mandatory.maxWidth=i),r&&(n.video.mandatory.maxHeight=r),e.navigator.mediaDevices.getUserMedia(n)}))}:console.error("shimGetDisplayMedia: getSourceId argument is not a function"))}function I(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function R(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(n=>{let i;i=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===n.track.id)):{track:n.track};const r=new Event("track");r.track=n.track,r.receiver=i,r.transceiver={receiver:i},r.streams=[t.stream],this.dispatchEvent(r)})),t.stream.getTracks().forEach((n=>{let i;i=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===n.id)):{track:n};const r=new Event("track");r.track=n,r.receiver=i,r.transceiver={receiver:i},r.streams=[t.stream],this.dispatchEvent(r)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else l(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function w(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){let r=n.apply(this,arguments);return r||(r=t(this,e),this._senders.push(r)),r};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){i.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],i.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function E(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,n,i]=arguments;if(arguments.length>0&&"function"==typeof e)return t.apply(this,arguments);if(0===t.length&&(0===arguments.length||"function"!=typeof e))return t.apply(this,[]);const r=function(e){const t={};return e.result().forEach((e=>{const n={id:e.id,timestamp:e.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type};e.names().forEach((t=>{n[t]=e.stat(t)})),t[n.id]=n})),t},o=function(e){return new Map(Object.keys(e).map((t=>[t,e[t]])))};if(arguments.length>=2){const i=function(e){n(o(r(e)))};return t.apply(this,[i,e])}return new Promise(((e,n)=>{t.apply(this,[function(t){e(o(r(t)))},n])})).then(n,i)}}function T(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>y(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),l(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>y(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,i;return this.getSenders().forEach((n=>{n.track===e&&(t?i=!0:t=n)})),this.getReceivers().forEach((t=>(t.track===e&&(n?i=!0:n=t),t.track===e))),i||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function P(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const i=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(i)&&this._shimmedLocalStreams[n.id].push(i):this._shimmedLocalStreams[n.id]=[n,i],i};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();n.apply(this,arguments);const i=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(i)};const i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],i.apply(this,arguments)};const r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),r.apply(this,arguments)}}function D(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return P(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}i.apply(this,[t])};const r=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const i=e._reverseStreams[t],r=e._streams[i.id];n=n.replace(new RegExp(r.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},r.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const r=this._streams[n.id];if(r)r.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const i=new e.MediaStream([t]);this._streams[n.id]=i,this._reverseStreams[i.id]=n,this.addStream(i)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=o(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then((e=>o(this,e)))}};e.RTCPeerConnection.prototype[t]=i[t]}));const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=function(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const i=e._reverseStreams[t],r=e._streams[i.id];n=n.replace(new RegExp(i.id,"g"),r.id)})),new RTCSessionDescription({type:t.type,sdp:n})}(this,arguments[0]),a.apply(this,arguments)):a.apply(this,arguments)};const s=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=s.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(e._pc!==this)throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((n=>{this._streams[n].getTracks().find((t=>e.track===t))&&(t=this._streams[n])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function A(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=i[t]}))}function k(e,t){l(e,"negotiationneeded",(e=>{const n=e.target;if(!(t.version<72||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e}))}function O(e,t){const n=e&&e.navigator,i=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,i){f("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,i)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},i&&i.prototype.getSettings){const t=i.prototype.getSettings;i.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(i&&i.prototype.applyConstraints){const t=i.prototype.applyConstraints;i.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function _(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})}function L(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function N(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=i[t]}));const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,r,o]=arguments;return i.apply(this,[e||null]).then((e=>{if(t.version<53&&!r)try{e.forEach((e=>{e.type=n[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,i)=>{e.set(i,Object.assign({},t,{type:n[t.type]||t.type}))}))}return e})).then(r,o)}}function M(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function x(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),l(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function F(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){f("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function U(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function V(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]),e=[...e];const n=e.length>0;n&&e.forEach((e=>{if("rid"in e&&!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const i=t.apply(this,arguments);if(n){const{sender:t}=i,n=t.getParameters();(!("encodings"in n)||1===n.encodings.length&&0===Object.keys(n.encodings[0]).length)&&(n.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(n).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return i})}function j(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function G(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function W(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function H(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((n=>t.call(this,n,e))),e.getVideoTracks().forEach((n=>t.call(this,n,e)))},e.RTCPeerConnection.prototype.addTrack=function(e,...n){return n&&n.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach((e=>{n.includes(e.track)&&this.removeTrack(e)}))})}}function B(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)}))}),t.apply(e,arguments)}}}function q(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,i=t.createAnswer,r=t.setLocalDescription,o=t.setRemoteDescription,a=t.addIceCandidate;t.createOffer=function(e,t){const i=arguments.length>=2?arguments[2]:arguments[0],r=n.apply(this,[i]);return t?(r.then(e,t),Promise.resolve()):r},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],r=i.apply(this,[n]);return t?(r.then(e,t),Promise.resolve()):r};let s=function(e,t,n){const i=r.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i};t.setLocalDescription=s,s=function(e,t,n){const i=o.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i},t.setRemoteDescription=s,s=function(e,t,n){const i=a.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i},t.addIceCandidate=s}function z(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(K(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,i){t.mediaDevices.getUserMedia(e).then(n,i)}.bind(t))}function K(e){return e&&void 0!==e.video?Object.assign({},e,{video:g(e.video)}):e}function J(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let i=e.iceServers[n];void 0===i.urls&&i.url?(f("RTCIceServer.url","RTCIceServer.urls"),i=JSON.parse(JSON.stringify(i)),i.urls=i.url,delete i.url,t.push(i)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function Q(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function X(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function Y(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var $=n(985),Z=n.n($);function ee(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){const n=new t(e),i=Z().parseCandidate(e.candidate),r=Object.assign(n,i);return r.toJSON=function(){return{candidate:r.candidate,sdpMid:r.sdpMid,sdpMLineIndex:r.sdpMLineIndex,usernameFragment:r.usernameFragment}},r}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,l(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function te(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||l(e,"icecandidate",(e=>{if(e.candidate){const t=Z().parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e}))}function ne(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(function(e){if(!e||!e.sdp)return!1;const t=Z().splitSections(e.sdp);return t.shift(),t.some((e=>{const t=Z().parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){const e=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n}(arguments[0]),n=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n}(e),i=function(e,n){let i=65536;"firefox"===t.browser&&57===t.version&&(i=65535);const r=Z().matchPrefix(e.sdp,"a=max-message-size:");return r.length>0?i=parseInt(r[0].substring(19),10):"firefox"===t.browser&&-1!==n&&(i=2147483637),i}(arguments[0],e);let r;r=0===n&&0===i?Number.POSITIVE_INFINITY:0===n||0===i?Math.max(n,i):Math.min(n,i);const o={};Object.defineProperty(o,"maxMessageSize",{get:()=>r}),this._sctp=o}return n.apply(this,arguments)}}function ie(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const i=arguments[0],r=i.length||i.size||i.byteLength;if("open"===e.readyState&&t.sctp&&r>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},l(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function re(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}}))}function oe(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const n=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:n}):t.sdp=n}return n.apply(this,arguments)}}function ae(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function se(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.setLocalDescription;n&&0!==n.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return n.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}return e.sdp||"offer"!==e.type&&"answer"!==e.type?n.apply(this,[e]):("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then((e=>n.apply(this,[e])))})}const ce=function({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const n=h,s=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator)return t.browser="Not a browser.",t;const{navigator:n}=e;if(n.mozGetUserMedia)t.browser="firefox",t.version=u(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=u(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=u(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),c={browserDetails:s,commonShim:a,extractVersion:u,disableLog:d,disableWarnings:p,sdp:$};switch(s.browser){case"chrome":if(!i||!A||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),c;if(null===s.version)return n("Chrome shim can not determine version, not shimming."),c;n("adapter.js shimming chrome."),c.browserShim=i,ae(e,s),se(e),S(e,s),I(e),A(e,s),R(e),D(e,s),w(e),E(e),T(e),k(e,s),ee(e),te(e),re(e),ne(e,s),ie(e),oe(e,s);break;case"firefox":if(!r||!N||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),c;n("adapter.js shimming firefox."),c.browserShim=r,ae(e,s),se(e),O(e,s),N(e,s),L(e),F(e),M(e),x(e),U(e),V(e),j(e),G(e),W(e),ee(e),re(e),ne(e,s),ie(e);break;case"safari":if(!o||!t.shimSafari)return n("Safari shim is not included in this adapter release."),c;n("adapter.js shimming safari."),c.browserShim=o,ae(e,s),se(e),J(e),X(e),q(e),H(e),B(e),Q(e),z(e),Y(e),ee(e),te(e),ne(e,s),ie(e),oe(e,s);break;default:n("Unsupported browser!")}return c}({window:"undefined"==typeof window?void 0:window}),ue=ce}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var i={};return(()=>{n.r(i),n.d(i,{AWebRtcCall:()=>ce,AWebRtcPeer:()=>N,AudioProcessor:()=>me,AutoplayResolver:()=>fe,BrowserMediaNetwork:()=>Me,BrowserMediaStream:()=>ve,BrowserWebRtcCall:()=>Fe,CAPI_DeviceApi_LastUpdate:()=>xt,CAPI_DeviceApi_RequestUpdate:()=>Mt,CAPI_DeviceApi_Update:()=>Nt,CAPI_InitAsync:()=>We,CAPI_MediaNetwork_Configure:()=>Ct,CAPI_MediaNetwork_Create:()=>yt,CAPI_MediaNetwork_GetConfigurationError:()=>It,CAPI_MediaNetwork_GetConfigurationError_Length:()=>bt,CAPI_MediaNetwork_GetConfigurationState:()=>St,CAPI_MediaNetwork_HasAudioTrack:()=>kt,CAPI_MediaNetwork_HasUserMedia:()=>vt,CAPI_MediaNetwork_HasVideoTrack:()=>Ot,CAPI_MediaNetwork_IsAvailable:()=>gt,CAPI_MediaNetwork_IsMute:()=>Lt,CAPI_MediaNetwork_ResetConfiguration:()=>Rt,CAPI_MediaNetwork_SetMute:()=>_t,CAPI_MediaNetwork_SetVolume:()=>Dt,CAPI_MediaNetwork_SetVolumePan:()=>At,CAPI_MediaNetwork_TryGetFrame:()=>wt,CAPI_MediaNetwork_TryGetFrameDataLength:()=>Pt,CAPI_MediaNetwork_TryGetFrame_Resolution:()=>Tt,CAPI_MediaNetwork_TryGetFrame_ToTexture:()=>Et,CAPI_Media_EnableScreenCapture:()=>qt,CAPI_Media_GetAudioInputDevices:()=>jt,CAPI_Media_GetAudioInputDevices_Length:()=>Vt,CAPI_Media_GetVideoDevices:()=>Ut,CAPI_Media_GetVideoDevices_Length:()=>Ft,CAPI_PollInitState:()=>He,CAPI_SLog_SetLogLevel:()=>Be,CAPI_VideoInput_AddCanvasDevice:()=>Gt,CAPI_VideoInput_AddDevice:()=>Wt,CAPI_VideoInput_RemoveDevice:()=>Ht,CAPI_VideoInput_UpdateFrame:()=>Bt,CAPI_WebRtcNetwork_CheckEventLength:()=>lt,CAPI_WebRtcNetwork_Connect:()=>Ye,CAPI_WebRtcNetwork_Create:()=>Qe,CAPI_WebRtcNetwork_Dequeue:()=>st,CAPI_WebRtcNetwork_DequeueEm:()=>pt,CAPI_WebRtcNetwork_DequeueRtcEvent:()=>mt,CAPI_WebRtcNetwork_Disconnect:()=>et,CAPI_WebRtcNetwork_EventDataToUint8Array:()=>dt,CAPI_WebRtcNetwork_Flush:()=>it,CAPI_WebRtcNetwork_GetBufferedAmount:()=>at,CAPI_WebRtcNetwork_IsAvailable:()=>Ke,CAPI_WebRtcNetwork_IsBrowserSupported:()=>Je,CAPI_WebRtcNetwork_Peek:()=>ct,CAPI_WebRtcNetwork_PeekEm:()=>ht,CAPI_WebRtcNetwork_PeekEventDataLength:()=>ut,CAPI_WebRtcNetwork_Release:()=>Xe,CAPI_WebRtcNetwork_RequestStats:()=>ft,CAPI_WebRtcNetwork_SendData:()=>rt,CAPI_WebRtcNetwork_SendDataEm:()=>ot,CAPI_WebRtcNetwork_Shutdown:()=>tt,CAPI_WebRtcNetwork_StartServer:()=>$e,CAPI_WebRtcNetwork_StopServer:()=>Ze,CAPI_WebRtcNetwork_Update:()=>nt,CallAcceptedEventArgs:()=>J,CallEndedEventArgs:()=>Q,CallErrorType:()=>z,CallEventArgs:()=>K,CallEventType:()=>H,ConnectionId:()=>I,DataMessageEventArgs:()=>ee,Debug:()=>d,DeviceApi:()=>De,Encoder:()=>p,Encoding:()=>f,ErrorEventArgs:()=>Y,FramePixelFormat:()=>ae,FrameUpdateEventArgs:()=>ne,GetUnityCanvas:()=>zt,GetUnityContext:()=>Kt,Helper:()=>g,IFrameData:()=>de,LazyFrame:()=>he,List:()=>c,LocalNetwork:()=>G,Media:()=>Oe,MediaConfig:()=>ie,MediaConfigurationState:()=>j,MediaDevice:()=>Ee,MediaPeer:()=>be,MediaUpdatedEventArgs:()=>te,MessageEventArgs:()=>Z,NetEventDataType:()=>y,NetEventType:()=>v,NetworkConfig:()=>r,NetworkEvent:()=>b,Output:()=>u,PeerConfig:()=>L,Queue:()=>s,Random:()=>m,RawFrame:()=>pe,RtcEvent:()=>T,RtcEventType:()=>S,SLog:()=>C,SLogLevel:()=>t,SLogger:()=>l,SignalingInfo:()=>O,StatsEvent:()=>P,StreamAddedEvent:()=>B,UTF16Encoding:()=>h,VideoInput:()=>Ae,VideoInputType:()=>ge,WaitForIncomingCallEventArgs:()=>$,WebRtcDataPeer:()=>M,WebRtcHelper:()=>a,WebRtcInternalState:()=>E,WebRtcNetwork:()=>U,WebRtcNetworkServerState:()=>_,WebRtcPeerState:()=>w,WebsocketConnectionStatus:()=>x,WebsocketNetwork:()=>V,WebsocketServerStatus:()=>F,gCAPI_WebRtcNetwork_Instances:()=>qe,gCAPI_WebRtcNetwork_InstancesNextIndex:()=>ze});var e,t,r=function(){function e(){this.mIceServers=new Array,this.mSignalingUrl=null,this.mIsConference=!1,this.mMaxIceRestart=0,this.mKeepSignalingAlive=!1,this.mSignalingNetwork=null}return Object.defineProperty(e.prototype,"IceServers",{get:function(){return this.mIceServers},set:function(e){this.mIceServers=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"SignalingUrl",{get:function(){return this.mSignalingUrl},set:function(e){this.mSignalingUrl=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"IsConference",{get:function(){return this.mIsConference},set:function(e){this.mIsConference=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MaxIceRestart",{get:function(){return 0==this.mKeepSignalingAlive?0:this.mMaxIceRestart},set:function(e){this.mMaxIceRestart=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"KeepSignalingAlive",{get:function(){return this.mKeepSignalingAlive},set:function(e){this.mKeepSignalingAlive=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"SignalingNetwork",{get:function(){return this.mSignalingNetwork},set:function(e){this.mSignalingNetwork=e},enumerable:!1,configurable:!0}),e.prototype.GetOrCreateSignalingNetwork=function(){return this.mSignalingNetwork?this.mSignalingNetwork:null==this.mSignalingUrl||""==this.mSignalingUrl?new G:new V(this.mSignalingUrl)},e.prototype.BuildRtcConfig=function(){return{iceServers:this.IceServers}},e.prototype.Clone=function(){var t=new e;return this.CloneTo(t)},e.prototype.CloneTo=function(e){return e.mIceServers=[].concat(this.mIceServers),e.mIsConference=this.mIsConference,e.mKeepSignalingAlive=this.mKeepSignalingAlive,e.mMaxIceRestart=this.mMaxIceRestart,e.mSignalingUrl=this.mSignalingUrl,e.mSignalingNetwork=this.mSignalingNetwork,e},e.prototype.FromJson=function(e){var t=JSON.parse(e);Object.assign(this,t)},e.prototype.IsEqual=function(e){return!!e&&JSON.stringify(this)==JSON.stringify(e)},e.prototype.ToString=function(){return JSON.stringify(this)},e}(),o=(e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)},function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}),a=function(){function e(){}return e.EmitAdapter=function(){0==e.sAdapterActive&&(console.debug("loading webrtc-adapter"),n(20),e.sAdapterActive=!0)},e.sAdapterActive=!1,e}(),s=function(){function e(){this.mArr=new Array}return e.prototype.Enqueue=function(e){this.mArr.push(e)},e.prototype.TryDequeue=function(e){var t=!1;return this.mArr.length>0&&(e.val=this.mArr.shift(),t=!0),t},e.prototype.Dequeue=function(){return this.mArr.length>0?this.mArr.shift():null},e.prototype.Peek=function(){return this.mArr.length>0?this.mArr[0]:null},e.prototype.Count=function(){return this.mArr.length},e.prototype.Clear=function(){this.mArr=new Array},e}(),c=function(){function e(){this.mArr=new Array}return Object.defineProperty(e.prototype,"Internal",{get:function(){return this.mArr},enumerable:!1,configurable:!0}),e.prototype.Add=function(e){this.mArr.push(e)},Object.defineProperty(e.prototype,"Count",{get:function(){return this.mArr.length},enumerable:!1,configurable:!0}),e}(),u=function(){},l=function(){function e(e){this.mPrefix=e}return Object.defineProperty(e.prototype,"Prefix",{get:function(){return this.mPrefix},set:function(e){this.mPrefix=e},enumerable:!1,configurable:!0}),e.prototype.CreateSub=function(t){return new e(this.mPrefix+"."+t)},e.prototype.LV=function(e){C.L(this.mPrefix+": "+e)},e.prototype.L=function(e){C.L(this.mPrefix+": "+e)},e.prototype.LW=function(e){C.LW(this.mPrefix+": "+e)},e.prototype.LE=function(e){C.LE(this.mPrefix+": "+e)},e}(),d=function(){function e(){}return e.Log=function(e){C.Log(e)},e.LogError=function(e){C.LogError(e)},e.LogWarning=function(e){C.LogWarning(e)},e}(),p=function(){},h=function(e){function t(){return e.call(this)||this}return o(t,e),t.prototype.GetBytes=function(e){return this.stringToBuffer(e)},t.prototype.GetString=function(e){return this.bufferToString(e)},t.prototype.bufferToString=function(e){var t=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);return String.fromCharCode.apply(null,t)},t.prototype.stringToBuffer=function(e){for(var t=new ArrayBuffer(2*e.length),n=new Uint16Array(t),i=0,r=e.length;i<r;i++)n[i]=e.charCodeAt(i);return new Uint8Array(t)},t}(p),f=function(){function e(){}return Object.defineProperty(e,"UTF16",{get:function(){return new h},enumerable:!1,configurable:!0}),e}(),m=function(){function e(){}return e.getRandomInt=function(e,t){return e=Math.ceil(e),t=Math.floor(t),Math.floor(Math.random()*(t-e))+e},e}(),g=function(){function e(){}return e.tryParseInt=function(e){try{if(/^(\-|\+)?([0-9]+)$/.test(e)){var t=Number(e);if(0==isNaN(t))return t}}catch(e){}return null},e}();!function(e){e[e.Verbose=0]="Verbose",e[e.Info=1]="Info",e[e.Warnings=2]="Warnings",e[e.Errors=3]="Errors",e[e.None=4]="None"}(t||(t={}));var v,y,C=function(){function e(){}return e.SetTimePrefix=function(t){e.timePrefixEnabled=t,e.startTime=t?Date.now():null},e.formatMessage=function(t){if(e.timePrefixEnabled){var n=Date.now()-e.startTime;return"[".concat(n," ms] ").concat(t)}return t},e.SetLogLevel=function(t){e.sLogLevel=t,e.L("Log level set to: "+t)},e.RequestLogLevel=function(t){t>e.sLogLevel&&(e.sLogLevel=t)},e.L=function(t,n){e.Log(t,n)},e.LW=function(t,n){e.LogWarning(t,n)},e.LE=function(t,n){e.LogError(t,n)},e.Log=function(n,i){e.sLogLevel<=t.Info&&(n=e.formatMessage(n),i?console.log(n,i):console.log(n))},e.LogWarning=function(n,i){i||(i=""),e.sLogLevel<=t.Warnings&&(n=e.formatMessage(n),i?console.warn(n,i):console.warn(n))},e.LogError=function(n,i){e.sLogLevel<=t.Errors&&(n=e.formatMessage(n),i?console.error(n,i):console.error(n))},e.sLogLevel=t.Warnings,e.timePrefixEnabled=!1,e}();!function(e){e[e.Invalid=0]="Invalid",e[e.UnreliableMessageReceived=1]="UnreliableMessageReceived",e[e.ReliableMessageReceived=2]="ReliableMessageReceived",e[e.ServerInitialized=3]="ServerInitialized",e[e.ServerInitFailed=4]="ServerInitFailed",e[e.ServerClosed=5]="ServerClosed",e[e.NewConnection=6]="NewConnection",e[e.ConnectionFailed=7]="ConnectionFailed",e[e.Disconnected=8]="Disconnected",e[e.FatalError=100]="FatalError",e[e.Warning=101]="Warning",e[e.Log=102]="Log",e[e.ReservedStart=200]="ReservedStart",e[e.MetaVersion=201]="MetaVersion",e[e.MetaHeartbeat=202]="MetaHeartbeat"}(v||(v={})),function(e){e[e.Null=0]="Null",e[e.ByteArray=1]="ByteArray",e[e.UTF16String=2]="UTF16String"}(y||(y={}));var S,b=function(){function e(e,t,n){this.type=e,this.connectionId=t,this.data=n}return Object.defineProperty(e.prototype,"RawData",{get:function(){return this.data},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MessageData",{get:function(){return"string"!=typeof this.data?this.data:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Info",{get:function(){return"string"==typeof this.data?this.data:null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Type",{get:function(){return this.type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ConnectionId",{get:function(){return this.connectionId},enumerable:!1,configurable:!0}),e.prototype.toString=function(){var e="NetworkEvent[";return e+="NetEventType: (",e+=v[this.type],e+="), id: (",e+=this.connectionId.id,e+="), Data: (","string"==typeof this.data&&(e+=this.data),e+")]"},e.parseFromString=function(t){var n,i=JSON.parse(t);if(null==i.data)n=null;else if("string"==typeof i.data)n=i.data;else if("object"==typeof i.data){var r=i.data;for(var o in r);for(var a=new Uint8Array(Object.keys(r).length),s=0;s<a.length;s++)a[s]=r[s];n=a}else C.LogError("network event can't be parsed: "+t);return new e(i.type,i.connectionId,n)},e.toString=function(e){return JSON.stringify(e)},e.fromByteArray=function(t){var n=new Uint8Array(t),i=n[0],r=n[1],o=new Int16Array(n.buffer,n.byteOffset+2,1)[0],a=null;if(r==y.ByteArray){var s=new Uint32Array(n.buffer,n.byteOffset+4,1)[0];a=new Uint8Array(n.buffer,n.byteOffset+8,s)}else if(r==y.UTF16String){for(var c=new Uint32Array(n.buffer,n.byteOffset+4,1)[0],u=new Uint16Array(n.buffer,n.byteOffset+8,c),l="",d=0;d<u.length;d++)l+=String.fromCharCode(u[d]);a=l}else if(r!=y.Null)throw new Error("Message has an invalid data type flag: "+r);return new e(i,new I(o),a)},e.toByteArray=function(e){var t,n=4;null==e.data?t=y.Null:"string"==typeof e.data?(t=y.UTF16String,n+=2*(a=e.data).length+4):(t=y.ByteArray,n+=4+(r=e.data).length);var i=new Uint8Array(n);if(i[0]=e.type,i[1]=t,new Int16Array(i.buffer,i.byteOffset+2,1)[0]=e.connectionId.id,t==y.ByteArray){var r=e.data;new Uint32Array(i.buffer,i.byteOffset+4,1)[0]=r.length;for(var o=0;o<r.length;o++)i[8+o]=r[o]}else if(t==y.UTF16String){var a=e.data;new Uint32Array(i.buffer,i.byteOffset+4,1)[0]=a.length;var s=new Uint16Array(i.buffer,i.byteOffset+8,a.length);for(o=0;o<s.length;o++)s[o]=a.charCodeAt(o)}return i},e}(),I=function(){function e(e){this.id=e}return e.INVALID=new e(-1),e}(),R=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();!function(e){e[e.Invalid=0]="Invalid",e[e.Stats=10]="Stats",e[e.StreamAdded=1e3]="StreamAdded"}(S||(S={}));var w,E,T=function(){function e(e,t){this.mType=e,this.mId=t}return Object.defineProperty(e.prototype,"EventType",{get:function(){return this.mType},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ConnectionId",{get:function(){return this.mId},enumerable:!1,configurable:!0}),e}(),P=function(e){function t(t,n){var i=e.call(this,S.Stats,t)||this;return i.mReports=n,i}return R(t,e),Object.defineProperty(t.prototype,"Reports",{get:function(){return this.mReports},enumerable:!1,configurable:!0}),t}(T),D=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),A=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function a(e){try{c(i.next(e))}catch(e){o(e)}}function s(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((i=i.apply(e,t||[])).next())}))},k=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(n=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],i=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},O=function(){function e(e,t,n){this.mConnectionId=e,this.mIsIncoming=t,this.mCreationTime=n,this.mSignalingConnected=!0}return e.prototype.IsSignalingConnected=function(){return this.mSignalingConnected},Object.defineProperty(e.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),e.prototype.IsIncoming=function(){return this.mIsIncoming},e.prototype.GetCreationTimeMs=function(){return Date.now()-this.mCreationTime},e.prototype.SignalingDisconnected=function(){this.mSignalingConnected=!1},e}();!function(e){e[e.Invalid=0]="Invalid",e[e.Created=1]="Created",e[e.Signaling=2]="Signaling",e[e.SignalingFailed=3]="SignalingFailed",e[e.Connected=4]="Connected",e[e.Closing=5]="Closing",e[e.Closed=6]="Closed"}(w||(w={})),function(e){e[e.None=0]="None",e[e.Signaling=1]="Signaling",e[e.SignalingFailed=2]="SignalingFailed",e[e.Connected=3]="Connected",e[e.Closed=4]="Closed"}(E||(E={}));var _,L=function(e){this.MaxIceRestart=e.MaxIceRestart,this.RtcConfig=e.BuildRtcConfig()},N=function(){function e(t,n){var i=this;this.DEBUG=!1,this.LOG_SIGNALING=!0,this.USE_ICE_RESTART_DC_WORKAROUND=!1,this.MAX_RETRIES=2,this.mRetries=0,this.mState=w.Invalid,this.mRtcInternalState=E.None,this.mIncomingSignalingQueue=new s,this.mOutgoingSignalingQueue=new s,this.SINGLE_NEGOTIATION=!1,this.RENEGOTATE_ROLES=!0,this.mInActiveRoleNegotiation=!1,this.mUseRoleNegotiation=!0,this.mRandomNumberSent=0,this.mReadyForIce=!1,this.mBufferedIceCandidates=[],this.mIsOfferer=!1,this.OnIceCandidate=function(e){if(e&&e.candidate){var t=e.candidate,n=JSON.stringify(t);i.EnqueueOutgoing(n)}},this.OnConnectionStateChange=function(e){i.DEBUG&&i.log.LW("onconnectionstatechange: "+i.mPeer.connectionState),"failed"===i.mPeer.connectionState?i.USE_ICE_RESTART&&i.mRetries<i.MAX_RETRIES?(i.mRetries++,i.mIsOfferer?(i.DEBUG&&i.log.LW("Try to reconnect. Attempt "+i.mRetries),i.RestartIce()):i.DEBUG&&i.log.LW("Wait for reconnect")):(i.mRetries>=i.MAX_RETRIES&&i.log.LW("Shutting down peer. IceRestart failed "+i.mRetries+"times"),i.mState==w.Signaling?i.RtcSetSignalingFailed("connectionState switched to failed"):i.mState==w.Connected&&i.RtcSetClosed("ice connection state changed to "+i.mPeer.iceConnectionState)):"connected"===i.mPeer.connectionState&&(i.mRetries=0,i.RENEGOTATE_ROLES&&(i.mUseRoleNegotiation=!0))},this.OnIceGatheringStateChange=function(e){i.DEBUG&&i.log.L("onicegatheringstatechange: "+i.mPeer.iceGatheringState)},this.OnNegotiationNeeded=function(e){i.mState==w.Connected&&(i.SINGLE_NEGOTIATION?i.log.LW("OnNegotiationNeeded: ignored because the peer is configured for single negotiation. This can indicate the peer is configured incorrectly and media will not be sent."):i.RENEGOTATE_ROLES?(i.log.L("OnNegotiationNeeded: renegotiating signaling roles"),i.NegotiateSignaling()):(i.log.L("starting signaling due to OnNegotiationNeeded and RENEGOTATE_ROLES=false"),i.StartSignalingInternal()))},this.OnSignalingChange=function(e){i.DEBUG&&i.log.LW("onsignalingstatechange:"+i.mPeer.signalingState),"closed"==i.mPeer.signalingState&&i.RtcSetClosed("signaling state changed to "+i.mPeer.signalingState)},this.mId=e.sNextId++,this.log=n.CreateSub("Peer"+this.mId),this.USE_ICE_RESTART=t.MaxIceRestart>0,this.MAX_RETRIES=t.MaxIceRestart,this.SetupPeer(t.RtcConfig),this.OnSetup(),this.mState=w.Created,this.DEBUG&&(window["peer"+this.mId]=this)}return e.prototype.GetState=function(){return this.mState},e.prototype.SetupPeer=function(e){this.mPeer=new RTCPeerConnection(e),this.mPeer.onicecandidate=this.OnIceCandidate,this.mPeer.onconnectionstatechange=this.OnConnectionStateChange,this.mPeer.onicegatheringstatechange=this.OnIceGatheringStateChange,this.mPeer.onnegotiationneeded=this.OnNegotiationNeeded,this.mPeer.onsignalingstatechange=this.OnSignalingChange},e.prototype.DisposeInternal=function(){this.Cleanup("Dispose was called")},e.prototype.Dispose=function(){null!=this.mPeer&&this.DisposeInternal()},e.prototype.Cleanup=function(e){this.mState!=w.Closed&&this.mState!=w.Closing&&(this.mState=w.Closing,this.log.L("Peer is closing down. reason: "+e),this.OnCleanup(),null!=this.mPeer&&this.mPeer.close(),this.mState=w.Closed)},e.prototype.Update=function(){this.mState!=w.Closed&&this.mState!=w.Closing&&this.mState!=w.SignalingFailed&&this.UpdateState(),this.HandleIncomingSignaling()},e.prototype.UpdateState=function(){this.mRtcInternalState==E.Closed?this.Cleanup("WebRTC triggered an event to initiate shutdown (see log above)."):this.mRtcInternalState==E.SignalingFailed?this.mState=w.SignalingFailed:this.mRtcInternalState==E.Connected&&(this.mState=w.Connected)},e.prototype.BufferIceCandidate=function(e){this.mBufferedIceCandidates.push(e)},e.prototype.StartIce=function(){if(this.DEBUG&&this.log.L("accepting ice candidates. buffered "+this.mBufferedIceCandidates.length),this.mReadyForIce=!0,this.mBufferedIceCandidates.length>0){this.DEBUG&&this.log.L("adding locally buffered ice candidates");var e=this.mBufferedIceCandidates;this.mBufferedIceCandidates=[];for(var t=0,n=e;t<n.length;t++){var i=n[t];this.AddIceCandidate(i)}}},e.prototype.AddIceCandidate=function(e){var t=this;try{var n=this.mPeer.addIceCandidate(e);n.then((function(){})),n.catch((function(n){t.log.LW("Error during promise addIceCandidate: "+n+"! ice candidate ignored: "+JSON.stringify(e))}))}catch(t){this.log.LW("Error during call to addIceCandidate: "+t+"! ice candidate ignored: "+JSON.stringify(e))}},e.prototype.HandleIncomingSignaling=function(){for(;this.mIncomingSignalingQueue.Count()>0;){var e=this.mIncomingSignalingQueue.Dequeue(),t=g.tryParseInt(e);if(null!=t)this.mUseRoleNegotiation?(!1===this.mInActiveRoleNegotiation&&(this.log.L("Remote side requested renegotiation."),this.NegotiateSignaling()),t<this.mRandomNumberSent?(this.log.L("Role negotiation complete. Starting signaling."),this.StartSignalingInternal()):t==this.mRandomNumberSent?(this.log.L("Retrying role negotiation"),this.NegotiateSignaling()):this.log.L("Role negotiation complete. Waiting for signaling.")):this.log.L("Other side attempted role negotiation but this is inactive. Ignored "+t);else{var n=JSON.parse(e);if(n.sdp){var i=new RTCSessionDescription(n);"offer"==i.type?this.CreateAnswer(i):this.RecAnswer(i)}else{var r=new RTCIceCandidate(n);null!=r&&(this.mReadyForIce?this.AddIceCandidate(r):this.BufferIceCandidate(r))}}}},e.prototype.AddSignalingMessage=function(e){this.LOG_SIGNALING&&this.log.L("incoming Signaling message "+e),this.mIncomingSignalingQueue.Enqueue(e)},e.prototype.DequeueSignalingMessage=function(e){return this.mOutgoingSignalingQueue.Count()>0?(e.val=this.mOutgoingSignalingQueue.Dequeue(),!0):(e.val=null,!1)},e.prototype.EnqueueOutgoing=function(e){this.LOG_SIGNALING&&this.log.L("Outgoing Signaling message "+e),this.mOutgoingSignalingQueue.Enqueue(e)},e.prototype.StartSignaling=function(){C.L("StartSignaling signaling by forcing offer role"),this.mUseRoleNegotiation=!1,this.StartSignalingInternal()},e.prototype.StartSignalingInternal=function(){this.OnStartSignaling(),this.CreateOffer()},e.prototype.NegotiateSignaling=function(){var e=m.getRandomInt(1,2147483647);this.mRandomNumberSent=e,this.mInActiveRoleNegotiation=!0,C.L("Attempting to negotiate signaling using number "+this.mRandomNumberSent),this.EnqueueOutgoing(""+e)},e.prototype.CreateOfferImpl=function(){return A(this,void 0,void 0,(function(){var e;return k(this,(function(t){return e={offerToReceiveAudio:!1,offerToReceiveVideo:!1},[2,this.mPeer.createOffer(e)]}))}))},e.prototype.CreateOffer=function(){var e=this;this.mIsOfferer=!0,this.mReadyForIce=!1,this.mInActiveRoleNegotiation=!1,this.log.L("CreateOffer");var t=this.CreateOfferImpl();t.then((function(t){var n=e.ProcessLocalSdp(t),i=JSON.stringify(n),r=e.mPeer.setLocalDescription(t);r.then((function(){return A(e,void 0,void 0,(function(){return k(this,(function(e){return this.RtcSetSignalingStarted(),this.EnqueueOutgoing(i),[2]}))}))})),r.catch((function(n){e.log.LE(n),e.log.LE("Error during setLocalDescription with sdp: "+JSON.stringify(t)),e.RtcSetSignalingFailed("Failed to set the offer as local description.")}))})),t.catch((function(t){e.log.LE(t),e.RtcSetSignalingFailed("Failed to create an offer.")}))},e.prototype.ProcessLocalSdp=function(e){return e},e.prototype.ProcessRemoteSdp=function(e){return e},e.prototype.CreateAnswerImpl=function(){return A(this,void 0,void 0,(function(){return k(this,(function(e){return[2,this.mPeer.createAnswer()]}))}))},e.prototype.CreateAnswer=function(e){var t=this;this.log.L("CreateAnswer"),this.mInActiveRoleNegotiation=!1,this.mReadyForIce=!1,e=this.ProcessRemoteSdp(e);var n=this.mPeer.setRemoteDescription(e);n.then((function(){t.StartIce();var e=t.CreateAnswerImpl();e.then((function(e){var n=t.ProcessLocalSdp(e),i=JSON.stringify(n),r=t.mPeer.setLocalDescription(e);r.then((function(){t.RtcSetSignalingStarted(),t.EnqueueOutgoing(i)})),r.catch((function(e){t.log.LE(e),t.RtcSetSignalingFailed("Failed to set the answer as local description.")}))})),e.catch((function(e){t.log.LE(e),t.RtcSetSignalingFailed("Failed to create an answer.")}))})),n.catch((function(e){t.log.LE(e),t.RtcSetSignalingFailed("Failed to set the offer as remote description.")}))},e.prototype.RecAnswer=function(e){var t=this;this.DEBUG&&this.log.LW("RecAnswer"),e=this.ProcessRemoteSdp(e);var n=this.mPeer.setRemoteDescription(e);n.then((function(){t.StartIce()})),n.catch((function(e){t.log.LE(e),t.RtcSetSignalingFailed("Failed to set the answer as remote description.")}))},e.prototype.RtcSetSignalingStarted=function(){this.mRtcInternalState==E.None&&(this.mRtcInternalState=E.Signaling)},e.prototype.RtcSetSignalingFailed=function(e){this.log.L("Signaling failed: "+e),this.mRtcInternalState=E.SignalingFailed},e.prototype.RtcSetConnected=function(){this.mRtcInternalState==E.Signaling&&(this.mRtcInternalState=E.Connected)},e.prototype.RtcSetClosed=function(e){this.mRtcInternalState==E.Connected&&(this.mState!==w.Closed&&this.mState!==w.Closing&&this.log.L("WebRTC side event triggered closure. Reason: "+e),this.mRtcInternalState=E.Closed)},e.prototype.TriggerRestartIce=function(){this.mPeer.restartIce(),this.log.LW("restartIce + starting signaling"),this.StartSignalingInternal()},e.prototype.RestartIce=function(){this.TriggerRestartIce()},e.sNextId=0,e}(),M=function(e){function t(t,n,i){var r=e.call(this,n,i)||this;return r.mInfo=null,r.mEvents=new s,r.mRtcEvents=new s,r.mReliableDataChannelReady=!1,r.mUnreliableDataChannelReady=!1,r.mReliableDataChannel=null,r.mUnreliableDataChannel=null,r.mConnectionId=t,r}return D(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"SignalingInfo",{get:function(){return this.mInfo},enumerable:!1,configurable:!0}),t.prototype.SetSignalingInfo=function(e){this.mInfo=e},t.prototype.OnSetup=function(){var e=this;this.mPeer.ondatachannel=function(t){e.OnDataChannel(t.channel)}},t.prototype.OnStartSignaling=function(){if(null===this.mReliableDataChannel&&(this.mReliableDataChannel=this.mPeer.createDataChannel(t.sLabelReliable,{}),this.RegisterObserverReliable()),null===this.mUnreliableDataChannel){this.mUnreliableDataChannel=this.mPeer.createDataChannel(t.sLabelUnreliable,{maxRetransmits:0,ordered:!1}),this.RegisterObserverUnreliable()}},t.prototype.OnCleanup=function(){null!=this.mReliableDataChannel&&this.mReliableDataChannel.close(),null!=this.mUnreliableDataChannel&&this.mUnreliableDataChannel.close()},t.prototype.RegisterObserverReliable=function(){var e=this;this.mReliableDataChannel.onmessage=function(t){e.ReliableDataChannel_OnMessage(t)},this.mReliableDataChannel.onopen=function(t){e.ReliableDataChannel_OnOpen()},this.mReliableDataChannel.onclose=function(t){e.ReliableDataChannel_OnClose()},this.mReliableDataChannel.onerror=function(t){e.ReliableDataChannel_OnError("")}},t.prototype.RegisterObserverUnreliable=function(){var e=this;this.mUnreliableDataChannel.onmessage=function(t){e.UnreliableDataChannel_OnMessage(t)},this.mUnreliableDataChannel.onopen=function(t){e.UnreliableDataChannel_OnOpen()},this.mUnreliableDataChannel.onclose=function(t){e.UnreliableDataChannel_OnClose()},this.mUnreliableDataChannel.onerror=function(t){e.UnreliableDataChannel_OnError("")}},t.prototype.SendData=function(e,t){var n=e,i=1048576,r=!1;try{t?"open"===this.mReliableDataChannel.readyState&&this.mReliableDataChannel.bufferedAmount+n.byteLength<i&&(this.mReliableDataChannel.send(n),r=!0):"open"===this.mUnreliableDataChannel.readyState&&this.mUnreliableDataChannel.bufferedAmount+n.byteLength<i&&(this.mUnreliableDataChannel.send(n),r=!0)}catch(e){this.log.LE("Exception while trying to send: "+e)}return r},t.prototype.GetBufferedAmount=function(e){var t=-1;try{e?"open"===this.mReliableDataChannel.readyState&&(t=this.mReliableDataChannel.bufferedAmount):"open"===this.mUnreliableDataChannel.readyState&&(t=this.mUnreliableDataChannel.bufferedAmount)}catch(e){this.log.LE("Exception while trying to access GetBufferedAmount: "+e)}return t},t.prototype.DequeueEvent=function(e){return this.mEvents.Count()>0&&(e.val=this.mEvents.Dequeue(),!0)},t.prototype.Enqueue=function(e){this.mEvents.Enqueue(e)},t.prototype.OnDataChannel=function(e){var n=e;n.label==t.sLabelReliable?(this.mReliableDataChannel=n,this.RegisterObserverReliable()):n.label==t.sLabelUnreliable?(this.mUnreliableDataChannel=n,this.RegisterObserverUnreliable()):this.log.LE("Datachannel with unexpected label "+n.label)},t.prototype.RtcOnMessageReceived=function(e,t){var n=v.UnreliableMessageReceived;if(t&&(n=v.ReliableMessageReceived),e.data instanceof ArrayBuffer){var i=new Uint8Array(e.data);this.Enqueue(new b(n,this.mConnectionId,i))}else if(e.data instanceof Blob){this.mConnectionId;var r=new FileReader,o=this;r.onload=function(){var e=this.result,t=new Uint8Array(e);o.Enqueue(new b(n,o.mConnectionId,t))},r.readAsArrayBuffer(e.data)}else this.log.LE("Invalid message type. Only blob and arraybuffer supported: "+e.data)},t.prototype.ReliableDataChannel_OnMessage=function(e){this.log.L("ReliableDataChannel_OnMessage "),this.RtcOnMessageReceived(e,!0)},t.prototype.ReliableDataChannel_OnOpen=function(){this.DEBUG&&this.log.LW("mReliableDataChannelReady"),this.mReliableDataChannelReady=!0,this.IsRtcConnected()&&(this.RtcSetConnected(),this.log.L("Fully connected"))},t.prototype.ReliableDataChannel_OnClose=function(){var e="reliable data channel closed";this.USE_ICE_RESTART&&this.USE_ICE_RESTART_DC_WORKAROUND&&this.MAX_RETRIES>0&&this.GetState()==w.Connected?this.log.LW("ICE_RESTART_DC_WORKAROUND: Ignoring data channel closure. "+e):this.RtcSetClosed(e)},t.prototype.ReliableDataChannel_OnError=function(e){var t="reliable data channel error: "+JSON.stringify(e);this.log.LE(t),this.RtcSetClosed(t)},t.prototype.UnreliableDataChannel_OnMessage=function(e){this.log.L("UnreliableDataChannel_OnMessage "),this.RtcOnMessageReceived(e,!1)},t.prototype.UnreliableDataChannel_OnOpen=function(){this.DEBUG&&this.log.LW("mUnreliableDataChannelReady"),this.mUnreliableDataChannelReady=!0,this.IsRtcConnected()&&(this.RtcSetConnected(),this.log.L("Fully connected"))},t.prototype.UnreliableDataChannel_OnClose=function(){var e="unreliable data channel closed";this.USE_ICE_RESTART&&this.USE_ICE_RESTART_DC_WORKAROUND&&this.MAX_RETRIES>0&&this.GetState()==w.Connected?this.log.LW("ICE_RESTART_DC_WORKAROUND: Ignoring data channel closure. "+e):this.RtcSetClosed(e)},t.prototype.UnreliableDataChannel_OnError=function(e){var t="reliable data channel error: "+JSON.stringify(e);this.log.LE(t),this.RtcSetClosed(t)},t.prototype.IsRtcConnected=function(){return this.mReliableDataChannelReady&&this.mUnreliableDataChannelReady},t.prototype.RequestStats=function(){var e=this;setTimeout((function(){return A(e,void 0,void 0,(function(){var e,t,n;return k(this,(function(i){switch(i.label){case 0:return[4,this.mPeer.getStats(null)];case 1:return e=i.sent(),t=Array.from(e.values()),n=new P(this.mConnectionId,t),this.mRtcEvents.Enqueue(n),[2]}}))}))}),0)},t.prototype.DequeueRtcEvent=function(){return this.mRtcEvents.Dequeue()},t.sLabelReliable="reliable",t.sLabelUnreliable="unreliable",t}(N);!function(e){e[e.Invalid=0]="Invalid",e[e.Offline=1]="Offline",e[e.Starting=2]="Starting",e[e.Online=3]="Online"}(_||(_={}));var x,F,U=function(){function e(t){this.mTimeout=6e4,this.mInSignaling={},this.mEvents=new s,this.mRtcEvents=new s,this.mIdToConnection={},this.mConnectionIds=new Array,this.mServerState=_.Offline,this.mIsDisposed=!1,this.mId=e.sNextId++,this.log=new l("WebRtcNetwork"+this.mId),this.mNetConfig=t.Clone(),this.log.L("Creating using NetworkConfig: "+this.mNetConfig.ToString()),this.mSignalingNetwork=this.mNetConfig.GetOrCreateSignalingNetwork()}return Object.defineProperty(e.prototype,"IdToConnection",{get:function(){return this.mIdToConnection},enumerable:!1,configurable:!0}),e.prototype.GetConnections=function(){return this.mConnectionIds},Object.defineProperty(e.prototype,"NetworkConfig",{get:function(){return this.mNetConfig.Clone()},enumerable:!1,configurable:!0}),e.prototype.StartServer=function(e){this.StartServerInternal(e)},e.prototype.StartServerInternal=function(e){this.mServerState=_.Starting,this.mSignalingNetwork.StartServer(e)},e.prototype.StopServer=function(){(this.mServerState==_.Starting||this.mServerState==_.Online)&&this.mSignalingNetwork.StopServer()},e.prototype.Connect=function(e){return this.AddOutgoingConnection(e)},e.prototype.Update=function(){this.CheckSignalingState(),this.UpdateSignalingNetwork(),this.UpdatePeers()},e.prototype.Dequeue=function(){return this.mEvents.Count()>0?this.mEvents.Dequeue():null},e.prototype.Peek=function(){return this.mEvents.Count()>0?this.mEvents.Peek():null},e.prototype.Flush=function(){this.mSignalingNetwork.Flush(),this.mRtcEvents.Clear()},e.prototype.SendData=function(e,t,n){if(null!=e&&null!=t&&0!=t.length){var i=this.mIdToConnection[e.id];return i?i.SendData(t,n):(this.log.LW("unknown connection id"),!1)}},e.prototype.GetBufferedAmount=function(e,t){var n=this.mIdToConnection[e.id];return n?n.GetBufferedAmount(t):(this.log.LW("unknown connection id"),-1)},e.prototype.Disconnect=function(e){this.mIdToConnection[e.id]&&this.HandleDisconnect(e)},e.prototype.Shutdown=function(){for(var e=0,t=this.mConnectionIds.slice();e<t.length;e++){var n=t[e];this.Disconnect(n)}this.StopServer(),this.mSignalingNetwork.Shutdown()},e.prototype.DisposeInternal=function(){0==this.mIsDisposed&&(this.Shutdown(),this.mIsDisposed=!0)},e.prototype.Dispose=function(){this.DisposeInternal()},e.prototype.CreatePeer=function(e){var t=new L(this.mNetConfig);return new M(e,t,this.log)},e.prototype.CheckSignalingState=function(){var e=new Array,t=new Array;for(var n in this.mInSignaling){var i=this.mInSignaling[n];i.Update();for(var r=i.SignalingInfo.GetCreationTimeMs(),o=new u;i.DequeueSignalingMessage(o);){var a=this.StringToBuffer(o.val);this.mSignalingNetwork.SendData(new I(+n),a,!0)}i.GetState()!=w.Connected||i.SignalingInfo.ConnectionId.id in this.mIdToConnection?(i.GetState()==w.SignalingFailed||r>this.mTimeout&&i.GetState()==w.Signaling)&&t.push(i.SignalingInfo.ConnectionId):e.push(i.SignalingInfo.ConnectionId)}for(var s=0,c=e;s<c.length;s++){var l=c[s];this.ConnectionEstablished(l)}for(var d=0,p=t;d<p.length;d++)l=p[d],this.SignalingFailed(l)},e.prototype.UpdateSignalingNetwork=function(){var e;for(this.mSignalingNetwork.Update();null!=(e=this.mSignalingNetwork.Dequeue());)if(e.Type==v.ServerInitialized)this.mServerState=_.Online,this.mEvents.Enqueue(new b(v.ServerInitialized,I.INVALID,e.RawData));else if(e.Type==v.ServerInitFailed)this.mServerState=_.Offline,this.mEvents.Enqueue(new b(v.ServerInitFailed,I.INVALID,e.RawData));else if(e.Type==v.ServerClosed)this.mServerState=_.Offline,this.mEvents.Enqueue(new b(v.ServerClosed,I.INVALID,e.RawData));else if(e.Type==v.NewConnection)(t=this.mInSignaling[e.ConnectionId.id])?t.StartSignaling():this.AddIncomingConnection(e.ConnectionId);else if(e.Type==v.ConnectionFailed)this.SignalingFailed(e.ConnectionId);else if(e.Type==v.Disconnected)(t=this.mInSignaling[e.ConnectionId.id])&&(t.SignalingInfo.SignalingDisconnected(),this.mNetConfig.KeepSignalingAlive&&this.mIdToConnection[e.ConnectionId.id]&&this.HandleDisconnect(e.ConnectionId));else if(e.Type==v.ReliableMessageReceived){var t;if(t=this.mInSignaling[e.ConnectionId.id]||this.mIdToConnection[e.ConnectionId.id]){var n=this.BufferToString(e.MessageData);t.AddSignalingMessage(n)}else this.log.L("No peer found for id "+e.ConnectionId.id+". Dropped signaling message.")}},e.prototype.UpdatePeers=function(){var e=new Array;for(var t in this.mIdToConnection){var n=this.mIdToConnection[t];n.Update();for(var i=new u;n.DequeueEvent(i);)this.mEvents.Enqueue(i.val);n.GetState()==w.Closed&&e.push(n.ConnectionId)}for(var r=0,o=e;r<o.length;r++){var a=o[r];this.HandleDisconnect(a)}},e.prototype.AddOutgoingConnection=function(e){var t=this.mSignalingNetwork.Connect(e);this.log.L("new outgoing connection");var n=new O(t,!1,Date.now()),i=this.CreatePeer(t);return i.SetSignalingInfo(n),this.mInSignaling[t.id]=i,i.ConnectionId},e.prototype.AddIncomingConnection=function(e){this.log.L("new incoming connection");var t=new O(e,!0,Date.now()),n=this.CreatePeer(e);return n.SetSignalingInfo(t),this.mInSignaling[e.id]=n,n.NegotiateSignaling(),n.ConnectionId},e.prototype.ConnectionEstablished=function(e){var t=this.mInSignaling[e.id];!1===this.mNetConfig.KeepSignalingAlive&&this.RemoveSignalingConnection(e),this.mConnectionIds.push(t.ConnectionId),this.mIdToConnection[t.ConnectionId.id]=t,this.mEvents.Enqueue(new b(v.NewConnection,t.ConnectionId,null))},e.prototype.RemoveSignalingConnection=function(e){var t=this.mInSignaling[e.id];t&&(delete this.mInSignaling[e.id],t.SignalingInfo.IsSignalingConnected()&&this.mSignalingNetwork.Disconnect(e))},e.prototype.SignalingFailed=function(e){var t=this.mInSignaling[e.id];t&&(this.RemoveSignalingConnection(e),this.mEvents.Enqueue(new b(v.ConnectionFailed,t.ConnectionId,null)),t.Dispose())},e.prototype.HandleDisconnect=function(e){var t=this.mIdToConnection[e.id];t&&t.Dispose();var n=this.mConnectionIds.findIndex((function(t){return t.id==e.id}));-1!=n&&(this.mConnectionIds.splice(n,1),delete this.mIdToConnection[e.id]),this.RemoveSignalingConnection(e);var i=new b(v.Disconnected,e,null);this.mEvents.Enqueue(i)},e.prototype.StringToBuffer=function(e){for(var t=new ArrayBuffer(2*e.length),n=new Uint16Array(t),i=0,r=e.length;i<r;i++)n[i]=e.charCodeAt(i);return new Uint8Array(t)},e.prototype.BufferToString=function(e){var t=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);return String.fromCharCode.apply(null,t)},e.prototype.RequestStats=function(){for(var e in this.mIdToConnection)this.mIdToConnection[e].RequestStats()},e.prototype.EnqueueRtcEvent=function(e){this.mRtcEvents.Enqueue(e)},e.prototype.DequeueRtcEvent=function(){if(this.mRtcEvents.Count()>0)return this.mRtcEvents.Dequeue();for(var e in this.mIdToConnection){var t=this.mIdToConnection[e].DequeueRtcEvent();if(t)return t}return null},e.sNextId=0,e}();!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.NotConnected=1]="NotConnected",e[e.Connecting=2]="Connecting",e[e.Connected=3]="Connected",e[e.Disconnecting=4]="Disconnecting"}(x||(x={})),function(e){e[e.Offline=0]="Offline",e[e.Starting=1]="Starting",e[e.Online=2]="Online",e[e.ShuttingDown=3]="ShuttingDown"}(F||(F={}));var V=function(){function e(t,n){this.mStatus=x.Uninitialized,this.mOutgoingQueue=new Array,this.mIncomingQueue=new Array,this.mServerStatus=F.Offline,this.mConnecting=new Array,this.mConnections=new Array,this.mNextOutgoingConnectionId=new I(1),this.mRemoteProtocolVersion=1,this.mUrl=null,this.mHeartbeatReceived=!0,this.mIsDisposed=!1,this.mUrl=t,this.mStatus=x.NotConnected,this.mConfig=n,this.mConfig||(this.mConfig=new e.Configuration),this.mConfig.Lock()}return e.prototype.getStatus=function(){return this.mStatus},e.prototype.WebsocketConnect=function(){var e=this;this.mStatus=x.Connecting,this.mSocket=new WebSocket(this.mUrl),this.mSocket.binaryType="arraybuffer",this.mSocket.onopen=function(){e.OnWebsocketOnOpen()},this.mSocket.onerror=function(t){e.OnWebsocketOnError(t)},this.mSocket.onmessage=function(t){e.OnWebsocketOnMessage(t)},this.mSocket.onclose=function(t){e.OnWebsocketOnClose(t)}},e.prototype.WebsocketCleanup=function(){this.mSocket.onopen=null,this.mSocket.onerror=null,this.mSocket.onmessage=null,this.mSocket.onclose=null,this.mSocket.readyState!=this.mSocket.OPEN&&this.mSocket.readyState!=this.mSocket.CONNECTING||(C.L("closing websockets"),this.mSocket.close()),this.mSocket=null},e.prototype.EnsureServerConnection=function(){this.mStatus==x.NotConnected&&this.WebsocketConnect()},e.prototype.UpdateHeartbeat=function(){if(this.mStatus==x.Connected&&this.mConfig.Heartbeat>0&&Date.now()-this.mLastHeartbeat>1e3*this.mConfig.Heartbeat){if(this.mRemoteProtocolVersion>1&&0==this.mHeartbeatReceived)return void this.TriggerHeartbeatTimeout();this.mLastHeartbeat=Date.now(),this.mHeartbeatReceived=!1,this.SendHeartbeat()}},e.prototype.TriggerHeartbeatTimeout=function(){C.L("Closing due to heartbeat timeout. Server didn't respond in time.",e.LOGTAG),this.Cleanup()},e.prototype.CheckSleep=function(){this.mStatus==x.Connected&&this.mServerStatus==F.Offline&&0==this.mConnecting.length&&0==this.mConnections.length&&this.Cleanup()},e.prototype.OnWebsocketOnOpen=function(){C.L("onWebsocketOnOpen",e.LOGTAG),this.mStatus=x.Connected,this.mLastHeartbeat=Date.now(),this.SendVersion()},e.prototype.OnWebsocketOnClose=function(t){C.L("Closed: "+JSON.stringify(t),e.LOGTAG),1e3!=t.code&&C.LE("Websocket closed with code: "+t.code+" "+t.reason),this.mStatus!=x.Disconnecting&&this.mStatus!=x.NotConnected&&(this.Cleanup(),this.mStatus=x.NotConnected)},e.prototype.OnWebsocketOnMessage=function(e){if(this.mStatus!=x.Disconnecting&&this.mStatus!=x.NotConnected){var t=new Uint8Array(e.data);this.ParseMessage(t)}},e.prototype.OnWebsocketOnError=function(e){console.error("Websocket triggered onerror: ",e),C.LE("WebSocket Error. See browser log for more information. "+JSON.stringify(e))},e.prototype.Cleanup=function(){if(this.mStatus!=x.Disconnecting&&this.mStatus!=x.NotConnected){this.mStatus=x.Disconnecting;for(var e=0,t=this.mConnecting;e<t.length;e++){var n=t[e];this.EnqueueIncoming(new b(v.ConnectionFailed,new I(n),null))}this.mConnecting=new Array;for(var i=0,r=this.mConnections;i<r.length;i++)n=r[i],this.EnqueueIncoming(new b(v.Disconnected,new I(n),null));this.mConnections=new Array,this.mServerStatus==F.Starting?this.EnqueueIncoming(new b(v.ServerInitFailed,I.INVALID,null)):(this.mServerStatus==F.Online||this.mServerStatus==F.ShuttingDown)&&this.EnqueueIncoming(new b(v.ServerClosed,I.INVALID,null)),this.mServerStatus=F.Offline,this.mOutgoingQueue=new Array,this.WebsocketCleanup(),this.mStatus=x.NotConnected}},e.prototype.EnqueueOutgoing=function(e){this.mOutgoingQueue.push(e)},e.prototype.EnqueueIncoming=function(e){this.mIncomingQueue.push(e)},e.prototype.TryRemoveConnecting=function(e){var t=this.mConnecting.indexOf(e.id);-1!=t&&this.mConnecting.splice(t,1)},e.prototype.TryRemoveConnection=function(e){var t=this.mConnections.indexOf(e.id);-1!=t&&this.mConnections.splice(t,1)},e.prototype.ParseMessage=function(e){if(0==e.length);else if(e[0]==v.MetaVersion)e.length>1?this.mRemoteProtocolVersion=e[1]:C.LW("Received an invalid MetaVersion header without content.");else if(e[0]==v.MetaHeartbeat)this.mHeartbeatReceived=!0;else{var t=b.fromByteArray(e);this.HandleIncomingEvent(t)}},e.prototype.HandleIncomingEvent=function(e){e.Type==v.NewConnection?(this.TryRemoveConnecting(e.ConnectionId),this.mConnections.push(e.ConnectionId.id)):e.Type==v.ConnectionFailed?this.TryRemoveConnecting(e.ConnectionId):e.Type==v.Disconnected?this.TryRemoveConnection(e.ConnectionId):e.Type==v.ServerInitialized?this.mServerStatus=F.Online:e.Type==v.ServerInitFailed?this.mServerStatus=F.Offline:e.Type==v.ServerClosed&&(this.mServerStatus=F.ShuttingDown,this.mServerStatus=F.Offline),this.EnqueueIncoming(e)},e.prototype.HandleOutgoingEvents=function(){for(;this.mOutgoingQueue.length>0;){var e=this.mOutgoingQueue.shift();this.SendNetworkEvent(e)}},e.prototype.SendHeartbeat=function(){var e=new Uint8Array(1);e[0]=v.MetaHeartbeat,this.InternalSend(e)},e.prototype.SendVersion=function(){var t=new Uint8Array(2);t[0]=v.MetaVersion,t[1]=e.PROTOCOL_VERSION,this.InternalSend(t)},e.prototype.SendNetworkEvent=function(e){var t=b.toByteArray(e);this.InternalSend(t)},e.prototype.InternalSend=function(e){this.mSocket.send(e)},e.prototype.NextConnectionId=function(){var e=this.mNextOutgoingConnectionId;return this.mNextOutgoingConnectionId=new I(this.mNextOutgoingConnectionId.id+1),e},e.prototype.GetRandomKey=function(){for(var e="",t=0;t<7;t++)e+=String.fromCharCode(65+Math.round(25*Math.random()));return e},e.prototype.Dequeue=function(){return this.mIncomingQueue.length>0?this.mIncomingQueue.shift():null},e.prototype.Peek=function(){return this.mIncomingQueue.length>0?this.mIncomingQueue[0]:null},e.prototype.Update=function(){this.UpdateHeartbeat(),this.CheckSleep()},e.prototype.Flush=function(){this.mStatus==x.Connected&&this.HandleOutgoingEvents()},e.prototype.SendData=function(e,t,n){if(null!=e&&e.id!=I.INVALID.id){var i;if(null!=t&&0!=t.length)return i=new b(n?v.ReliableMessageReceived:v.UnreliableMessageReceived,e,t),this.EnqueueOutgoing(i),!0;C.LW("Ignored message. Invalid data.")}else C.LW("Ignored message. Invalid connection id.")},e.prototype.Disconnect=function(e){var t=new b(v.Disconnected,e,null);this.EnqueueOutgoing(t)},e.prototype.Shutdown=function(){this.Cleanup(),this.mStatus=x.NotConnected},e.prototype.Dispose=function(){0==this.mIsDisposed&&(this.Shutdown(),this.mIsDisposed=!0)},e.prototype.StartServer=function(e){null==e&&(e=""+this.GetRandomKey()),this.mServerStatus==F.Offline?(this.EnsureServerConnection(),this.mServerStatus=F.Starting,this.EnqueueOutgoing(new b(v.ServerInitialized,I.INVALID,e))):this.EnqueueIncoming(new b(v.ServerInitFailed,I.INVALID,e))},e.prototype.StopServer=function(){this.EnqueueOutgoing(new b(v.ServerClosed,I.INVALID,null))},e.prototype.Connect=function(e){this.EnsureServerConnection();var t=this.NextConnectionId();this.mConnecting.push(t.id);var n=new b(v.NewConnection,t,e);return this.EnqueueOutgoing(n),t},e.LOGTAG="WebsocketNetwork",e.PROTOCOL_VERSION=2,e.PROTOCOL_VERSION_MIN=1,e}();!function(e){var t=function(){function e(){this.mHeartbeat=30,this.mLocked=!1}return Object.defineProperty(e.prototype,"Heartbeat",{get:function(){return this.mHeartbeat},set:function(e){if(this.mLocked)throw new Error("Can't change configuration once used.");this.mHeartbeat=e},enumerable:!1,configurable:!0}),e.prototype.Lock=function(){this.mLocked=!0},e}();e.Configuration=t}(V||(V={}));var j,G=function(){function e(){this.mNextNetworkId=new I(1),this.mServerAddress=null,this.mEvents=new s,this.mConnectionNetwork={},this.mIsDisposed=!1,this.mId=e.sNextId,e.sNextId++}return Object.defineProperty(e.prototype,"IsServer",{get:function(){return null!=this.mServerAddress},enumerable:!1,configurable:!0}),e.prototype.StartServer=function(t){void 0===t&&(t=null),null==t&&(t=""+this.mId),t in e.mServers?this.Enqueue(v.ServerInitFailed,I.INVALID,t):(e.mServers[t]=this,this.mServerAddress=t,this.Enqueue(v.ServerInitialized,I.INVALID,t))},e.prototype.StopServer=function(){this.IsServer&&(this.Enqueue(v.ServerClosed,I.INVALID,this.mServerAddress),delete e.mServers[this.mServerAddress],this.mServerAddress=null)},e.prototype.Connect=function(t){var n=this.NextConnectionId(),i=!1;if(t in e.mServers){var r=e.mServers[t];null!=r&&(r.ConnectClient(this),this.mConnectionNetwork[n.id]=e.mServers[t],this.Enqueue(v.NewConnection,n,null),i=!0)}return 0==i&&this.Enqueue(v.ConnectionFailed,n,"Couldn't connect to the given server with id "+t),n},e.prototype.Shutdown=function(){for(var e in this.mConnectionNetwork)this.Disconnect(new I(+e));this.StopServer()},e.prototype.Dispose=function(){0==this.mIsDisposed&&this.Shutdown()},e.prototype.SendData=function(e,t,n){return e.id in this.mConnectionNetwork&&(this.mConnectionNetwork[e.id].ReceiveData(this,t,n),!0)},e.prototype.Update=function(){this.CleanupWreakReferences()},e.prototype.Dequeue=function(){return this.mEvents.Dequeue()},e.prototype.Peek=function(){return this.mEvents.Peek()},e.prototype.Flush=function(){},e.prototype.Disconnect=function(e){if(e.id in this.mConnectionNetwork){var t=this.mConnectionNetwork[e.id];null!=t?(t.InternalDisconnectNetwork(this),this.InternalDisconnect(e)):this.CleanupWreakReferences()}},e.prototype.FindConnectionId=function(e){for(var t in this.mConnectionNetwork)if(null!=this.mConnectionNetwork[t])return new I(+t);return I.INVALID},e.prototype.NextConnectionId=function(){var e=this.mNextNetworkId;return this.mNextNetworkId=new I(e.id+1),e},e.prototype.ConnectClient=function(e){var t=this.NextConnectionId();this.mConnectionNetwork[t.id]=e,this.Enqueue(v.NewConnection,t,null)},e.prototype.Enqueue=function(e,t,n){var i=new b(e,t,n);this.mEvents.Enqueue(i)},e.prototype.ReceiveData=function(e,t,n){for(var i=this.FindConnectionId(e),r=new Uint8Array(t.length),o=0;o<r.length;o++)r[o]=t[o];var a=v.UnreliableMessageReceived;n&&(a=v.ReliableMessageReceived),this.Enqueue(a,i,r)},e.prototype.InternalDisconnect=function(e){e.id in this.mConnectionNetwork&&(this.Enqueue(v.Disconnected,e,null),delete this.mConnectionNetwork[e.id])},e.prototype.InternalDisconnectNetwork=function(e){this.InternalDisconnect(this.FindConnectionId(e))},e.prototype.CleanupWreakReferences=function(){},e.sNextId=1,e.mServers={},e}(),W=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();!function(e){e[e.Invalid=0]="Invalid",e[e.NoConfiguration=1]="NoConfiguration",e[e.InProgress=2]="InProgress",e[e.Successful=3]="Successful",e[e.Failed=4]="Failed"}(j||(j={}));var H,B=function(e){function t(t,n){var i=e.call(this,S.StreamAdded,t)||this;return i.mArgs=n,i}return W(t,e),Object.defineProperty(t.prototype,"Args",{get:function(){return this.mArgs},enumerable:!1,configurable:!0}),t}(T),q=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();!function(e){e[e.Invalid=0]="Invalid",e[e.WaitForIncomingCall=1]="WaitForIncomingCall",e[e.CallAccepted=2]="CallAccepted",e[e.CallEnded=3]="CallEnded",e[e.FrameUpdate=4]="FrameUpdate",e[e.Message=5]="Message",e[e.ConnectionFailed=6]="ConnectionFailed",e[e.ListeningFailed=7]="ListeningFailed",e[e.ConfigurationComplete=8]="ConfigurationComplete",e[e.ConfigurationFailed=9]="ConfigurationFailed",e[e.DataMessage=10]="DataMessage",e[e.MediaUpdate=20]="MediaUpdate"}(H||(H={}));var z,K=function(){function e(e){this.mType=H.Invalid,this.mType=e}return Object.defineProperty(e.prototype,"Type",{get:function(){return this.mType},enumerable:!1,configurable:!0}),e}(),J=function(e){function t(t){var n=e.call(this,H.CallAccepted)||this;return n.mConnectionId=I.INVALID,n.mConnectionId=t,n}return q(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),t}(K),Q=function(e){function t(t){var n=e.call(this,H.CallEnded)||this;return n.mConnectionId=I.INVALID,n.mConnectionId=t,n}return q(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),t}(K);!function(e){e[e.Unknown=0]="Unknown"}(z||(z={}));var X,Y=function(e){function t(t,n,i){var r=e.call(this,t)||this;if(r.mErrorType=z.Unknown,r.mErrorType=n,r.mErrorMessage=i,null==r.mErrorMessage)switch(t){case H.ConnectionFailed:r.mErrorMessage="Connection failed.";break;case H.ListeningFailed:r.mErrorMessage="Failed to allow incoming connections. Address already in use or server connection failed.";break;default:r.mErrorMessage="Unknown error."}return r}return q(t,e),Object.defineProperty(t.prototype,"ErrorMessage",{get:function(){return this.mErrorMessage},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ErrorType",{get:function(){return this.mErrorType},enumerable:!1,configurable:!0}),t}(K),$=function(e){function t(t){var n=e.call(this,H.WaitForIncomingCall)||this;return n.mAddress=t,n}return q(t,e),Object.defineProperty(t.prototype,"Address",{get:function(){return this.mAddress},enumerable:!1,configurable:!0}),t}(K),Z=function(e){function t(t,n,i){var r=e.call(this,H.Message)||this;return r.mConnectionId=I.INVALID,r.mConnectionId=t,r.mContent=n,r.mReliable=i,r}return q(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Content",{get:function(){return this.mContent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Reliable",{get:function(){return this.mReliable},enumerable:!1,configurable:!0}),t}(K),ee=function(e){function t(t,n,i){var r=e.call(this,H.DataMessage)||this;return r.mConnectionId=I.INVALID,r.mConnectionId=t,r.mContent=n,r.mReliable=i,r}return q(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Content",{get:function(){return this.mContent},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Reliable",{get:function(){return this.mReliable},enumerable:!1,configurable:!0}),t}(K),te=function(e){function t(t,n){var i=e.call(this,H.MediaUpdate)||this;return i.mConnectionId=I.INVALID,i.mConnectionId=t,i.mVideoElement=n,i}return q(t,e),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"IsRemote",{get:function(){return this.mConnectionId.id!=I.INVALID.id},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"VideoElement",{get:function(){return this.mVideoElement},enumerable:!1,configurable:!0}),t}(K),ne=function(e){function t(t,n){var i=e.call(this,H.FrameUpdate)||this;return i.mConnectionId=I.INVALID,i.mConnectionId=t,i.mFrame=n,i}return q(t,e),Object.defineProperty(t.prototype,"Frame",{get:function(){return this.mFrame},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ConnectionId",{get:function(){return this.mConnectionId},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"IsRemote",{get:function(){return this.mConnectionId.id!=I.INVALID.id},enumerable:!1,configurable:!0}),t}(K),ie=function(){function e(){this.mAudio=!1,this.mVideo=!1,this.mVideoDeviceName="",this.mAudioInputDevice="",this.mMinWidth=-1,this.mMinHeight=-1,this.mMaxWidth=-1,this.mMaxHeight=-1,this.mIdealWidth=-1,this.mIdealHeight=-1,this.mMinFps=-1,this.mMaxFps=-1,this.mIdealFps=-1,this.mVideoCodecs=[],this.mVideoBitrateKbits=null,this.mVideoContentHint=null,this.mFrameUpdates=!1}return Object.defineProperty(e.prototype,"Audio",{get:function(){return this.mAudio},set:function(e){this.mAudio=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Video",{get:function(){return this.mVideo},set:function(e){this.mVideo=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VideoDeviceName",{get:function(){return this.mVideoDeviceName},set:function(e){this.mVideoDeviceName=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"AudioInputDevice",{get:function(){return this.mAudioInputDevice},set:function(e){this.mAudioInputDevice=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MinWidth",{get:function(){return this.mMinWidth},set:function(e){this.mMinWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MinHeight",{get:function(){return this.mMinHeight},set:function(e){this.mMinHeight=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MaxWidth",{get:function(){return this.mMaxWidth},set:function(e){this.mMaxWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MaxHeight",{get:function(){return this.mMaxHeight},set:function(e){this.mMaxHeight=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"IdealWidth",{get:function(){return this.mIdealWidth},set:function(e){this.mIdealWidth=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"IdealHeight",{get:function(){return this.mIdealHeight},set:function(e){this.mIdealHeight=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MinFps",{get:function(){return this.mMinFps},set:function(e){this.mMinFps=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"MaxFps",{get:function(){return this.mMaxFps},set:function(e){this.mMaxFps=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"IdealFps",{get:function(){return this.mIdealFps},set:function(e){this.mIdealFps=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VideoCodecs",{get:function(){return this.mVideoCodecs},set:function(e){this.mVideoCodecs=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VideoBitrateKbits",{get:function(){return this.mVideoBitrateKbits},set:function(e){this.mVideoBitrateKbits=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VideoContentHint",{get:function(){return this.mVideoContentHint},set:function(e){this.mVideoContentHint=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"FrameUpdates",{get:function(){return this.mFrameUpdates},set:function(e){this.mFrameUpdates=e},enumerable:!1,configurable:!0}),e.prototype.clone=function(){var t=JSON.parse(JSON.stringify(this));return Object.assign(new e,t)},e.prototype.toString=function(){return JSON.stringify(this)},e}(),re=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),oe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return re(t,e),t}(function(){function e(e){this.mErrorMsg=e}return e.prototype.ErrorMsg=function(){},e}());!function(e){e[e.Invalid=0]="Invalid",e[e.Initialized=1]="Initialized",e[e.Configuring=2]="Configuring",e[e.Configured=3]="Configured",e[e.RequestingAddress=4]="RequestingAddress",e[e.WaitingForIncomingCall=5]="WaitingForIncomingCall",e[e.WaitingForOutgoingCall=6]="WaitingForOutgoingCall",e[e.InCall=7]="InCall",e[e.Closed=8]="Closed"}(X||(X={}));var ae,se=function(){function e(){this.mConnectionIds=new Array}return e.prototype.AddConnection=function(e,t){this.mConnectionIds.push(e.id)},e.prototype.RemConnection=function(e){var t=this.mConnectionIds.indexOf(e.id);t>=0?this.mConnectionIds.splice(t,1):C.LE("tried to remove an unknown connection with id "+e.id)},e.prototype.HasConnection=function(e){return-1!=this.mConnectionIds.indexOf(e.id)},e.prototype.GetIds=function(){return this.mConnectionIds},e}(),ce=function(){function e(e){void 0===e&&(e=null),this.MESSAGE_TYPE_INVALID=0,this.MESSAGE_TYPE_DATA=1,this.MESSAGE_TYPE_STRING=2,this.MESSAGE_TYPE_CONTROL=3,this.mNetworkConfig=new r,this.mMediaConfig=new ie,this.mCallEventHandlers=[],this.mNetwork=null,this.mConnectionInfo=new se,this.mConferenceMode=!1,this.mState=X.Invalid,this.mIsDisposed=!1,this.mServerInactive=!0,this.mPendingListenCall=!1,this.mPendingCallCall=!1,this.mPendingAddress=null,null!=e&&(this.mNetworkConfig=e,this.mConferenceMode=e.IsConference)}return e.prototype.addEventListener=function(e){this.mCallEventHandlers.push(e)},e.prototype.removeEventListener=function(e){this.mCallEventHandlers=this.mCallEventHandlers.filter((function(t){return t!==e}))},Object.defineProperty(e.prototype,"State",{get:function(){return this.mState},enumerable:!1,configurable:!0}),e.prototype.Initialize=function(e){this.mNetwork=e,this.mState=X.Initialized},e.prototype.Configure=function(e){this.CheckDisposed(),this.mState=X.Configuring,C.Log("Enter state CallState.Configuring"),this.mMediaConfig=e,this.mNetwork.Configure(this.mMediaConfig)},e.prototype.Call=function(e){if(this.CheckDisposed(),this.mState!=X.Initialized&&this.mState!=X.Configuring&&this.mState!=X.Configured)throw new oe("Method can't be used in state "+this.mState);if(this.mConferenceMode)throw new oe("Method can't be used in conference calls.");C.Log("Call to "+e),this.EnsureConfiguration(),this.mState==X.Configured?this.ProcessCall(e):this.PendingCall(e)},e.prototype.Listen=function(e){if(this.CheckDisposed(),this.mState!=X.Initialized&&this.mState!=X.Configuring&&this.mState!=X.Configured)throw new oe("Method can't be used in state "+this.mState);this.EnsureConfiguration(),this.mState==X.Configured?this.ProcessListen(e):this.PendingListen(e)},e.prototype.Send=function(e,t,n){this.CheckDisposed(),null==t&&(t=!0),n?this.InternalSendTo(e,t,n):this.InternalSendToAll(e,t)},e.prototype.InternalSendToAll=function(e,t){for(var n=this.PackStringMsg(e),i=0,r=this.mConnectionInfo.GetIds();i<r.length;i++){var o=r[i];C.L("Send message to "+o+"! "+e),this.InternalSendRawTo(n,new I(o),t)}},e.prototype.InternalSendTo=function(e,t,n){var i=this.PackStringMsg(e);this.InternalSendRawTo(i,n,t)},e.prototype.SendData=function(e,t,n){this.CheckDisposed();var i=this.PackDataMsg(e);this.InternalSendRawTo(i,n,t)},e.prototype.PackStringMsg=function(e){var t=f.UTF16.GetBytes(e),n=new Uint8Array(t.length+1);n[0]=this.MESSAGE_TYPE_STRING;for(var i=0;i<t.length;i++)n[i+1]=t[i];return n},e.prototype.UnpackStringMsg=function(e){for(var t=new Uint8Array(e.length-1),n=0;n<t.length;n++)t[n]=e[n+1];return f.UTF16.GetString(t)},e.prototype.PackDataMsg=function(e){var t=new Uint8Array(e.length+1);t[0]=this.MESSAGE_TYPE_DATA;for(var n=0;n<e.length;n++)t[n+1]=e[n];return t},e.prototype.UnpackDataMsg=function(e){for(var t=new Uint8Array(e.length-1),n=0;n<t.length;n++)t[n]=e[n+1];return t},e.prototype.InternalSendRawTo=function(e,t,n){this.mNetwork.SendData(t,e,n)},e.prototype.Update=function(){if(!this.mIsDisposed&&null!=this.mNetwork){if(this.mNetwork.Update(),this.mState==X.Configuring){var e=this.mNetwork.GetConfigurationState();if(e==j.Failed){if(this.OnConfigurationFailed(this.mNetwork.GetConfigurationError()),this.mIsDisposed)return;null!=this.mNetwork&&this.mNetwork.ResetConfiguration()}else if(e==j.Successful&&(this.OnConfigurationComplete(),this.mIsDisposed))return}for(var t;null!=(t=this.mNetwork.Dequeue());)switch(t.Type){case v.NewConnection:if(this.mState==X.WaitingForIncomingCall||this.mConferenceMode&&this.mState==X.InCall){if(0==this.mConferenceMode&&this.mNetwork.StopServer(),this.mState=X.InCall,this.mConnectionInfo.AddConnection(t.ConnectionId,!0),this.TriggerCallEvent(new J(t.ConnectionId)),this.mIsDisposed)return}else if(this.mState==X.WaitingForOutgoingCall){if(this.mConnectionInfo.AddConnection(t.ConnectionId,!1),this.mState=X.InCall,this.TriggerCallEvent(new J(t.ConnectionId)),this.mIsDisposed)return}else C.LogWarning("Received incoming connection during invalid state "+this.mState);break;case v.ConnectionFailed:if(this.mState==X.WaitingForOutgoingCall){if(this.TriggerCallEvent(new Y(H.ConnectionFailed)),this.mIsDisposed)return;this.mState=X.Configured}else C.LogError("Received ConnectionFailed during "+this.mState);break;case v.Disconnected:if(this.mConnectionInfo.HasConnection(t.ConnectionId)&&(this.mConnectionInfo.RemConnection(t.ConnectionId),0==this.mConferenceMode&&0==this.mConnectionInfo.GetIds().length&&(this.mState=X.Closed),this.TriggerCallEvent(new Q(t.ConnectionId)),this.mIsDisposed))return;break;case v.ServerInitialized:if(this.mServerInactive=!1,this.mState=X.WaitingForIncomingCall,this.TriggerCallEvent(new $(t.Info)),this.mIsDisposed)return;break;case v.ServerInitFailed:if(this.mServerInactive=!0,this.mState=X.Configured,this.TriggerCallEvent(new Y(H.ListeningFailed)),this.mIsDisposed)return;break;case v.ServerClosed:if(this.mServerInactive=!0,(this.mState==X.WaitingForIncomingCall||this.mState==X.RequestingAddress)&&(this.mState=X.Configured,this.TriggerCallEvent(new Y(H.ListeningFailed,z.Unknown,"Server closed the connection while waiting for incoming calls.")),this.mIsDisposed))return;break;case v.ReliableMessageReceived:case v.UnreliableMessageReceived:var n=t.Type===v.ReliableMessageReceived;if(t.MessageData.length>=2)if(t.MessageData[0]==this.MESSAGE_TYPE_STRING){var i=this.UnpackStringMsg(t.MessageData);this.TriggerCallEvent(new Z(t.ConnectionId,i,n))}else t.MessageData[0]==this.MESSAGE_TYPE_DATA&&(i=this.UnpackDataMsg(t.MessageData),this.TriggerCallEvent(new ee(t.ConnectionId,i,n)));if(this.mIsDisposed)return}if(this.mMediaConfig.FrameUpdates){var r=this.mNetwork.TryGetFrame(I.INVALID);if(null!=r&&(this.FrameToCallEvent(I.INVALID,r),this.mIsDisposed))return}if(this.mMediaConfig.FrameUpdates)for(var o=0,a=this.mConnectionInfo.GetIds();o<a.length;o++){var s=a[o],c=new I(s),u=this.mNetwork.TryGetFrame(c);if(null!=u&&(this.FrameToCallEvent(c,u),this.mIsDisposed))return}for(var l=null;null!=(l=this.mNetwork.DequeueRtcEvent());)this.MediaEventToCallEvent(l);this.mNetwork.Flush()}},e.prototype.FrameToCallEvent=function(e,t){var n=new ne(e,t);this.TriggerCallEvent(n)},e.prototype.MediaEventToCallEvent=function(e){if(e.EventType==S.StreamAdded){var t=e,n=new te(t.ConnectionId,t.Args);this.TriggerCallEvent(n)}else C.L("Event type "+S[e.EventType]+" ignored.")},e.prototype.PendingCall=function(e){this.mPendingAddress=e,this.mPendingCallCall=!0,this.mPendingListenCall=!1},e.prototype.ProcessCall=function(e){this.mState=X.WaitingForOutgoingCall,this.mNetwork.Connect(e),this.ClearPending()},e.prototype.PendingListen=function(e){this.mPendingAddress=e,this.mPendingCallCall=!1,this.mPendingListenCall=!0},e.prototype.ProcessListen=function(e){C.Log("Listen at "+e),this.mServerInactive=!1,this.mState=X.RequestingAddress,this.mNetwork.StartServer(e),this.ClearPending()},e.prototype.DoPending=function(){this.mPendingCallCall?this.ProcessCall(this.mPendingAddress):this.mPendingListenCall&&this.ProcessListen(this.mPendingAddress),this.ClearPending()},e.prototype.ClearPending=function(){this.mPendingAddress=null,this.mPendingCallCall=null,this.mPendingListenCall=null},e.prototype.CheckDisposed=function(){if(this.mIsDisposed)throw new oe("Object is disposed. No method calls possible.")},e.prototype.EnsureConfiguration=function(){this.mState==X.Initialized&&(C.Log("Use default configuration"),this.Configure(new ie))},e.prototype.TriggerCallEvent=function(e){for(var t=0,n=this.mCallEventHandlers.slice();t<n.length;t++)(0,n[t])(this,e)},e.prototype.OnConfigurationComplete=function(){this.mIsDisposed||(this.mState=X.Configured,C.Log("Enter state CallState.Configured"),this.TriggerCallEvent(new K(H.ConfigurationComplete)),0==this.mIsDisposed&&this.DoPending())},e.prototype.OnConfigurationFailed=function(e){C.LogWarning("Configuration failed: "+e),this.mIsDisposed||(this.mState=X.Initialized,this.TriggerCallEvent(new Y(H.ConfigurationFailed,z.Unknown,e)),0==this.mIsDisposed&&this.ClearPending())},e.prototype.DisposeInternal=function(e){this.mIsDisposed||(this.mIsDisposed=!0)},e.prototype.Dispose=function(){this.DisposeInternal(!0)},e.prototype.HasAudioTrack=function(e){return!!this.mNetwork&&this.mNetwork.HasAudioTrack(e)},e.prototype.HasVideoTrack=function(e){return!!this.mNetwork&&this.mNetwork.HasVideoTrack(e)},e}(),ue=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();!function(e){e[e.Invalid=0]="Invalid",e[e.Format32bppargb=1]="Format32bppargb"}(ae||(ae={}));var le,de=function(){function e(){}return Object.defineProperty(e.prototype,"Format",{get:function(){return ae.Format32bppargb},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Buffer",{get:function(){return null},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Width",{get:function(){return-1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Height",{get:function(){return-1},enumerable:!1,configurable:!0}),e.prototype.ToTexture=function(e,t){return!1},e}(),pe=function(e){function t(t,n,i){var r=e.call(this)||this;return r.mBuffer=null,r.mBuffer=t,r.mWidth=n,r.mHeight=i,r}return ue(t,e),Object.defineProperty(t.prototype,"Buffer",{get:function(){return this.mBuffer},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Width",{get:function(){return this.mWidth},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Height",{get:function(){return this.mHeight},enumerable:!1,configurable:!0}),t}(de),he=function(e){function t(t){var n=e.call(this)||this;return n.mFrameGenerator=t,n}return ue(t,e),Object.defineProperty(t.prototype,"FrameGenerator",{get:function(){return this.mFrameGenerator},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Buffer",{get:function(){return this.GenerateFrame(),null==this.mRawFrame?null:this.mRawFrame.Buffer},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Width",{get:function(){return null==this.mRawFrame?this.mFrameGenerator.VideoElement.videoWidth:this.mRawFrame.Width},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"Height",{get:function(){return null==this.mRawFrame?this.mFrameGenerator.VideoElement.videoHeight:this.mRawFrame.Height},enumerable:!1,configurable:!0}),t.prototype.ToTexture=function(e,t){return e.bindTexture(e.TEXTURE_2D,t),e.texSubImage2D(e.TEXTURE_2D,0,0,0,e.RGB,e.UNSIGNED_BYTE,this.mFrameGenerator.VideoElement),!0},t.prototype.GenerateFrame=function(){if(null==this.mRawFrame)try{this.mRawFrame=this.mFrameGenerator.CreateFrame()}catch(e){this.mRawFrame=null,C.LogWarning("frame skipped in GenerateFrame due to exception: "+JSON.stringify(e))}},t}(de),fe=function(){function e(){}return e.HasCompleted=function(){return 0==e.sBlockedStreams.size},e.RequestAutoplayFix=function(t){e.sBlockedStreams.add(t),null!==e.onautoplayblocked&&e.onautoplayblocked()},e.Resolve=function(){C.L("ResolveAutoplay. Trying to restart video / turn on audio after user interaction ");for(var t=e.sBlockedStreams,n=0,i=Array.from(t);n<i.length;n++){var r=i[n];try{r.ResolveAutoplay()}catch(e){C.LE("AutoplayResolver.Resolve failed: "+e),this.Remove(r)}}},e.RemoveCompleted=function(t){e.sBlockedStreams.delete(t)},e.Remove=function(t){e.sBlockedStreams.delete(t)},e.onautoplayblocked=function(){C.LW("Playback of a media stream was blocked. Set an event handler to AutoplayResolver.onautoplayblocked to allow the user to start playback.")},e.sBlockedStreams=new Set,e}(),me=function(){function e(){this.panningControl=null,this.audioCtx=new AudioContext;var e=this.audioCtx;this.audioCtx.onstatechange=function(){C.L("AudioContext state changed to ",e.state)},this.gainNode=new GainNode(this.audioCtx),this.panNode=new StereoPannerNode(this.audioCtx),console.warn("AudioProcessor constructed in state ",this.audioCtx.state),"suspended"==this.audioCtx.state&&(C.L("Audio context was created as suspended."),this.RequestAutoplayFix())}return e.prototype.Dispose=function(){this.panningControl&&this.panningControl.remove(),this.audioCtx.close(),this.audioCtx=null},e.prototype.CreatePanningControl=function(){var e=document.createElement("input");return e.className="panning-control",e.type="range",e.min="-1",e.max="1",e.step="0.1",e.value="0",document.body.appendChild(e),e},e.prototype.SetVolumePan=function(e,t){this.gainNode.gain.setValueAtTime(e,this.audioCtx.currentTime),this.panNode.pan.value=Number(t)},e.prototype.RequestAutoplayFix=function(){fe.RequestAutoplayFix(this)},e.prototype.ResolveAutoplay=function(){var e=this;this.audioCtx?(C.L("Attempting to resume audio context."),this.audioCtx.resume().then((function(){C.L("AudioContext resumed successfully."),fe.RemoveCompleted(e)})).catch((function(e){C.LW("AudioContext failed to resume:",e)}))):C.L("ResolveAutoplay called after disposal.")},e.prototype.InjectPanner=function(t){if(this.source=this.audioCtx.createMediaStreamSource(t),this.source.connect(this.gainNode),this.gainNode.connect(this.panNode),e.AUDIO_PROCESSING_CHROME_WORKAROUND)return this.panNode.connect(this.audioCtx.destination),t;var n=this.audioCtx.createMediaStreamDestination();this.panNode.connect(n);var i=new MediaStream;return i.addTrack(n.stream.getAudioTracks()[0]),t.getVideoTracks().length>0&&i.addTrack(t.getVideoTracks()[0]),i},e.AUDIO_PROCESSING_CHROME_WORKAROUND=!0,e}();!function(e){e.DEFAULT_FALLBACK="DEFAULT_FALLBACK",e.TRACK="TRACK",e.EXACT="EXACT"}(le||(le={}));var ge,ve=function(){function e(t,n){void 0===n&&(n=new l("")),this.mCurrentFrame=null,this.mInstanceId=0,this.mIdentity="Stream",this.mCanvasElement=null,this.mIsActive=!1,this.mAudioProcessor=null,this.mMsPerFrame=1/e.DEFAULT_FRAMERATE*1e3,this.mFrameEventMethod=le.DEFAULT_FALLBACK,this.mDefaultVolume=.5,this.mLastFrameTime=0,this.mNextFrameTime=0,this.mLastFrameNumber=0,this.mHasVideo=!1,this.InternalStreamAdded=null,this.mStream=new MediaStream,this.mLocal=t,this.mInstanceId=e.sNextInstanceId,e.sNextInstanceId++,this.log=n.CreateSub(this.mIdentity+this.mInstanceId),this.mMsPerFrame=1/e.DEFAULT_FRAMERATE*1e3,this.mFrameEventMethod=le.DEFAULT_FALLBACK,this.SetupElements()}return Object.defineProperty(e.prototype,"Stream",{get:function(){return this.mStream},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VideoElement",{get:function(){return this.mVideoElement},enumerable:!1,configurable:!0}),e.prototype.ResolveAutoplay=function(){var e=this;this.mVideoElement?this.mVideoElement.paused?(this.log.L("Attempting to play HTMLVideoElement"),this.mVideoElement.play().then((function(){fe.RemoveCompleted(e),e.log.L("Playing video element was successful")})).catch((function(t){e.log.LE("Playing video element failed with error: "+t)}))):this.log.L("ResolveAutoplay called but HTMLVideoElement was already playing"):this.log.L("ResolveAutoplay after disposal")},e.prototype.UpdateTrack=function(e){var t=this;this.mStream.getTracks().forEach((function(n){n.kind==e.kind&&(t.log.L("Replacing track of type "+n.kind),t.mStream.removeTrack(n))})),this.mStream.addTrack(e),this.UpdateAudioProcessing(),this.UpdateVideoProcessing()},e.prototype.RemoveTrack=function(e){},e.prototype.ResetObject=function(){this.mStream.getAudioTracks().length>0&&0==this.mLocal?(null==this.mAudioProcessor&&(this.mAudioProcessor=new me),this.mVideoElement.srcObject=this.mAudioProcessor.InjectPanner(this.mStream),me.AUDIO_PROCESSING_CHROME_WORKAROUND&&(this.mVideoElement.muted=!0)):this.mVideoElement.srcObject=this.mStream},e.prototype.DetermineFrameEventMethod=function(){if(this.mVideoElement){if(this.mStream.getVideoTracks().length>0){var t=this.mStream.getVideoTracks()[0].getSettings().frameRate;t&&(e.VERBOSE&&this.log.LV("Track FPS: "+t),this.mMsPerFrame=1/t*1e3,this.mFrameEventMethod=le.TRACK)}-1!=this.GetFrameNumber()&&(e.VERBOSE&&this.log.LV("Get frame available."),this.mFrameEventMethod=le.EXACT),this.mFrameEventMethod===le.DEFAULT_FALLBACK&&this.log.LW("Framerate unknown for stream "+this.mInstanceId+". Using default framerate of "+e.DEFAULT_FRAMERATE)}},e.prototype.UpdateAudioProcessing=function(){this.ResetObject()},e.prototype.UpdateVideoProcessing=function(){if(this.mVideoElement){if(0==this.mStream.getVideoTracks().length)return this.mHasVideo=!1,void this.DestroyCanvas();this.mHasVideo=!0,this.DetermineFrameEventMethod(),this.mCanvasElement||this.SetupCanvas()}},e.prototype.TriggerAutoplayBlockled=function(){fe.RequestAutoplayFix(this)},e.prototype.TryPlay=function(){var e=this,t=this.mVideoElement.play();this.mDefaultVolume=this.mVideoElement.volume,t.then((function(){})).catch((function(t){e.log.LW("Media playback failed. This might be caused by the browser blocking autoplay."),console.error(t),e.TriggerAutoplayBlockled()}))},e.prototype.SetupElements=function(){var e=this,t="remote";this.mLocal&&(t="local"),this.mVideoElement=this.SetupVideoElement(),this.log.L("video element created for "+t+" video tracks: "+this.mStream.getVideoTracks().length+" audio:"+this.mStream.getAudioTracks().length),this.mVideoElement.onloadedmetadata=function(n){if(null!=e.mVideoElement){e.TryPlay(),null!=e.InternalStreamAdded&&e.InternalStreamAdded(e),e.UpdateVideoProcessing();var i="onloadedmetadata: "+t+" audio: "+(e.mStream.getAudioTracks().length>0)+" Resolution: "+e.mVideoElement.videoWidth+"x"+e.mVideoElement.videoHeight+" fps method: "+e.mFrameEventMethod+" "+Math.round(1e3/e.mMsPerFrame);e.log.L(i),e.mIsActive=!0}else e.log.L("Stream destroyed by the time onloadedmetadata triggered. Skip event.")};try{this.ResetObject()}catch(e){this.mVideoElement.src=window.URL.createObjectURL(this.mStream)}},e.prototype.GetFrameNumber=function(){return this.mVideoElement&&this.mVideoElement.webkitDecodedFrameCount?this.mVideoElement.webkitDecodedFrameCount:-1},e.prototype.TryGetFrame=function(){var e=this.mCurrentFrame;return this.mCurrentFrame=null,e},e.prototype.SetMute=function(e){if(this.mVideoElement){if(this.mAudioProcessor&&me.AUDIO_PROCESSING_CHROME_WORKAROUND)return void this.log.LW("SetMute ignored due to audio processing");this.mVideoElement.muted=e}},e.prototype.PeekFrame=function(){return this.mCurrentFrame},e.prototype.EnsureLatestFrame=function(){return!!this.HasNewerFrame()&&(this.GenerateFrame(),!0)},e.prototype.HasNewerFrame=function(){if(this.mIsActive&&this.mHasVideo&&null!=this.mCanvasElement&&this.mVideoElement.videoWidth>0&&this.mVideoElement.videoHeight>0)if(this.mLastFrameNumber>0){if(this.mFrameEventMethod=le.EXACT,this.GetFrameNumber()>this.mLastFrameNumber)return!0}else{var e=(new Date).getTime();if(this.mNextFrameTime<=e)return!0}return!1},e.prototype.Update=function(){this.EnsureLatestFrame()},e.prototype.DestroyCanvas=function(){null!=this.mCanvasElement&&null!=this.mCanvasElement.parentElement&&this.mCanvasElement.parentElement.removeChild(this.mCanvasElement),this.mCanvasElement=null},e.prototype.DestroyVideoElement=function(){null!=this.mVideoElement&&null!=this.mVideoElement.parentElement&&this.mVideoElement.parentElement.removeChild(this.mVideoElement),this.mVideoElement=null},e.prototype.Dispose=function(){this.log.L("Disposing stream "+this.mInstanceId),this.mIsActive=!1,fe.Remove(this),this.DestroyCanvas(),null!=this.mAudioProcessor&&this.mAudioProcessor.Dispose(),this.DestroyVideoElement(),this.mStream.getTracks().forEach((function(e){e.stop()})),this.mStream=null},e.prototype.CreateFrame=function(){if(this.mVideoElement.videoWidth<=0||this.mVideoElement.videoHeight<=0)return this.log.LW("Frame skipped. videoWidth / videoHeight were 0."),null;this.mCanvasElement.width=this.mVideoElement.videoWidth,this.mCanvasElement.height=this.mVideoElement.videoHeight;var e=this.mCanvasElement.getContext("2d");e.drawImage(this.mVideoElement,0,0);try{var t=e.getImageData(0,0,this.mCanvasElement.width,this.mCanvasElement.height).data,n=new Uint8Array(t.buffer);return new pe(n,this.mCanvasElement.width,this.mCanvasElement.height)}catch(e){(n=new Uint8Array(this.mCanvasElement.width*this.mCanvasElement.height*4)).fill(255,0,n.length-1);var i=new pe(n,this.mCanvasElement.width,this.mCanvasElement.height);return this.log.LW("Firefox workaround: Refused access to the remote video buffer. Retrying next frame..."),this.DestroyCanvas(),this.SetupCanvas(),i}},e.prototype.GenerateFrame=function(){this.mLastFrameNumber=this.GetFrameNumber();var e=(new Date).getTime(),t=e-this.mNextFrameTime,n=this.mMsPerFrame-t;n=Math.min(this.mMsPerFrame,Math.max(1,n)),this.mLastFrameTime=e,this.mNextFrameTime=e+n,this.mCurrentFrame=new he(this)},e.prototype.SetupVideoElement=function(){var t=document.createElement("video");return t.width=320,t.height=240,0==this.mLocal&&(t.controls=!0),t.setAttribute("playsinline",""),t.id="awrtc_mediastream_video_"+this.mInstanceId,e.DEBUG_SHOW_ELEMENTS&&document.body.appendChild(t),t},e.prototype.SetupCanvas=function(){if(null!=this.mVideoElement){var t=document.createElement("canvas");t.id="awrtc_mediastream_canvas_"+this.mInstanceId,e.DEBUG_SHOW_ELEMENTS&&document.body.appendChild(t),this.mCanvasElement=t}else this.log.LE("SetupCanvas was called without HTMLVideoElement")},e.prototype.SetVolume=function(e){null!=this.mVideoElement&&(e<0&&(e=0),e>1&&(e=1),null!=this.mAudioProcessor?this.mAudioProcessor.SetVolumePan(e,0):this.mVideoElement.volume=e)},e.prototype.SetVolumePan=function(e,t){null!=this.mVideoElement&&(e<0&&(e=0),e>1&&(e=1),null!=this.mAudioProcessor?this.mAudioProcessor.SetVolumePan(e,t):(this.log.LW("setVolumePan ignored the pan value. Audio processing is not available."),this.mVideoElement.volume=e))},e.prototype.HasAudioTrack=function(){return null!=this.mStream&&null!=this.mStream.getAudioTracks()&&this.mStream.getAudioTracks().length>0},e.prototype.HasVideoTrack=function(){return null!=this.mStream&&null!=this.mStream.getVideoTracks()&&this.mStream.getVideoTracks().length>0},e.prototype.GetAudioTrack=function(){return this.mStream.getAudioTracks().length>0?this.mStream.getAudioTracks()[0]:null},e.prototype.GetVideoTrack=function(){return this.mStream.getVideoTracks().length>0?this.mStream.getVideoTracks()[0]:null},e.DEBUG_SHOW_ELEMENTS=!1,e.sNextInstanceId=1,e.VERBOSE=!1,e.DEFAULT_FRAMERATE=30,e}(),ye=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),Ce=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function a(e){try{c(i.next(e))}catch(e){o(e)}}function s(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((i=i.apply(e,t||[])).next())}))},Se=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(n=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],i=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},be=function(e){function t(t,n,i,r){var o=e.call(this,t,n,r)||this;return o.DEBUG_QUALITY=!1,o.DEBUG_preferredCodec=null,o.DEBUG_IosWorkaround=!1,o.mRemoteStream=null,o.mAudioSender=null,o.mVideoSender=null,o.InternalStreamAdded=null,o.mMediaConfig=i,o}return ye(t,e),t.prototype.OnSetup=function(){var t=this;e.prototype.OnSetup.call(this),this.mPeer.ontrack=function(e){t.OnTrack(e)}},t.prototype.getTransceiverByKind=function(e){for(var t=0,n=this.mPeer.getTransceivers();t<n.length;t++){var i=n[t];if(null!==i.receiver&&null!==i.receiver.track&&i.receiver.track.kind==e||null!==i.sender&&null!==i.sender.track&&i.sender.track.kind==e)return i}return null},t.ReorderList=function(e,t){return e.sort((function(e,n){var i=t.findIndex((function(t){return e.mimeType.includes(t)})),r=t.findIndex((function(e){return n.mimeType.includes(e)}));return(-1===i?t.length:i)-(-1===r?t.length:r)})),e},t.prototype.SetVideoParams=function(e){return Ce(this,void 0,void 0,(function(){var t,n;return Se(this,(function(i){switch(i.label){case 0:if(!e||!e.track)return[2];if(!this.mMediaConfig.VideoBitrateKbits)return[3,4];i.label=1;case 1:return i.trys.push([1,3,,4]),(t=e.getParameters())||this.log.LW("Unable to call VideoBitrateKbits. getParameters returned null"),t.encodings?!1===Array.isArray(t.encodings)?(this.log.LW("encodings was not an array. Setting VideoBitrateKbits ignored"),[2]):0===t.encodings.length?(this.log.LW("encodings was empty. Setting VideoBitrateKbits ignored"),[2]):(t.encodings[0].maxBitrate=1e3*this.mMediaConfig.VideoBitrateKbits,[4,e.setParameters(t)]):(this.log.LW("encodings was undefined. VideoBitrateKbits ignored."),[2]);case 2:return i.sent(),[3,4];case 3:return n=i.sent(),this.log.LE("Setting VideoBitrateKbits failed with exception:"),this.log.LE(n),[3,4];case 4:return[2]}}))}))},t.prototype.setVideoTransceiver=function(e){if(this.mMediaConfig&&this.mMediaConfig.VideoCodecs&&this.mMediaConfig.VideoCodecs.length>0){var n=RTCRtpReceiver.getCapabilities("video").codecs,i=t.ReorderList(n,this.mMediaConfig.VideoCodecs);e.setCodecPreferences?e.setCodecPreferences(i):this.log.LW("Unable to call setCodecPreferences. Default codecs will be used.")}},t.prototype.CreateOfferImpl=function(){return Ce(this,void 0,void 0,(function(){var e,t,n,i;return Se(this,(function(r){switch(r.label){case 0:return this.SINGLE_NEGOTIATION?(null==this.getTransceiverByKind("audio")&&(this.log.L("Add transceiver for audio"),e=this.mPeer.addTransceiver("audio",{direction:"sendrecv"}),this.mAudioSender=e.sender),null!=this.getTransceiverByKind("video")?[3,2]:(this.log.L("Add transceiver for video"),t=this.mPeer.addTransceiver("video",{direction:"sendrecv"}),this.setVideoTransceiver(t),this.mVideoSender=t.sender,[4,this.SetVideoParams(this.mVideoSender)])):[3,3];case 1:r.sent(),r.label=2;case 2:return[2,this.mPeer.createOffer()];case 3:return null==(e=this.getTransceiverByKind("audio"))&&(this.log.L("Add transceiver for audio"),e=this.mPeer.addTransceiver("audio",{direction:"recvonly"}),this.mAudioSender=e.sender),null==(t=this.getTransceiverByKind("video"))&&(this.log.L("Add transceiver for video"),t=this.mPeer.addTransceiver("video",{direction:"recvonly"})),this.setVideoTransceiver(t),this.mVideoSender=t.sender,[4,this.SetVideoParams(this.mVideoSender)];case 4:return r.sent(),[4,this.mPeer.createOffer()];case 5:return[2,i=r.sent()];case 6:return null!=(t=this.getTransceiverByKind("video"))?this.setVideoTransceiver(t):console.warn("No transceiver found. "),n={offerToReceiveAudio:!0,offerToReceiveVideo:!0},[4,this.mPeer.createOffer(n)];case 7:return i=r.sent(),null===t?[3,9]:(this.mVideoSender=t.sender,[4,this.SetVideoParams(this.mVideoSender)]);case 8:r.sent(),r.label=9;case 9:return[2,i]}}))}))},t.prototype.CreateAnswerImpl=function(){return Ce(this,void 0,void 0,(function(){var e,t;return Se(this,(function(n){switch(n.label){case 0:return this.SINGLE_NEGOTIATION?(null!==(e=this.getTransceiverByKind("audio"))&&(e.direction="sendrecv",this.mAudioSender=e.sender),null===(t=this.getTransceiverByKind("video"))?[3,2]:(t.direction="sendrecv",this.setVideoTransceiver(t),this.mVideoSender=t.sender,[4,this.SetVideoParams(this.mVideoSender)])):[3,3];case 1:n.sent(),n.label=2;case 2:return[3,5];case 3:return null===(t=this.getTransceiverByKind("video"))?[3,5]:(this.setVideoTransceiver(t),this.mVideoSender=t.sender,[4,this.SetVideoParams(this.mVideoSender)]);case 4:n.sent(),n.label=5;case 5:return[2,this.mPeer.createAnswer()]}}))}))},t.prototype.OnCleanup=function(){e.prototype.OnCleanup.call(this),null!=this.mRemoteStream&&(this.mRemoteStream.Dispose(),this.mRemoteStream=null)},t.prototype.OnTrack=function(e){this.log.L("ontrack: "+e.track.kind),this.UpdateRemoteStream(e.track)},t.prototype.UpdateRemoteStream=function(e){var t=this;null==this.mRemoteStream&&(this.mRemoteStream=new ve(!1,this.log),this.mRemoteStream.InternalStreamAdded=function(e){null!=t.InternalStreamAdded&&t.InternalStreamAdded(t,e)}),!1===this.SINGLE_NEGOTIATION?this.mRemoteStream.UpdateTrack(e):(this.log.L("delaying track of type: "+e.kind+" muted?"+e.muted+" enabled?"+e.enabled),e.onunmute=function(){t.log.L("adding unmuted track of type: "+e.kind),t.mRemoteStream.UpdateTrack(e)},e.onmute=function(){t.log.L("removing muted track of type: "+e.kind),t.mRemoteStream.Stream.removeTrack(e),t.mRemoteStream.ResetObject()})},t.prototype.TryGetRemoteFrame=function(){return null==this.mRemoteStream?null:this.mRemoteStream.TryGetFrame()},t.prototype.PeekFrame=function(){return null==this.mRemoteStream?null:this.mRemoteStream.PeekFrame()},t.prototype.SetLocalStream=function(e,t){return Ce(this,void 0,void 0,(function(){var n,i,r,o,a,s,c,u,l=this;return Se(this,(function(d){switch(d.label){case 0:if(this.mMediaConfig=t,n=null,i=null,null!==e&&(n=e.GetAudioTrack(),i=e.GetVideoTrack(),this.mMediaConfig.VideoContentHint&&(i.contentHint=this.mMediaConfig.VideoContentHint)),null==n)return[3,7];if(null==this.mAudioSender)return[3,5];d.label=1;case 1:return d.trys.push([1,3,,4]),[4,this.mAudioSender.replaceTrack(n)];case 2:return d.sent(),(r=this.getTransceiverByKind("audio"))?"sendrecv"!=r.currentDirection&&(r.direction="sendrecv"):this.log.LW("Unable to find the audio transceiver to attach a new track. This indicates the peer is incorrectly configured."),[3,4];case 3:return o=d.sent(),this.log.LE("Error during replaceTrack: "+o),[3,4];case 4:return[3,6];case 5:this.log.L("addinging track of type "+n.kind),this.mAudioSender=this.mPeer.addTrack(n,e.Stream),d.label=6;case 6:return[3,9];case 7:return null==this.mAudioSender||null===this.mAudioSender.track?[3,9]:(this.log.L("setting track of type audio to null"),[4,this.mAudioSender.replaceTrack(null)]);case 8:d.sent(),d.label=9;case 9:if(null==i)return[3,16];if(null==this.mVideoSender)return[3,14];d.label=10;case 10:return d.trys.push([10,12,,13]),[4,this.mVideoSender.replaceTrack(i)];case 11:return d.sent(),(a=this.getTransceiverByKind("video"))?"sendrecv"!=a.currentDirection&&(a.direction="sendrecv"):C.LW("Unable to find the video transceiver to attach a new track. This indicates the peer is incorrectly configured."),[3,13];case 12:return s=d.sent(),this.log.LE("Error during replaceTrack: "+s),[3,13];case 13:return[3,15];case 14:this.log.L("addinging track of type "+i.kind),this.mVideoSender=this.mPeer.addTrack(i,e.Stream),this.DEBUG_QUALITY&&(c="unknown",u="unknown",setInterval((function(){return Ce(l,void 0,void 0,(function(){var e,t=this;return Se(this,(function(n){switch(n.label){case 0:return[4,this.mPeer.getStats(null)];case 1:return e=n.sent(),console.debug("all:"),console.debug(Array.from(e.values())),e.forEach((function(e){"video"===e.kind&&"outbound-rtp"===e.type?(console.log(e.type+" "+e.frameWidth+"x"+e.frameHeight+" FPS:"+e.framesPerSecond+" qual:"+JSON.stringify(e.qualityLimitationDurations)+" codec:"+c+" offer: "+t.mIsOfferer),u=e.codecId):"codec"===e.type&&e.id==u&&(c=e.mimeType)})),[2]}}))}))}),1e3)),d.label=15;case 15:return[3,18];case 16:return null==this.mVideoSender||null===this.mVideoSender.track?[3,18]:(this.log.L("setting track of type video to null"),[4,this.mVideoSender.replaceTrack(null)]);case 17:d.sent(),d.label=18;case 18:return this.DEBUG&&console.warn("SetLocalStream completed: ",this.mPeer.getTransceivers()),[2]}}))}))},t.prototype.Update=function(){e.prototype.Update.call(this),null!=this.mRemoteStream&&this.mRemoteStream.Update()},t.prototype.SetVolume=function(e){null!=this.mRemoteStream&&this.mRemoteStream.SetVolume(e)},t.prototype.SetVolumePan=function(e,t){null!=this.mRemoteStream&&this.mRemoteStream.SetVolumePan(e,t)},t.prototype.HasAudioTrack=function(){return null!=this.mRemoteStream&&this.mRemoteStream.HasAudioTrack()},t.prototype.HasVideoTrack=function(){return null!=this.mRemoteStream&&this.mRemoteStream.HasVideoTrack()},t.prototype.EditCodecs=function(e){var t,n,i;this.log.LW("sdp munging: prioritizing codec "+this.DEBUG_preferredCodec);for(var r=0;r<e.length;r++)if((a=e[r]).startsWith("m=video")){i=(n=a.split(" ")).slice(3,n.length),t=r;break}var o=[];for(r=t+1;r<e.length;r++){var a;if((a=e[r]).startsWith("a=rtpmap:")){var s=a.substr(9).split(" "),c=s[0],u=s[1].split("/")[0];i.includes(c)&&(u===this.DEBUG_preferredCodec?o.unshift(c):o.push(c))}}var l=n[0]+" "+n[1]+" "+n[2];o.forEach((function(e){l=l+" "+e})),e[t]=l},t.prototype.EditProfileLevel=function(e){console.warn("sdp munging: replacing h264 profile-level with 2a");for(var t=0;t<e.length;t++){var n=e[t];if(n.startsWith("a=fmtp:")){for(var i=n.split(";"),r=!1,o=0;o<i.length;o++)if(i[o].startsWith("profile-level-id=")){i[o]=i[o].substr(0,21)+"2a",r=!0;break}r&&(e[t]=i.join(";"))}}},t.prototype.FilterFeatures=function(e){return e.filter((function(e,t){var n=!e.includes("goog-remb");return 0==n&&console.log("dropping "+e),n}))},t.prototype.ProcessLocalSdp=function(e){if(!1===t.MUNGE_SDP)return e;console.warn("sdp munging active");var n,i=e.sdp.split("\r\n");if(this.DEBUG_preferredCodec&&this.EditCodecs(i),this.DEBUG_IosWorkaround)for(var r=0;r<i.length;r++)i[r]=i[r].replace("42e01f","42e034");return n=i.join("\r\n"),{type:e.type,sdp:n}},t.prototype.ProcessRemoteSdp=function(e){if(!1===t.MUNGE_SDP)return e;console.warn("sdp munging active");var n,i=e.sdp.split("\r\n");if(this.DEBUG_preferredCodec&&this.EditCodecs(i),this.DEBUG_IosWorkaround)for(var r=0;r<i.length;r++)i[r]=i[r].replace("42e034","42e01f");return n=i.join("\r\n"),{type:e.type,sdp:n}},t.MUNGE_SDP=!1,t}(M),Ie=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),Re=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function a(e){try{c(i.next(e))}catch(e){o(e)}}function s(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((i=i.apply(e,t||[])).next())}))},we=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(n=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],i=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},Ee=function(){this.Id=null,this.Name=null},Te=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.fallbackLabel=null,t.isLabelGuessed=!0,t}return Ie(t,e),t}(Ee),Pe=function(){function e(){this.mDeviceInfo={},this.mDeviceCounter=1}return Object.defineProperty(e.prototype,"DeviceDict",{get:function(){return this.mDeviceInfo},enumerable:!1,configurable:!0}),e.prototype.GetDeviceLabels=function(){return Object.values(this.mDeviceInfo).map((function(e){return e.Name}))},e.prototype.GetDeviceList=function(){return Object.values(this.mDeviceInfo)},e.prototype.UpdateDeviceList=function(e){for(var t={},n=0,i=e;n<i.length;n++){var r=i[n],o=new Te;o.Id=r.deviceId,o.kind=r.kind;var a=null;o.Id in this.mDeviceInfo&&(a=this.mDeviceInfo[o.Id]),null!=a?o.fallbackLabel=a.fallbackLabel:(o.fallbackLabel=r.kind+" "+this.mDeviceCounter,this.mDeviceCounter++),null!=a&&0==a.isLabelGuessed?(o.Name=a.Name,o.isLabelGuessed=!1):r.label?(o.Name=r.label,o.isLabelGuessed=!1):(o.Name=o.fallbackLabel,o.isLabelGuessed=!0),t[o.Id]=o}this.mDeviceInfo=t},e}(),De=function(){function e(){}return Object.defineProperty(e,"LastUpdate",{get:function(){return e.sLastUpdate},enumerable:!1,configurable:!0}),Object.defineProperty(e,"HasInfo",{get:function(){return e.sLastUpdate>0},enumerable:!1,configurable:!0}),Object.defineProperty(e,"IsPending",{get:function(){return e.sIsPending},enumerable:!1,configurable:!0}),Object.defineProperty(e,"LastError",{get:function(){return this.sLastError},enumerable:!1,configurable:!0}),e.AddOnChangedHandler=function(t){e.sUpdateEvents.push(t)},e.RemOnChangedHandler=function(t){var n=e.sUpdateEvents.indexOf(t);n>=0?e.sUpdateEvents.splice(n,1):C.LW("Tried to remove an unknown event handler in DeviceApi.RemOnChangedHandler")},e.TriggerChangedEvent=function(){for(var t=0,n=e.sUpdateEvents;t<n.length;t++){var i=n[t];try{i()}catch(e){C.LE("Error in DeviceApi user event handler: "+e),console.error(e)}}},Object.defineProperty(e,"VideoDevices",{get:function(){return e.sVideoDeviceInfo.DeviceDict},enumerable:!1,configurable:!0}),e.GetVideoDevices=function(){return e.sVideoDeviceInfo.GetDeviceLabels()},e.GetVideoInputDevices=function(){return e.sVideoDeviceInfo.GetDeviceList()},e.GetAudioInputDevices=function(){return e.sAudioInputDeviceInfo.GetDeviceList()},e.Reset=function(){e.sUpdateEvents=[],e.sLastUpdate=0,e.sVideoDeviceInfo=new Pe,e.sAudioInputDeviceInfo=new Pe,e.sAccessStream=null,e.sLastError=null,e.sIsPending=!1},e.Update=function(){e.sLastError=null,e.IsApiAvailable()?(e.sIsPending=!0,navigator.mediaDevices.enumerateDevices().then(e.InternalOnEnum).catch(e.InternalOnErrorCatch)):e.InternalOnErrorString(e.ENUM_FAILED)},e.UpdateAsync=function(){return Re(this,void 0,void 0,(function(){return we(this,(function(t){return[2,new Promise((function(t,n){e.sLastError=null,0==e.IsApiAvailable()&&(e.InternalOnErrorString(e.ENUM_FAILED),n(e.ENUM_FAILED)),t()})).then((function(){return e.sIsPending=!0,navigator.mediaDevices.enumerateDevices().then(e.InternalOnEnum).catch(e.InternalOnErrorCatch)}))]}))}))},e.IsApiAvailable=function(){return!!(navigator&&navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)},e.RequestUpdate=function(){e.sLastError=null,e.IsApiAvailable()?(e.sIsPending=!0,navigator.mediaDevices.getUserMedia({video:!0}).then(e.InternalOnStream).catch(e.InternalOnErrorCatch)):e.InternalOnErrorString("Can't access mediaDevices or enumerateDevices")},e.GetDeviceId=function(t){var n=e.VideoDevices;for(var i in n){var r=n[i];if(r.Name==t||r.fallbackLabel==t||r.Id==t)return r.Id}return null},e.IsUserMediaAvailable=function(){return!(!navigator||!navigator.mediaDevices)},e.ToConstraints=function(t){var n={audio:t.Audio};t.Audio&&t.AudioInputDevice&&(n.audio={deviceId:{exact:t.AudioInputDevice}});var i={},r={},o={},a={};-1!=t.MinWidth&&(i.min=t.MinWidth),-1!=t.MaxWidth&&(i.max=t.MaxWidth),-1!=t.IdealWidth&&(i.ideal=t.IdealWidth),-1!=t.MinHeight&&(r.min=t.MinHeight),-1!=t.MaxHeight&&(r.max=t.MaxHeight),-1!=t.IdealHeight&&(r.ideal=t.IdealHeight),-1!=t.MinFps&&(a.min=t.MinFps),-1!=t.MaxFps&&(a.max=t.MaxFps),-1!=t.IdealFps&&(a.ideal=t.IdealFps);var s=null;if(t.Video&&t.VideoDeviceName&&""!==t.VideoDeviceName)if(s=e.GetDeviceId(t.VideoDeviceName),C.L("using device "+t.VideoDeviceName),""===s)s=null;else if(null===s)throw C.LE("Failed to find deviceId for label "+t.VideoDeviceName),new Error("Unknown device "+t.VideoDeviceName);return 0==t.Video?o=!1:(Object.keys(i).length>0&&(o.width=i),Object.keys(r).length>0&&(o.height=r),Object.keys(a).length>0&&(o.frameRate=a),null!==s&&(o.deviceId={exact:s}),0==Object.keys(o).length&&(o=!0)),n.video=o,n},e.getBrowserUserMedia=function(t){return Re(this,void 0,void 0,(function(){var n;return we(this,(function(i){switch(i.label){case 0:return[4,navigator.mediaDevices.getUserMedia(t)];case 1:return n=i.sent(),e.Update(),[2,n]}}))}))},e.getAssetUserMedia=function(t){return Re(this,void 0,void 0,(function(){var n;return we(this,(function(i){switch(i.label){case 0:return n=e.ToConstraints(t),[4,e.getBrowserUserMedia(n)];case 1:return[2,i.sent()]}}))}))},e.ENUM_FAILED="Can't access mediaDevices or enumerateDevices",e.sVideoDeviceInfo=new Pe,e.sAudioInputDeviceInfo=new Pe,e.sAccessStream=null,e.sLastUpdate=0,e.sIsPending=!1,e.sLastError=null,e.sUpdateEvents=[],e.InternalOnEnum=function(t){e.sIsPending=!1,e.sLastUpdate=(new Date).getTime();var n=t.filter((function(e){return"videoinput"==e.kind})),i=t.filter((function(e){return"audioinput"==e.kind}));if(e.sVideoDeviceInfo.UpdateDeviceList(n),e.sAudioInputDeviceInfo.UpdateDeviceList(i),e.sAccessStream){for(var r=e.sAccessStream.getTracks(),o=0;o<r.length;o++)r[o].stop();e.sAccessStream=null}e.TriggerChangedEvent()},e.InternalOnErrorCatch=function(t){e.InternalOnErrorString(JSON.stringify(t))},e.InternalOnErrorString=function(t){e.sIsPending=!1,e.sLastError=t,C.LE(t),e.TriggerChangedEvent()},e.InternalOnStream=function(t){e.sAccessStream=t,e.Update()},e}(),Ae=function(){function e(){this.canvasDevices={}}return e.prototype.AddCanvasDevice=function(e,t,n,i,r){var o=ke.CreateExternal(e,r);n==e.width&&i==e.height||o.initScaling(n,i),this.canvasDevices[t]=o},e.prototype.HasDevice=function(e){return e in this.canvasDevices},e.prototype.GetDeviceNames=function(){return Object.keys(this.canvasDevices)},e.prototype.GetStream=function(e){return this.HasDevice(e)?this.canvasDevices[e].captureStream():null},e.prototype.AddDevice=function(e,t,n,i){var r=ke.CreateInternal(t,n,i);this.canvasDevices[e]=r},e.prototype.RemCanvasDevice=function(e){this.canvasDevices[e]&&delete this.canvasDevices[e]},e.prototype.RemoveDevice=function(e){this.RemCanvasDevice(e)},e.prototype.UpdateFrame=function(e,t,n,i,r,o,a){if(void 0===r&&(r=ge.ARGB),void 0===o&&(o=0),void 0===a&&(a=!0),this.HasDevice(e)){var s=this.canvasDevices[e];if(s.IsExternal()||null==t)s.UpdateFrame();else{var c=new ImageData(t,n,i);s.UpdateFrame(c)}return!0}return!1},e}(),ke=function(){function e(e,t,n){this.external_canvas=!1,this.scaling_canvas=null,this.is_capturing=!1,this.canvas=e,this.external_canvas=t,this.fps=n}return e.prototype.getStreamingCanvas=function(){return null==this.scaling_canvas?this.canvas:this.scaling_canvas},e.prototype.captureStream=function(){return 0==this.is_capturing&&this.scaling_canvas&&this.startScaling(),this.is_capturing=!0,this.fps&&this.fps>0?this.getStreamingCanvas().captureStream(this.fps):this.getStreamingCanvas().captureStream()},e.CreateInternal=function(t,n,i){var r=e.MakeCanvas(t,n);return new e(r,!1,i)},e.CreateExternal=function(t,n){return new e(t,!0,n)},e.prototype.initScaling=function(e,t){this.scaling_canvas=document.createElement("canvas"),this.scaling_canvas.width=e,this.scaling_canvas.height=t,this.scaling_canvas.getContext("2d")},e.prototype.UpdateFrame=function(e){e&&this.canvas.getContext("2d").putImageData(e,0,0),this.scaleNow()},e.prototype.startScaling=function(){this.scaleNow()},e.prototype.scaleNow=function(){null!=this.scaling_canvas&&this.scaling_canvas.getContext("2d").drawImage(this.canvas,0,0,this.scaling_canvas.width,this.scaling_canvas.height)},e.prototype.IsExternal=function(){return this.external_canvas},e.MakeCanvas=function(e,t){var n=document.createElement("canvas");n.width=e,n.height=t;var i=n.getContext("2d");return i.fillStyle="red",i.fillRect(0,0,n.width,n.height),n},e}();!function(e){e[e.ARGB=0]="ARGB"}(ge||(ge={}));var Oe=function(){function e(){this.videoInput=null,this.mScreenCaptureDevice="_screen",this.mAllowScreenCapture=!1,this.mAllowAudioCapture=!1}return Object.defineProperty(e,"SharedInstance",{get:function(){return this.sSharedInstance},enumerable:!1,configurable:!0}),e.ResetSharedInstance=function(){this.sSharedInstance=new e},Object.defineProperty(e.prototype,"VideoInput",{get:function(){return null===this.videoInput&&(this.videoInput=new Ae),this.videoInput},enumerable:!1,configurable:!0}),e.prototype.EnableScreenCapture=function(e,t){this.mScreenCaptureDevice=e,this.mAllowScreenCapture=!0,this.mAllowAudioCapture=t},e.prototype.DisableScreenCapture=function(){this.mAllowScreenCapture=!1,this.mAllowAudioCapture=!1},e.prototype.GetVideoDevices=function(){var e=De.GetVideoDevices();if(null!=this.VideoInput){var t=this.VideoInput.GetDeviceNames();e=e.concat(t)}return this.mAllowScreenCapture&&e.push(this.mScreenCaptureDevice),e},e.prototype.GetAudioInputDevices=function(){return De.GetAudioInputDevices()},e.IsNameSet=function(e){return null!==e&&""!==e},e.prototype.getUserMedia=function(t){return n=this,i=void 0,o=function(){var n,i,r,o,a,s;return function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(n=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],i=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}(this,(function(c){switch(c.label){case 0:return n=t.clone(),i=new MediaStream,n.Video&&e.IsNameSet(n.VideoDeviceName)?null!=this.videoInput&&this.videoInput.HasDevice(n.VideoDeviceName)?(r=this.videoInput.GetStream(n.VideoDeviceName),i.addTrack(r.getVideoTracks()[0]),n.Video=!1,[3,3]):[3,1]:[3,3];case 1:return this.mAllowScreenCapture&&n.VideoDeviceName===this.mScreenCaptureDevice?(o={},n.IdealWidth<=0&&n.IdealHeight<=0?o.video=!0:(a={},n.IdealWidth>0&&(a.width=n.IdealWidth),n.IdealHeight>0&&(a.height=n.IdealHeight),o.video=a),this.mAllowAudioCapture&&n.Audio&&(o.audio=!0),[4,navigator.mediaDevices.getDisplayMedia(o)]):[3,3];case 2:(s=c.sent()).getVideoTracks().length>0?i.addTrack(s.getVideoTracks()[0]):console.warn("Failed to get video access via getDisplayMedia"),o.audio&&(s.getAudioTracks().length>0?(i.addTrack(s.getAudioTracks()[0]),n.Audio=!1):console.warn("Failed to get audio access via getDisplayMedia.")),n.Video=!1,c.label=3;case 3:return n.Video||n.Audio?[4,De.getAssetUserMedia(n)]:[3,5];case 4:c.sent().getTracks().forEach((function(e){return i.addTrack(e)})),c.label=5;case 5:return[2,i]}}))},new((r=void 0)||(r=Promise))((function(e,t){function a(e){try{c(o.next(e))}catch(e){t(e)}}function s(e){try{c(o.throw(e))}catch(e){t(e)}}function c(t){var n;t.done?e(t.value):(n=t.value,n instanceof r?n:new r((function(e){e(n)}))).then(a,s)}c((o=o.apply(n,i||[])).next())}));var n,i,r,o},e.sSharedInstance=new e,e}(),_e=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),Le=function(e,t,n,i){return new(n||(n=Promise))((function(r,o){function a(e){try{c(i.next(e))}catch(e){o(e)}}function s(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((i=i.apply(e,t||[])).next())}))},Ne=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,s[0]&&(a=0)),a;)try{if(n=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,i=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){a.label=s[1];break}if(6===s[0]&&a.label<r[1]){a.label=r[1],r=s;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(s);break}r[2]&&a.ops.pop(),a.trys.pop();continue}s=t.call(e,a)}catch(e){s=[6,e],i=0}finally{n=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}},Me=function(e){function t(t){var n=e.call(this,t)||this;return n.mMediaConfig=new ie,n.mLocalStream=null,n.mConfigurationState=j.Invalid,n.mConfigurationError=null,n.MediaPeer_InternalMediaStreamAdded=function(e,t){n.EnqueueRtcEvent(new B(e.ConnectionId,t.VideoElement))},n.log=new l("MediaNetwork"+n.mId),n.mConfigurationState=j.NoConfiguration,n}return _e(t,e),t.prototype.GetMedia=function(e){return Le(this,void 0,void 0,(function(){return Ne(this,(function(t){switch(t.label){case 0:if(0==De.IsUserMediaAvailable())throw"Configuration failed. navigator.mediaDevices is undefined. The browser might not allow media access.Is the page loaded via http or file URL? Some browsers only support media access via https!";return[4,Oe.SharedInstance.getUserMedia(e)];case 1:return[2,t.sent()]}}))}))},t.prototype.Configure=function(e){var t=this;this.mIsDisposed?this.OnConfigurationFailed("Network has been disposed."):(this.mMediaConfig=e,this.mConfigurationError=null,this.mConfigurationState=j.InProgress,null!==this.mLocalStream&&(this.mLocalStream.Dispose(),this.mLocalStream=null),e.Audio||e.Video?(C.L("calling GetUserMedia. Media config: "+JSON.stringify(e)),setTimeout((function(){return Le(t,void 0,void 0,(function(){var t,n,i=this;return Ne(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.GetMedia(e)];case 1:return t=r.sent(),this.mIsDisposed?[2]:(this.mLocalStream=new ve(!0,this.log),t.getTracks().forEach((function(e){i.mLocalStream.UpdateTrack(e)})),this.mLocalStream.SetMute(!0),this.OnLocalStreamUpdated(),this.OnConfigurationSuccess(),[3,3]);case 2:return n=r.sent(),this.OnConfigurationFailed("Accessing media failed due to exception: "+n),[3,3];case 3:return[2]}}))}))}),0)):(this.OnLocalStreamUpdated(),this.OnConfigurationSuccess()))},t.prototype.OnLocalStreamUpdated=function(){var e=this;Object.values(this.IdToConnection).forEach((function(t){return t.SetLocalStream(e.mLocalStream,e.mMediaConfig)})),null!=this.mLocalStream&&(this.mLocalStream.InternalStreamAdded=function(t){e.EnqueueRtcEvent(new B(I.INVALID,e.mLocalStream.VideoElement))})},t.prototype.Update=function(){e.prototype.Update.call(this),null!=this.mLocalStream&&this.mLocalStream.Update()},t.prototype.Flush=function(){e.prototype.Flush.call(this)},t.prototype.GetConfigurationState=function(){return this.mConfigurationState},t.prototype.GetConfigurationError=function(){return this.mConfigurationError},t.prototype.ResetConfiguration=function(){this.mConfigurationState=j.NoConfiguration,this.mMediaConfig=new ie,this.mConfigurationError=null},t.prototype.OnConfigurationSuccess=function(){this.mConfigurationState=j.Successful},t.prototype.OnConfigurationFailed=function(e){this.mConfigurationError=e,this.mConfigurationState=j.Failed},t.prototype.PeekFrame=function(e){if(null!=e){if(e.id==I.INVALID.id){if(null!=this.mLocalStream)return this.mLocalStream.PeekFrame()}else{var t=this.IdToConnection[e.id];if(null!=t)return t.PeekFrame()}return null}},t.prototype.TryGetFrame=function(e){if(null!=e){if(e.id==I.INVALID.id){if(null!=this.mLocalStream)return this.mLocalStream.TryGetFrame()}else{var t=this.IdToConnection[e.id];if(null!=t)return t.TryGetRemoteFrame()}return null}},t.prototype.SetVolume=function(e,t){C.L("SetVolume called. Volume: "+e+" id: "+t.id);var n=this.IdToConnection[t.id];if(null!=n)return n.SetVolume(e)},t.prototype.SetVolumePan=function(e,t,n){C.L("SetVolumePan called. Volume: "+e+"pan: "+t+" id: "+n.id);var i=this.IdToConnection[n.id];if(null!=i)return i.SetVolumePan(e,t)},t.prototype.HasAudioTrack=function(e){var t=this.IdToConnection[e.id];return null!=t&&t.HasAudioTrack()},t.prototype.HasVideoTrack=function(e){var t=this.IdToConnection[e.id];return null!=t&&t.HasVideoTrack()},t.prototype.IsMute=function(){if(null!=this.mLocalStream&&null!=this.mLocalStream.Stream){var e=this.mLocalStream.Stream.getAudioTracks();if(e.length>0&&e[0].enabled)return!1}return!0},t.prototype.SetMute=function(e){if(null!=this.mLocalStream&&null!=this.mLocalStream.Stream){var t=this.mLocalStream.Stream.getAudioTracks();t.length>0&&(t[0].enabled=!e)}},t.prototype.CreatePeer=function(e){var t=this,n=new L(this.mNetConfig),i=new be(e,n,this.mMediaConfig,this.log);return i.InternalStreamAdded=this.MediaPeer_InternalMediaStreamAdded,null!=this.mLocalStream&&setTimeout((function(){return Le(t,void 0,void 0,(function(){return Ne(this,(function(e){switch(e.label){case 0:return[4,i.SetLocalStream(this.mLocalStream,this.mMediaConfig)];case 1:return e.sent(),this.log.L("Set local stream to new peer"),[2]}}))}))})),i},t.prototype.DisposeInternal=function(){e.prototype.DisposeInternal.call(this),this.DisposeLocalStream()},t.prototype.DisposeLocalStream=function(){null!=this.mLocalStream&&(this.mLocalStream.Dispose(),this.mLocalStream=null)},t}(U),xe=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),Fe=function(e){function t(t){var n=e.call(this,t)||this;return n.Initialize(n.CreateNetwork()),n}return xe(t,e),t.prototype.CreateNetwork=function(){return new Me(this.mNetworkConfig)},t.prototype.DisposeInternal=function(t){e.prototype.DisposeInternal.call(this,t),t&&(null!=this.mNetwork&&this.mNetwork.Dispose(),this.mNetwork=null)},t}(ce),Ue={Default:0,WaitForDevices:1,RequestAccess:2},Ve={Uninitialized:0,Initializing:1,Initialized:2,Failed:3},je=Ve.Uninitialized,Ge=null;function We(e,t,n){console.debug("CAPI_InitAsync mode: "+e),je=Ve.Initializing,t&&t.canvas&&(Ge=t.canvas),n&&a.EmitAdapter(),function(){if(null!=Ge){var e=null;e=function(){fe.Resolve(),!0===fe.HasCompleted()&&(Ge.removeEventListener("click",e,!1),Ge.removeEventListener("touchstart",e,!1))},fe.onautoplayblocked=function(){C.L("The browser blocked playback of a video stream. Trying to resolve this the next time the user interacts with the canvas"),Ge.addEventListener("click",e,!1),Ge.addEventListener("touchstart",e,!1)}}else C.LW("Autoplay workaround inactive. No canvas object known to register click & touch event handlers.")}();var i=De.IsApiAvailable();i&&e==Ue.WaitForDevices?De.Update():i&&e==Ue.RequestAccess?De.RequestUpdate():(je=Ve.Initialized,0==i&&console.debug("Initialized without accessible DeviceAPI"))}function He(){return 0==De.IsPending&&je==Ve.Initializing&&(je=Ve.Initialized,console.debug("Init completed.")),je}function Be(e){e<0||e>4?C.LogError("Invalid log level "+e):C.SetLogLevel(e)}var qe={},ze=1;function Ke(){return!(!U||!V)}function Je(){return!(!RTCPeerConnection||!RTCDataChannel)}function Qe(e){var t=ze;if(ze++,null==e||"string"!=typeof e||0===e.length)return C.LogError("invalid configuration. Returning -1! Config: "+e),-1;var n=new r;return n.FromJson(e),qe[t]=new U(n),t}function Xe(e){e in qe&&(qe[e].Dispose(),delete qe[e])}function Ye(e,t){return qe[e].Connect(t)}function $e(e,t){qe[e].StartServer(t)}function Ze(e){qe[e].StopServer()}function et(e,t){qe[e].Disconnect(new I(t))}function tt(e){qe[e].Shutdown()}function nt(e){qe[e].Update()}function it(e){qe[e].Flush()}function rt(e,t,n,i){qe[e].SendData(new I(t),n,i)}function ot(e,t,n,i,r,o){var a=new Uint8Array(n.buffer,i,r);return qe[e].SendData(new I(t),a,o)}function at(e,t,n){return qe[e].GetBufferedAmount(new I(t),n)}function st(e){return qe[e].Dequeue()}function ct(e){return qe[e].Peek()}function ut(e){return lt(qe[e].Peek())}function lt(e){return null==e?-1:null==e.RawData?0:(e.RawData,e.RawData.length)}function dt(e,t,n,i){if(null==e)return 0;if("string"==typeof e){var r=0;for(r=0;r<e.length&&r<i;r++)t[n+r]=e.charCodeAt(r);return r}for(r=0,r=0;r<e.length&&r<i;r++)t[n+r]=e[r];return r}function pt(e,t,n,i,r,o,a,s,c,u){var l=st(e);if(null==l)return!1;t[n]=l.Type,i[r]=l.ConnectionId.id;var d=dt(l.RawData,o,a,s);return c[u]=d,!0}function ht(e,t,n,i,r,o,a,s,c,u){var l=ct(e);if(null==l)return!1;t[n]=l.Type,i[r]=l.ConnectionId.id;var d=dt(l.RawData,o,a,s);return c[u]=d,!0}function ft(e){qe[e].RequestStats()}function mt(e,t,n,i,r){var o=qe[e].DequeueRtcEvent();if(o&&o.EventType==S.Stats){var a=o;return t[n]=a.EventType,i[r]=a.ConnectionId.id,JSON.stringify(a.Reports)}return null}function gt(){return!(!Me||!Fe)}function vt(){return!(!navigator||!navigator.mediaDevices)}function yt(e){var t=new r;t.FromJson(e);var n=new Me(t),i=ze;return ze++,qe[i]=n,i}function Ct(e,t,n,i,r,o,a,s,c,u,l,d,p,h,f,m,g){void 0===p&&(p=""),void 0===h&&(h=[]),void 0===f&&(f=-1),void 0===m&&(m=""),void 0===g&&(g="");var v=new ie;v.Audio=t,v.Video=n,v.MinWidth=i,v.MinHeight=r,v.MaxWidth=o,v.MaxHeight=a,v.IdealWidth=s,v.IdealHeight=c,v.MinFps=u,v.MaxFps=l,v.IdealFps=d,v.VideoDeviceName=p,h&&h.length>0&&(v.VideoCodecs=h),f>0&&(v.VideoBitrateKbits=f),m&&(v.VideoContentHint=m),g&&(v.AudioInputDevice=g),v.FrameUpdates=!0,qe[e].Configure(v)}function St(e){return qe[e].GetConfigurationState()}function bt(e){var t=qe[e].GetConfigurationError();return null==t?0:t.length}function It(e){var t=qe[e].GetConfigurationError();return null==t?"":t}function Rt(e){return qe[e].ResetConfiguration()}function wt(e,t,n,i,r,o,a,s,c){var u=qe[e].TryGetFrame(new I(t));if(null==u||null==u.Buffer)return!1;n[i]=u.Width,r[o]=u.Height;for(var l=0;l<c&&l<u.Buffer.length;l++)a[s+l]=u.Buffer[l];return!0}function Et(e,t,n,i,r,o){var a=qe[e].TryGetFrame(new I(t));return null!=a&&(a.Width!=n||a.Height!=i?(C.LW("CAPI_MediaNetwork_TryGetFrame_ToTexture failed. Width height expected: "+a.Width+"x"+a.Height+" but received "+n+"x"+i),!1):(a.ToTexture(r,o),!0))}function Tt(e,t,n,i,r,o){var a=qe[e].PeekFrame(new I(t));return null!=a&&(n[i]=a.Width,r[o]=a.Height,!0)}function Pt(e,t){var n=qe[e].PeekFrame(new I(t)),i=-1;return null!=n&&null!=n.Buffer&&(i=n.Buffer.length),i}function Dt(e,t,n){qe[e].SetVolume(t,new I(n))}function At(e,t,n,i){qe[e].SetVolumePan(t,n,new I(i))}function kt(e,t){return qe[e].HasAudioTrack(new I(t))}function Ot(e,t){return qe[e].HasVideoTrack(new I(t))}function _t(e,t){qe[e].SetMute(t)}function Lt(e){return qe[e].IsMute()}function Nt(){De.Update()}function Mt(){De.RequestUpdate()}function xt(){return De.LastUpdate}function Ft(){return Oe.SharedInstance.GetVideoDevices().length}function Ut(e){var t=Oe.SharedInstance.GetVideoDevices();return t.length>e?t[e]:(C.LE("Requested video device with index "+e+" does not exist."),"")}function Vt(){return Oe.SharedInstance.GetAudioInputDevices().length}function jt(e){var t=Oe.SharedInstance.GetAudioInputDevices();return t.length>e?JSON.stringify(t[e]):(C.LE("Requested audio input device with index "+e+" does not exist."),"")}function Gt(e,t,n,i,r){var o=document.querySelector(e);return!!o&&(console.debug("CAPI_VideoInput_AddCanvasDevice",{query:e,name:t,width:n,height:i,fps:r}),(n<=0||i<=0)&&(n=o.width,i=o.height),Oe.SharedInstance.VideoInput.AddCanvasDevice(o,t,n,i,r),!0)}function Wt(e,t,n,i){Oe.SharedInstance.VideoInput.AddDevice(e,t,n,i)}function Ht(e){Oe.SharedInstance.VideoInput.RemoveDevice(e)}function Bt(e,t,n,i,r,o,a,s){var c=null;return t&&i>0&&(c=new Uint8ClampedArray(t.buffer,n,i)),Oe.SharedInstance.VideoInput.UpdateFrame(e,c,r,o,ge.ARGB,a,s)}function qt(e,t){return Oe.SharedInstance.EnableScreenCapture(e,t)}function zt(){return null!==Ge?Ge:(C.LogWarning("Using GetUnityCanvas without a known cavans reference."),document.querySelector("canvas"))}function Kt(){return zt().getContext("webgl2")}console.debug("loading awrtc modules ..."),console.debug("loading awrtc modules completed!")})(),i})()));