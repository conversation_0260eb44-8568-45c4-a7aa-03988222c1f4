namespace GranVideoChatSDK
{
    public interface IVideoChat
    {
        /// <summary>
        /// 记录是否打通了webrtc
        /// </summary>
        bool IsConnected();

        void Init();
        void SetNetworkConfig(string siglURL, string turnURL, string turnName, string turnPwd, bool multiPlayer = true, params string[] stunURLs);
        void EnterRoom();
        void OuterRoom(bool isRemoveObject);
        void SetMute(bool state);
        void SetRemoteVolume(float volume);
        void ReCall();
        void SendData(byte[] image);
        void SendMessage(string action, string content);

        void Reg_VideoDataCallback(VideoDelegate.Del_OnVideoDataCallback callBack);
        void Reg_GranCamDataCallback(VideoDelegate.Del_OnGranCamDataCallback callBack);
        void Reg_CallEventCallback(VideoDelegate.Del_OnCallEvent callBack);
        void Reg_VideoCoreErrorCallback(VideoDelegate.Del_OnVideoCoreError callBack);
        void Reg_GranCameraResolutionChangedCallback(VideoDelegate.Del_OnGranCameraResolutionChanged callBack);

        void UnReg_VideoDataCallback(VideoDelegate.Del_OnVideoDataCallback callBack);
        void UnReg_GranCamDataCallback(VideoDelegate.Del_OnGranCamDataCallback callBack);
        void UnReg_CallEventCallback(VideoDelegate.Del_OnCallEvent callBack);
        void UnReg_VideoCoreErrorCallback(VideoDelegate.Del_OnVideoCoreError callBack);
        void UnReg_GranCameraResolutionChangedCallback(VideoDelegate.Del_OnGranCameraResolutionChanged callBack);
    }
}
