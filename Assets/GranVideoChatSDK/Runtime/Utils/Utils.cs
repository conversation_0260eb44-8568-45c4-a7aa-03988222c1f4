namespace GranVideoChatSDK
{
	public class Utils  {
		
        #region Platform

        /// <summary>
        /// check android platform
        /// </summary>
        /// <value><c>true</c> if android platform; otherwise, <c>false</c>.</value>
        /// $momo 2016-09-28
        public static bool AndroidPlatform
        {
	        get
	        {
#if !UNITY_EDITOR && UNITY_ANDROID
                return true;
#else
		        return false;
#endif
	        }
        }

        /// <summary>
        /// check ios platform
        /// </summary>
        /// <value><c>true</c> if iOS platform; otherwise, <c>false</c>.</value>
        /// $momo 2016-09-28
        public static bool iOSPlatform
        {
	        get
	        {
#if !UNITY_EDITOR && UNITY_IOS
                return true;
#else
		        return false;
#endif
	        }
        }

        #endregion
	}
}
