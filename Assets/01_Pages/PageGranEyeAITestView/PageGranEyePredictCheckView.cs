using UnityEngine;
using System;
using System.Threading;
using System.Linq;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.graneye;
using com.luxza.ui.components.atoms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine.UI;
using com.luxza.ui.components.molecules;
using UnityEngine.Localization.SmartFormat.PersistentVariables;

public class PageGranEyePredictCheckView : MonoBehaviour
{
    [SerializeField] private Page _page;
    [SerializeField] private Canvas _targetCanvas;
    [SerializeField] private GranIconButton _btn_Close;
    [SerializeField] protected RawImage _rawImage_Board;
    [SerializeField] protected RectTransform _rectTransform_dot;
    [SerializeField] private RectTransform _RectTransformDarts;
    [SerializeField] private RectTransform _OutlineDot;
    [SerializeField] private RectTransform _BoundingBox;
    [SerializeField] private GranText _txt_PredictDartsCount;
    [SerializeField] private GranButton _btn_Refresh;

    protected GranEyeSettingParameter _parameter;
    
    protected void Awake()
    {
        _page.OnActivate.Subscribe(async _ => await OnActivate(_)).RegisterTo(destroyCancellationToken);
        _btn_Close.onClickAsObservable.Subscribe(async _ => await OnCloseClicked()).RegisterTo(destroyCancellationToken);
        _btn_Refresh.onClickAsObservable.Subscribe(async _ => await RefreshPredictionAsync()).RegisterTo(destroyCancellationToken);
    }
    
    private async UniTask OnActivate(Unit _)
    {
        await RefreshPredictionAsync();
        OnActivateDone();
    }

    private async UniTask RefreshPredictionAsync()
    {
        if(!_page.Meta.TryGetParameter<GranEyeSettingParameter>(out var parameter)) {
            throw new ArgumentException("GranEyeSettingParameter is required.");
        }
        _parameter = parameter;
        _targetCanvas.sortingOrder = 1;
        _rawImage_Board.texture = parameter.Texture;
        _rawImage_Board.color = Color.white;

        var DartsPredict = await GranEye.GranEyeCore.PredictDartWithoutDartsCount(parameter.CameraPos, _rawImage_Board.texture, true);
        var DartsCount = await GranEye.GranEyeCore.DartCountPredict(parameter.CameraPos,CancellationToken.None);

        ShowAllCalibrationPoints(parameter);

        _txt_PredictDartsCount.text = "";
        if (DartsCount != null && DartsCount.Any())
        {
            var DartsCountResult = DartsCount.Max(t => t.Tag).ToString();
            _txt_PredictDartsCount.text = $"Darts Count: {DartsCountResult}\n";
        }
        else
        {
            _txt_PredictDartsCount.text = $"Darts Count: 0\n";
        }
        
        for (int i = 0; i < DartsPredict.Count; i++)
        {
            var dartPos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(DartsPredict[i].x, DartsPredict[i].y));
            var virtualPosition = GranEye.GranEyeCore.GetCalibrationTool(parameter.CameraPos).GetVirtualPointFromReal(new Vector3(dartPos.x, dartPos.y, 0));
            var score = GranEye.GranEyeCore.GetCalibrationTool(parameter.CameraPos).GetDartZone(virtualPosition, true);
            var realPos = TflitePositionHelper.ConvertToPixelCoordinates(dartPos, _rawImage_Board.rectTransform.rect.width);

            // 新しいdotを生成
            var rectTransform = Instantiate(_OutlineDot, _RectTransformDarts);
            rectTransform.sizeDelta = new Vector2(1.5f, 1.5f);
            rectTransform.GetComponent<Image>().color = Color.yellow;
            rectTransform.localPosition = realPos;
            rectTransform.gameObject.SetActive(true);

            var conf = $"dart {DartsPredict[i].score:0.00}";
            DrawRectangleAroundPoint(rectTransform, conf);

            _txt_PredictDartsCount.text +=
                $"  {DartsPredict[i].x},  {DartsPredict[i].y},  {DartsPredict[i].score * 100:0.00}%\n" +
                $"  Score: {score}\n";
        }
    }

    private void ShowAllCalibrationPoints(GranEyeSettingParameter parameter)
    {
        var allPoint = GranEye.GranEyeCore.GetCalibrationTool(parameter.CameraPos).GetAllPointsOnRealBoard();
        for (int i = 0; i < allPoint.Count; i++)
        {
            var dot = Instantiate(_rectTransform_dot, _rawImage_Board.transform);
            dot.gameObject.SetActive(true);
            var pixelPos = TflitePositionHelper.ConvertToPixelCoordinates(allPoint[i], _rawImage_Board.rectTransform.rect.width);
            dot.localPosition = pixelPos;
            dot.sizeDelta = new Vector2(2, 2);
            dot.GetComponent<Image>().color = Color.yellow;
        }
    }

    protected virtual void OnActivateDone()
    {

    }

    private void DrawRectangleAroundPoint(RectTransform centerPoint, string conf)
    {
        // BoundingBoxプレハブから新しいインスタンスを生成
        var boundingBoxInstance = Instantiate(_BoundingBox, centerPoint.parent);

        // localPositionで中心点に合わせる
        boundingBoxInstance.localPosition = centerPoint.localPosition;

        // オブジェクトをアクティブ化
        boundingBoxInstance.gameObject.SetActive(true);

        // 信頼度テキストをセット
        var txtConfidence = boundingBoxInstance.GetComponentInChildren<GranText>();
        txtConfidence.text = conf;
    }

    private async UniTask OnCloseClicked()
    {
        await PageManager.Instance.CloseModalAsync(_page);
    }
}

