using System;
using System.Linq;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.usecases.player;
using com.luxza.grandarts.usecases.user;
using com.luxza.grandarts.userinterface.account;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.granlog;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

public class PageDeletePlayerView : MonoBehaviour
{
    [SerializeField] private Page _page;
    [SerializeField] private GranButton _btn_Cancel;
    [SerializeField] private GranButton _btn_Delete;
    [SerializeField] private SwitchPlayerSlot[] _playerIconList;

    private IPlayer _selectPlayer;
    
    private void Awake()
    {
        _btn_Cancel.onClickAsObservable.Subscribe(async _ => await OnCancelClicked()).RegisterTo(destroyCancellationToken);
        _btn_Delete.onClickAsObservable.Subscribe(async _ => await OnDeleteClicked()).RegisterTo(destroyCancellationToken);

        _btn_Delete.interactable = false;
    }
    
    private void Start()
    {
        var players = ApplicationAuth.LoggedInUser.Players.Where(p =>
            p.BoardSize == ApplicationAuth.LoggedInUser.CurrentPlayer.BoardSize).ToList();
        for (int i = 0; i < _playerIconList.Length; i++)
        {
            _playerIconList[i].gameObject.SetActive(players.Count > i);
            if(players.Count > i)
            {
                _playerIconList[i].Bind(players[i]);
                _playerIconList[i].onClickSelect.Subscribe(OnClickPlayerIcon).RegisterTo(destroyCancellationToken);
            }
        }
    }
    
    private void OnClickPlayerIcon(IPlayer player)
    {
        _selectPlayer = player;
        foreach (var item in _playerIconList)
        {
            if (!item.IsBinded) continue;
            if (_selectPlayer.Id != item.BindPlayer.Id)
                item.ResetSelectFlame();
        }
        _btn_Delete.interactable = true;
    }

    private async UniTask OnCancelClicked()
    {
        await PageManager.Instance.CloseModalAsync(_page);
    }
    
    IUserCacheRepository _userCacheRepository;
    private readonly IInstantPlayerRepository _instantPlayerRepository;
    private readonly IPlayerPresenceRepository _playerPresenceRepository;

    private async UniTask OnDeleteClicked()
    {
        if(_selectPlayer == null) 
        {
            Log.d("Please select a player to delete.");
            return;
        }
        _btn_Cancel.loading = true;
        _btn_Delete.loading = true;
        try
        {
            var isSuccess = await new DeletePlayerUsecase(DIContainer.Instance.Resolve<IPlayerRepository>())
                .ExecuteAsync(ApplicationAuth.LoggedInUser.Id, _selectPlayer.Id, destroyCancellationToken);
            Log.d($"Delete player result: {isSuccess}");
            if (isSuccess)
            {
                if (!ApplicationAuth.LoggedInUser.Players.Any())
                {
                    MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.RegisterPlayerAgain), GranModal.IconType.OK,
                        (LocalizeString.GetLocalizedString(LocalizeKey.Labels.OK),
                            async () =>
                            {
                                await PageManager.Instance.OpenAsync(new PageMeta(PageNames.PlayerCreation, new PagePlayerCreationParameter(enableCloseButton: false)));
                            }));
                }
                else
                {
                    if (_selectPlayer.Id == ApplicationAuth.LoggedInUser.CurrentPlayer.Id)
                    {
                        var players = ApplicationAuth.LoggedInUser.Players.Where(p =>
                            p.BoardSize == ApplicationAuth.LoggedInUser.CurrentPlayer.BoardSize).ToList();
                        var usecase = new SwitchPlayerUsecase(
                            DIContainer.Instance.Resolve<IUserCacheRepository>(),
                            DIContainer.Instance.Resolve<IInstantPlayerRepository>(),
                            DIContainer.Instance.Resolve<IPlayerPresenceRepository>());
                        await usecase.SwitchAsync(players.First().Id,destroyCancellationToken);
                    }
                    _btn_Cancel.loading = false;
                    _btn_Delete.loading = false;
                    if(_page.Meta.TryGetParameter<PageDeletePlayerPageParameter>(out var param)) {
                        param.OnDeletePlayer?.Invoke();
                    }
                    MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.DeletePlayerSuccess));
                    await PageManager.Instance.CloseModalAsync(_page);
                }
            }
            else
            {
                Log.w("Delete player failed");
                _btn_Cancel.loading = false;
                _btn_Delete.loading = false;
            }
        }
        catch (Exception e) 
        {
            _btn_Cancel.loading = false;
            _btn_Delete.loading = false;
            Log.e(e.Message);
        }
    }
}