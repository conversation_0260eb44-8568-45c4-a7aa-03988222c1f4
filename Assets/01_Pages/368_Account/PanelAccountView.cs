using System.Linq;
using com.luxza.grandarts.usecases.auth;
using com.luxza.grandarts.userinterface.account;
using com.luxza.grandarts.userinterfaces.components.player;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.components.organizms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

public class PanelAccountView : MonoBehaviour
{
    [SerializeField] private GranHeader _header;
    [SerializeField] private GranButton _Btn_ChangeEmailAddress;
    [SerializeField] private GranButton _Btn_ChangePassword;
    [SerializeField] private GranButton _Btn_DeletePlayer;
    [SerializeField] private GranButton _Btn_DeleteAccount;
    [SerializeField] private GranText _granText_EmailAddress;
    [SerializeField] private PlayerIconWithName[] _playerList;

    private void Awake()
    {
        _granText_EmailAddress.text = ApplicationAuth.LoggedInUser.Email.Value;
        _header.onClickLeftIcon1.Subscribe(async _ => await OnBackButtonClicked()).RegisterTo(destroyCancellationToken);
        _Btn_ChangeEmailAddress.onClickAsObservable.Subscribe(async _ => await OnChangeEmailAddressButtonClicked()).RegisterTo(destroyCancellationToken);
        _Btn_ChangePassword.onClickAsObservable.Subscribe(async _ => await OnChangePasswordButtonClicked()).RegisterTo(destroyCancellationToken);
        _Btn_DeletePlayer.onClickAsObservable.Subscribe(async _ => await OnDeletePlayerButtonClicked()).RegisterTo(destroyCancellationToken);
        _Btn_DeleteAccount.onClickAsObservable.Subscribe(async _ => await OnDeleteAccountButtonClicked()).RegisterTo(destroyCancellationToken);
    }

    private void Start()
    {
        this.UpdatePlayerList();
    }

    private void UpdatePlayerList()
    {
        var players = ApplicationAuth.LoggedInUser.Players.Where(p =>
            p.BoardSize == ApplicationAuth.LoggedInUser.CurrentPlayer.BoardSize).ToList();
        for (int i = 0; i < _playerList.Length; i++)
        {
            _playerList[i].gameObject.SetActive(players.Count > i);
            if(players.Count > i)
            {
                _playerList[i].Bind(players[i]);
                _playerList[i].Selected = ApplicationAuth.LoggedInUser.CurrentPlayer.Id == players[i].Id;
            }
        }
    }

    private async UniTask OnBackButtonClicked()
    {
        await PageManager.Instance.Back();
    }

    private async UniTask OnChangeEmailAddressButtonClicked()
    {
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.ChangeEmailAddress, parameter: new PageChangeEmailAddressPageParameter((email) =>
        {
            if (_granText_EmailAddress != null)
            {
                _granText_EmailAddress.text = email.Value;
            }
        })));
    }

    private async UniTask OnChangePasswordButtonClicked()
    {
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.ResetPassword));
    }

    private async UniTask OnDeletePlayerButtonClicked()
    {
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.DeletePlayer,
            parameter: new PageDeletePlayerPageParameter(this.UpdatePlayerList)));
    }

    private async UniTask OnDeleteAccountButtonClicked()
    {
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.DeleteUserPassword));
    }
}
