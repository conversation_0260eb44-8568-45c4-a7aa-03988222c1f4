using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.userinterfaces.components.game.graneye;
using com.luxza.graneye;
using com.luxza.ui.components.organizms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

public class PageGranEyeAllPointsCheckView : PageGranEyeGameAllPointsModal
{
    [Header("子类特有UI组件")]
    [SerializeField] private GranHeader _granHeader;

    protected new void Awake()
    {
        base.Awake();

        // 绑定Header返回按钮
        _granHeader.onClickLeftIcon1.Subscribe(async _ => await OnBackClicked()).RegisterTo(destroyCancellationToken);
    }

    /// <summary>
    /// 返回按钮点击事件
    /// </summary>
    private async UniTask OnBackClicked()
    {
        if (_currentPanelState == PanelState.Adjustment)
        {
            // 取消半径修改
            if (_hasRadiusChanges)
            {
                CancelRadiusChanges();
            }

            // 返回Panel1面板
            SwitchToPanel(PanelState.Confirmation);
        }
        else
        {
            // 返回GranEyeManualSet
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeManualSet, _parameter));
        }
    }



    /// <summary>
    /// 重写OK按钮点击事件
    /// </summary>
    protected override async UniTask OnOKClicked()
    {
        if (_parameter.CameraPos == GranEyeCore.CameraPos.Left)
        {
            _parameter.CameraPos = GranEyeCore.CameraPos.Right;
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeCalibrationPointCheck, _parameter));
        }
        else
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeAIModelTest));
        }
    }

    /// <summary>
    /// 取消半径修改，恢复到原始状态
    /// </summary>
    private void CancelRadiusChanges()
    {
        _hasRadiusChanges = false;

        // 重置基类的自定义模式
        if (_isCustomizationMode)
        {
            // 恢复到修改前的状态
            _isCustomizationMode = false;
        }

        // 重置UI状态
        _pullDown_AreaSelect.SetValueWithoutNotify(0);
        _btn_Plus.interactable = false;
        _btn_Minus.interactable = false;

        // 隐藏基类的调整OK按钮
        if (_btn_AdjustmentOK != null)
            _btn_AdjustmentOK.gameObject.SetActive(false);
    }

    #region 重写基类方法



    /// <summary>
    /// 重写基类的加按钮方法
    /// </summary>
    protected override void OnPlusClicked()
    {
        base.OnPlusClicked();
        _hasRadiusChanges = true;
    }

    /// <summary>
    /// 重写基类的减按钮方法
    /// </summary>
    protected override void OnMinusClicked()
    {
        base.OnMinusClicked();
        _hasRadiusChanges = true;
    }
    
    #endregion
}
