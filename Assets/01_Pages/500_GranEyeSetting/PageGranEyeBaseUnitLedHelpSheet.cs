using com.luxza.ui.page;
using R3;
using UnityEngine;

public class PageGranEyeBaseUnitLedHelpSheet : MonoBehaviour
{
    [SerializeField] private Page _page;
    
    void Awake()
    {
        _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
    }

    private async void OnActivate(Unit unit)
    {
        
    }
    
    public async void Close()
    {
        await PageManager.Instance.CloseModalAsync(_page);
    }
}
