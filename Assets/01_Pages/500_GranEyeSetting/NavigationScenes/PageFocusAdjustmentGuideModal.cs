using System;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.graneye;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using R3;
using UnityEngine;

public class PageFocusAdjustmentGuideModal : MonoBehaviour
{
    [SerializeField] private Page _page;
    [SerializeField] private GranButton _btn_Close;
    [SerializeField] private GranText _txt_Describe;
    [SerializeField] private Transform _transform_Image;

    void Awake()
    {
        _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        _btn_Close.onClickAsObservable.Subscribe(OnCloseClicked).RegisterTo(destroyCancellationToken);
    }

    private void OnActivate(Unit _)
    {
        if (_page.Meta.TryGetParameter<GranEyeSettingParameter>(out var param))
        {
            switch (param.CameraPos)
            {
                case GranEyeCore.CameraPos.Left:
                    _txt_Describe.text = LocalizeString.GetLocalizedString(LocalizeKey.Labels.AdjustLeftFocus);
                    break;
                case GranEyeCore.CameraPos.Right:
                    _txt_Describe.text = LocalizeString.GetLocalizedString(LocalizeKey.Labels.AdjustRightFocus);
                    _transform_Image.eulerAngles = new Vector3(0, 180, 0);
                    break;
            }
            _transform_Image.gameObject.SetActive(true);
        }
        else
        {
            throw new ArgumentException("Invalid parameter.");
        }
    }

    private async void OnCloseClicked(Unit _)
    {
        await PageManager.Instance.CloseModalAsync(_page);
    }
}
