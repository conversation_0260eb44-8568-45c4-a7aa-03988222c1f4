using System.Collections.Generic;
using System.Text.RegularExpressions;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.granlog;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.components.organizms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using Luxza.Unit.Wifi;
using R3;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

//GranEyeConnectNavigationScenes Page2
public class PageWifiSettingView : MonoBehaviour
{
    [SerializeField] private GranHeader _granHeader;
    [SerializeField] private GameObject _InputArea_iOS;
    [SerializeField] private GameObject _InputArea_Android;
    [SerializeField] private GranInputField _GranInputFieldiOS_SSID;
    [SerializeField] private TMP_Dropdown _DropdownAndroid_SSID;
    [SerializeField] private GranInputField _GranInputFieldiOS_Password;
    [SerializeField] private GranInputField _GranInputFieldAndroid_Password;
    [SerializeField] private GranButton _btn_ManualInput;
    [SerializeField] private GranButton _GranButton_Next;
    [SerializeField] private Button _btn_Help;
    [SerializeField] private GameObject _gameObject_AndroidWifiSSIDPlaceholder;
    [SerializeField] private GameObject _gameObject_Loading;
    [Header("Input SSID Modal")]
    [SerializeField] private GameObject _gObj_InputModal;
    [SerializeField] private GranInputField _inputField_CustomSSID;
    [SerializeField] private GranButton _btn_Ok;
    [SerializeField] private GranButton _btn_Cancel;
    [Header("Failed Modal")]
    [SerializeField] private GameObject _gObj_FailedModal;
    [SerializeField] private GranIconButton _btn_Close;
    [SerializeField] private GranButton _btn_Research;
    [SerializeField] private GranButton _btn_OpenInputModal;
    [Header("WiFi Scan Settings")]
    [SerializeField] private float _wifiScanTimeoutSeconds = 10f;

    private List<string> _wifiOptions = new List<string>();
    private bool _isScanComplete = false;
    /// <summary>
    /// Allowed characters: Pinyin (a-z), numbers (0-9), symbols (common punctuation), spaces
    /// </summary>
    private string _pattern => @"[^a-zA-Z0-9!@#\$%\^&\*\(\)_\+\-=\[\]{};':"",.<>\/?`~ ]";

    void Awake()
    {
        _granHeader.onClickLeftIcon1.Subscribe(async _ => await OnBackButtonClicked())
            .RegisterTo(destroyCancellationToken);
        _granHeader.onClickRightIcon1.Subscribe(async _ => await OnHelpClicked())
            .RegisterTo(destroyCancellationToken);
        _btn_ManualInput.onClickAsObservable.Subscribe(_ =>
        {
            _gObj_InputModal.SetActive(true);
        }).RegisterTo(destroyCancellationToken);
        _GranButton_Next.onClickAsObservable.Subscribe(async _ => await OnNextClicked())
            .RegisterTo(destroyCancellationToken);
        _btn_Ok.onClickAsObservable.Subscribe(_ => OnInputCustomSSIDOkClicked(_)).RegisterTo(destroyCancellationToken);
        _btn_Cancel.onClickAsObservable.Subscribe(_ => OnInputCustomSSIDCancelClicked(_)).RegisterTo(destroyCancellationToken);
        _btn_Help.OnClickAsObservable().Subscribe(async _ => await OnHelpClicked()).RegisterTo(destroyCancellationToken);

        _GranInputFieldiOS_SSID.onValueChanged.AddListener(val =>
        {
            _GranInputFieldiOS_SSID.text = Regex.Replace(val, _pattern, "");
            _GranButton_Next.interactable =
                !string.IsNullOrEmpty(_GranInputFieldiOS_SSID.text) &&
                !string.IsNullOrEmpty(_GranInputFieldiOS_Password.text);
        });
        _GranInputFieldiOS_Password.onValueChanged.AddListener(val =>
        {
            _GranInputFieldiOS_Password.text = Regex.Replace(val, _pattern, "");
            _GranButton_Next.interactable =
                !string.IsNullOrEmpty(_GranInputFieldiOS_SSID.text) &&
                !string.IsNullOrEmpty(_GranInputFieldiOS_Password.text);
        });
        _DropdownAndroid_SSID.onValueChanged.AddListener(val =>
        {
            if (_DropdownAndroid_SSID.options.Count <= 0)
            {
                _GranButton_Next.interactable = false;
                return;
            }
            // if (val == _DropdownAndroid_SSID.options.Count - 1)
            // {
            //     _GranButton_Next.interactable = false;
            //     _inputField_CustomSSID.text = string.Empty;
            //     // Open the custom input ssid panel
            //     _gObj_InputModal.SetActive(true);
            //     return;
            // }

            _GranButton_Next.interactable =
                !string.IsNullOrEmpty(_DropdownAndroid_SSID.options[_DropdownAndroid_SSID.value].text) &&
                !string.IsNullOrEmpty(_GranInputFieldAndroid_Password.text);
        });
        _GranInputFieldAndroid_Password.onValueChanged.AddListener(val =>
        {
            _GranInputFieldAndroid_Password.text = Regex.Replace(val, _pattern, "");
            if (_DropdownAndroid_SSID.options.Count <= 0)
            {
                _GranButton_Next.interactable = false;
                return;
            }
            _GranButton_Next.interactable =
                !string.IsNullOrEmpty(_DropdownAndroid_SSID.options[_DropdownAndroid_SSID.value].text) &&
                !string.IsNullOrEmpty(_GranInputFieldAndroid_Password.text);
        });
        // _inputField_CustomSSID.onValueChanged.AddListener(val =>
        // {
        //     _inputField_CustomSSID.text = Regex.Replace(val, _pattern, "");
        // });

        _btn_Close.onClickAsObservable.Subscribe(_ =>
        {
            _gObj_FailedModal.SetActive(false);
        }).RegisterTo(destroyCancellationToken);
        
        _btn_Research.onClickAsObservable.Subscribe(_ =>
        {
            _gObj_FailedModal.SetActive(false);
            StartScanWithTimeout().Forget();
        }).RegisterTo(destroyCancellationToken);
        
        _btn_OpenInputModal.onClickAsObservable.Subscribe(_ =>
        {
            _gObj_FailedModal.SetActive(false);
            _gObj_InputModal.SetActive(true);
        }).RegisterTo(destroyCancellationToken);
        
#if !UNITY_EDITOR && UNITY_ANDROID
        _InputArea_Android.gameObject.SetActive(true);
        _InputArea_iOS.gameObject.SetActive(false);
        _btn_ManualInput.gameObject.SetActive(true);
        _GranInputFieldAndroid_Password.text = GranEyeSettingSaver.GranEye_WIFI_Password;
#else
        _InputArea_Android.gameObject.SetActive(false);
        _InputArea_iOS.gameObject.SetActive(true);
        _btn_ManualInput.gameObject.SetActive(false);
        _GranInputFieldiOS_SSID.text = GranEyeSettingSaver.GranEye_WIFI_SSID;
        _GranInputFieldiOS_Password.text = GranEyeSettingSaver.GranEye_WIFI_Password;
#endif

        _GranButton_Next.interactable = !string.IsNullOrEmpty(GranEyeSettingSaver.GranEye_WIFI_SSID) &&
                                        !string.IsNullOrEmpty(GranEyeSettingSaver.GranEye_WIFI_Password);
    }

    private void Start()
    {
        WifiScannerManager.Instance.OnWifiScannerDone.AddListener(UpdateWifiList);
        WifiScannerManager.Instance.OnWifiScannerFail.AddListener(WifiScannerFail);
        
        StartScanWithTimeout().Forget();
    }

    private async UniTask StartScanWithTimeout()
    {
#if !UNITY_EDITOR && UNITY_ANDROID
        _isScanComplete = false;
        
        // Start the WiFi scan
        WifiScannerManager.Instance.StartScan();
        
        // Wait for the timeout period
        await UniTask.Delay((int)(_wifiScanTimeoutSeconds * 1000), cancellationToken: destroyCancellationToken);
        
        // If scan has not completed within the timeout period, trigger the fail handler
        if (!_isScanComplete)
        {
            Log.i("WiFi scan timeout after " + _wifiScanTimeoutSeconds + " seconds");
            if (this != null)
            {
                WifiScannerFail();
            }
        }
#endif
    }

    private void OnDestroy()
    {
        WifiScannerManager.Instance.OnWifiScannerDone.RemoveListener(UpdateWifiList);
        WifiScannerManager.Instance.OnWifiScannerFail.RemoveListener(WifiScannerFail);
    }

    private void UpdateWifiList(List<string> list)
    {
        _isScanComplete = true;
#if !UNITY_EDITOR && UNITY_ANDROID
        _gameObject_Loading.SetActive(false);
        this.CloseTMPDropdown();
#endif
        Log.d("Scan Success");
        _gameObject_AndroidWifiSSIDPlaceholder.SetActive(false);
        _DropdownAndroid_SSID.ClearOptions();
        var index = 0;
        _wifiOptions.Clear();
        for (int i = 0; i < list.Count; i++)
        {
            _wifiOptions.Add(list[i]);
            if (list[i].Equals(GranEyeSettingSaver.GranEye_WIFI_SSID))
            {
                index = i;
            }
        }
        // _wifiOptions.Add("Other");
        _DropdownAndroid_SSID.AddOptions(_wifiOptions);
        _DropdownAndroid_SSID.value = index;
    }

    private void WifiScannerFail()
    {
        _isScanComplete = true;
#if !UNITY_EDITOR && UNITY_ANDROID
        _gameObject_Loading.SetActive(false);
        this.CloseTMPDropdown();
#endif
        Log.i("Scan Fail");
        _DropdownAndroid_SSID.ClearOptions();
        _wifiOptions.Clear();
        // _wifiOptions.Add("Other");
        _DropdownAndroid_SSID.AddOptions(_wifiOptions);
        _DropdownAndroid_SSID.value = 0;
        
        _gObj_FailedModal.SetActive(true);
    }
    
    private void CloseTMPDropdown()
    {
        if (_DropdownAndroid_SSID != null && _DropdownAndroid_SSID.gameObject.activeInHierarchy)
        {
            _DropdownAndroid_SSID.Hide();
        }
    }

    private async UniTask OnBackButtonClicked()
    {
        if (GranEye.IsGuide)
        {
            await PageManager.Instance.Back();
        }
        else
        {
            await PageManager.Instance.BackUntil(PageNames.GranEyeSettingTop);
        }
    }

    private async UniTask OnHelpClicked()
    {
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.GranEyeWifiHelpSheet));
    }

    private async UniTask OnNextClicked()
    {
#if UNITY_EDITOR
        GranEyeSettingSaver.GranEye_WIFI_SSID = "GranEyeUnityTest";
        GranEyeSettingSaver.GranEye_WIFI_Password = "GranEyeUnityTest";
#else
#if UNITY_ANDROID
        GranEyeSettingSaver.GranEye_WIFI_SSID = _DropdownAndroid_SSID.options[_DropdownAndroid_SSID.value].text;
        GranEyeSettingSaver.GranEye_WIFI_Password = _GranInputFieldAndroid_Password.text;
#else
        GranEyeSettingSaver.GranEye_WIFI_SSID = _GranInputFieldiOS_SSID.text;
        GranEyeSettingSaver.GranEye_WIFI_Password = _GranInputFieldiOS_Password.text;
#endif
#endif
        await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeQRScanLeft));
    }

    private void OnInputCustomSSIDOkClicked(Unit _)
    {
        var newSSID = _inputField_CustomSSID.text.Trim();
        if (!string.IsNullOrEmpty(newSSID))
        {
            _DropdownAndroid_SSID.ClearOptions();
            _wifiOptions.Insert(0, _inputField_CustomSSID.text.Trim());
            _DropdownAndroid_SSID.AddOptions(_wifiOptions);
            _DropdownAndroid_SSID.value = 0;
        }
        _gObj_InputModal.SetActive(false);
        _gameObject_AndroidWifiSSIDPlaceholder.SetActive(false);
    }

    private void OnInputCustomSSIDCancelClicked(Unit _)
    {
        _gObj_InputModal.SetActive(false);
    }
}