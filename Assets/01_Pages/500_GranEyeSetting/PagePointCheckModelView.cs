using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using AeLa.EasyFeedback;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.graneye;
using com.luxza.graneye.exception;
using com.luxza.granlog;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class PagePointCheckModelView : MonoBehaviour
{
    [SerializeField] private GranButton _GranButton_Next;
    [SerializeField] private GranButton _GranButton_Cancel;
    [SerializeField] private GranButton _GranButton_Left;
    [SerializeField] private GranButton _GranButton_Right;
    [SerializeField] private GranButton _GranButton_Up;
    [SerializeField] private GranButton _GranButton_Down;
    [SerializeField] private GranText _GranText_Value;
    [SerializeField] private GranText _GranText_Set;
    [SerializeField] private RawImage _RImg_Preview;
    [SerializeField] private RawImage _RImg_Zoom;
    [SerializeField] private Page _page;
    [SerializeField] private GranIconButton _btn_Close;
    [SerializeField] private RectTransform _UIControlPoint;
    [SerializeField] private GranButton _btn_BugReport;
    [SerializeField] private Button _btn_ReloadCameraTex;

    /// <summary>
    /// 移动方向枚举
    /// </summary>
    private enum MoveDirection
    {
        Left,
        Right,
        Up,
        Down
    }

    private GranEyeSettingParameter _parameter;
    private int _pointIndex = -1;
    private float _fineTuneSize => 1f;
    private int _cameraTryGetTexCount = 0;
    
    /// <summary>
    /// 动画持续时间（秒）
    /// </summary>
    private const float AnimationDuration = 0.25f;
    
    /// <summary>
    /// 平滑动画的当前Coroutine引用
    /// </summary>
    private Coroutine _currentAnimationCoroutine;
    
    private GranEyeCore.CameraPos _cameraPos = GranEyeCore.CameraPos.Left;
    private Texture2D _sourceTexture;
    /// <summary>
    /// Get the zoomed image
    /// </summary>
    private Vector2 _lastCenterPoint; // 记录上一次的中心点
    private Dictionary<int,Vector3> _DicCalibrationPointList = new();
    
    private Vector2 _relativeOffset = Vector2.zero; // 添加这个变量来记录相对于overflow后位置的偏移
    
    /// <summary>
    /// AI预测的偏移量
    /// </summary>
    private Vector2 _aiPredictedOffset = Vector2.zero;

    private void Awake()
    {
        _btn_Close.onClickAsObservable.Subscribe(async _ =>
        {
            ClearAll();
            await PageManager.Instance.CloseModalAsync(_page);
        }).RegisterTo(destroyCancellationToken);
        _GranButton_Next.onClickAsObservable.Subscribe(async _ => await OnNextClicked()).RegisterTo(destroyCancellationToken);
        _GranButton_Cancel.onClickAsObservable.Subscribe(async _ => await OnCancelClicked()).RegisterTo(destroyCancellationToken);
        _GranButton_Left.onClickAsObservable.Subscribe(_ => OnDirectionClicked(MoveDirection.Left)).RegisterTo(destroyCancellationToken);
        _GranButton_Right.onClickAsObservable.Subscribe(_ => OnDirectionClicked(MoveDirection.Right)).RegisterTo(destroyCancellationToken);
        _GranButton_Up.onClickAsObservable.Subscribe(_ => OnDirectionClicked(MoveDirection.Up)).RegisterTo(destroyCancellationToken);
        _GranButton_Down.onClickAsObservable.Subscribe(_ => OnDirectionClicked(MoveDirection.Down)).RegisterTo(destroyCancellationToken);
        _btn_BugReport.onClickAsObservable.Subscribe(_ => OnBugReportClicked()).RegisterTo(destroyCancellationToken);
#if Production
        _btn_BugReport.gameObject.SetActive(Application.version.Split('.')[0].Equals("0"));
#endif
        _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        
        _btn_ReloadCameraTex.onClick.AddListener(async () =>
        {
            _btn_ReloadCameraTex.gameObject.SetActive(false);
            await TakePicture();
        });
    }

    private async void OnBugReportClicked()
    {
        FeedbackForm feedbackForm = FindFirstObjectByType<FeedbackForm>();
        if (feedbackForm == null)
        {
            Log.e("FeedbackForm is not found");
            return;
        }
        feedbackForm.Show();
        var tBack_Left = await GranEye.GranEyeCore.GetImage(GranEyeCore.CameraPos.Left);
        if (tBack_Left.IsSuccess && tBack_Left.Texture != null)
        {
            var tLeftCameraData = (tBack_Left.Texture as Texture2D).EncodeToJPG();
            feedbackForm.CurrentReport.AttachFile("LeftCamera", tLeftCameraData);
        }
        var tBack_Right = await GranEye.GranEyeCore.GetImage(GranEyeCore.CameraPos.Right);
        if (tBack_Right.IsSuccess && tBack_Right.Texture != null)
        {
            var tRightCameraData = (tBack_Right.Texture as Texture2D).EncodeToJPG();
            feedbackForm.CurrentReport.AttachFile("RightCamera", tRightCameraData);
        }
    }

    private async void OnActivate(Unit _)
    {
        if(!_page.Meta.TryGetParameter<GranEyeSettingParameter>(out var parameter)) {
            throw new ArgumentException("CheckoutModalParameter is required.");
        }
        _parameter = parameter;
        Log.d($"get parameter");
        _cameraPos = parameter.CameraPos;
        if (parameter.Texture != null)
        {
            _RImg_Preview.texture = parameter.Texture;
            _RImg_Preview.color = Color.white;
            _RImg_Zoom.color = Color.white;
            await InitializeAfterImageReady();
        }
        else
        {
            if (GranEye.IsReloadTest)
            {
                _btn_ReloadCameraTex.gameObject.SetActive(true);
            }
            else
            {
                await TakePicture();
            }
        }
    }
    
    /// <summary>
    /// 在图像获取完成后执行初始化流程
    /// </summary>
    private async UniTask InitializeAfterImageReady()
    {
        _pointIndex = 0;
        StartSetCalibrationPointById();
        await OnAIOffset();
    }

    private async UniTask OnNextClicked()
    {
        var sourceWidth = _RImg_Preview.texture.width;   // 2048

        // 使用相对偏移来计算
        var pixelOffsetX = _relativeOffset.x;
        var pixelOffsetY = _relativeOffset.y;
        var normalizedOffsetX = pixelOffsetX / sourceWidth;
        var normalizedOffsetY = pixelOffsetY / sourceWidth;

        var finalPos = new Vector2(
            _lastCenterPoint.x + normalizedOffsetX,
            _lastCenterPoint.y + normalizedOffsetY
        );
        GranEye.GranEyeCore.Set_Dot(_cameraPos, CalibrationType.Auto, _pointIndex, finalPos);

        if (_DicCalibrationPointList.ContainsKey(_pointIndex))
        {
            _DicCalibrationPointList[_pointIndex] = new Vector3(finalPos.x, finalPos.y, 0);
        }
        else
        {
            _DicCalibrationPointList.Add(_pointIndex, new Vector3(finalPos.x, finalPos.y, 0));
        }
        if (_pointIndex < 3)
        {
            _pointIndex++;
            StartSetCalibrationPointById();
            _GranButton_Next.interactable = false;
            await OnAIOffset();
            _GranButton_Next.interactable = true;
            return;
        }
        _DicCalibrationPointList = _DicCalibrationPointList.OrderBy(pair => pair.Key)
            .ToDictionary(pair => pair.Key, pair => pair.Value);
        GranEye.GranEyeCore.Calibration(_cameraPos, _DicCalibrationPointList.Values.ToList());
        await PageManager.Instance.CloseModalAsync(_page);
    }

    private async UniTask OnCancelClicked()
    {
        ClearAll();
        await PageManager.Instance.CloseModalAsync(_page);
    }

    private void OnDirectionClicked(MoveDirection direction)
    {
        Vector2 offset = direction switch
        {
            MoveDirection.Left => new Vector2(-_fineTuneSize, 0),
            MoveDirection.Right => new Vector2(_fineTuneSize, 0),
            MoveDirection.Up => new Vector2(0, _fineTuneSize),
            MoveDirection.Down => new Vector2(0, -_fineTuneSize),
            _ => Vector2.zero
        };
        
        AdjustCurrentPointPosition(offset);
    }

    private void AdjustCurrentPointPosition(Vector2 offset)
    {
        if (_pointIndex == -1) return;
        // Cumulative relative offset
        _relativeOffset += offset;
        _UIControlPoint.anchoredPosition += offset;
    }

    private void StartSetCalibrationPointById()
    {
        _GranText_Value.text = _pointIndex > 3 ? "4/4" : $"{_pointIndex + 1}/4";
        _GranText_Set.text = _pointIndex == 4 ? "Mapping" : "Set";

        var dot = GranEye.GranEyeCore.Get_Dot(_cameraPos, CalibrationType.Auto, _pointIndex);
        var pos = new Vector3(dot.x, dot.y, 0);
        
        _lastCenterPoint = pos;
        _UIControlPoint.anchoredPosition = Vector2.zero;

        UpdateZoomView(_lastCenterPoint);
    }

    /// <summary>
    /// Update magnified view
    /// </summary>
    private void UpdateZoomView(Vector2 centerPoint)
    {
        try
        {
            var calibrationTool = GranEye.GranEyeCore.GetCalibrationTool(_cameraPos);
            var virtualPoint = calibrationTool.GetVirtualPointFromReal(centerPoint);

            var result = GranEye.GranEyeCore.GetZoomedTexture((int)_cameraPos, _RImg_Preview.texture, virtualPoint);
            _RImg_Zoom.texture = result.texture;
            _RImg_Zoom.color = Color.white;
            // Use result.overflowX and result.overflowY to adjust the position of _UIControlPoint
            _UIControlPoint.anchoredPosition += new Vector2(result.overflowX, result.overflowY);
            // Reset relative offset
            _relativeOffset = Vector2.zero;
        }
        catch (GranEyeHomoCoreIsNotInitException)
        {
            if (GranEye.GranEyeCore.Has_Dot(_cameraPos, CalibrationType.Auto, _pointIndex))
            {
                var result = GetZoomedTexture(_RImg_Preview.texture, centerPoint);
                _RImg_Zoom.texture = result.Item1;
                _RImg_Zoom.color = Color.white;
                // Use result.overflowX and result.overflowY to adjust the position of _UIControlPoint
                _UIControlPoint.anchoredPosition += new Vector2(result.Item2, result.Item3);
                // Reset relative offset
                _relativeOffset = Vector2.zero;
            }
            else
            {
                MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Systems.GranEyeHomoCoreIsNotInitError));
            }
        }
    }
    
    public (Texture2D, float, float) GetZoomedTexture(Texture sourceTexture, Vector2 pos, float targetSize = 320f)
    {
        var newRealPos = TflitePositionHelper.ConvertToPixelCoordinates(pos, sourceTexture.width);

        var centerX = newRealPos.x + sourceTexture.width / 2.0f;
        var centerY = newRealPos.y + sourceTexture.height / 2.0f;

        var halfSize = targetSize / 2.0f;

        // Record original position
        var originalSrcX = centerX - halfSize;
        var originalSrcY = centerY - halfSize;

        // Calculate the starting position of the source area and handle boundaries
        var srcX = Mathf.Clamp(centerX - halfSize, 0, sourceTexture.width - targetSize);
        var srcY = Mathf.Clamp(centerY - halfSize, 0, sourceTexture.height - targetSize);

        // Calculate the value exceeding the boundary
        var overflowX = originalSrcX - srcX; // 记录调整的值
        var overflowY = originalSrcY - srcY; // 记录调整的值

        // Create target texture
        var zoomedTexture = new Texture2D((int)targetSize, (int)targetSize, TextureFormat.RGBA32, false);
        zoomedTexture.filterMode = FilterMode.Point;

        // Create a temporary RenderTexture for Graphics.Blit operation
        var tempRT = RenderTexture.GetTemporary(sourceTexture.width, sourceTexture.height, 0);
        Graphics.Blit(sourceTexture, tempRT);

        // Read pixels
        RenderTexture.active = tempRT;
        zoomedTexture.ReadPixels(new Rect(srcX, srcY, targetSize, targetSize), 0, 0);
        zoomedTexture.Apply();

        // Restore the previously active RenderTexture
        RenderTexture.active = null;
        RenderTexture.ReleaseTemporary(tempRT);

        return (zoomedTexture, overflowX, overflowY);
    }

    private void ClearAll()
    {
        _UIControlPoint.gameObject.SetActive(false);
        _DicCalibrationPointList.Clear();
    }
    
    private async UniTask OnAIOffset()
    {
        if (_pointIndex == -1 || _RImg_Zoom.texture == null) return;
        var predictResults = await GranEye.GranEyeCore.PredictDartZoom(_RImg_Zoom.texture);
        
        if (predictResults == null || predictResults.Count == 0)
        {
            Log.e("No prediction results from PredictDartZoom");
            return;
        }
        
        var bestPrediction = predictResults.OrderByDescending(p => p.score).First();
        
        // 使用ModelPos2UnityPos将模型坐标转换为Unity坐标系（归一化，原点在中心）
        var unityPos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(bestPrediction.x, bestPrediction.y));
        
        // 使用RectTransform的实际尺寸，而不是纹理尺寸
        var rectSize = _RImg_Zoom.rectTransform.rect.size;
        
        // 统一坐标系统，确保一致性
        // 将归一化坐标（-0.5到0.5范围，中心为原点）转换为UI坐标
        var pixPos = new Vector2(
            unityPos.x * rectSize.x,
            unityPos.y * rectSize.y
        );
        
        // 应用校正因子处理系统性偏差
        var correctedPixPos = new Vector2(
            pixPos.x,
            pixPos.y
        );
        
        _aiPredictedOffset = correctedPixPos;
        
        // 计算AI预测位置与当前控制点位置的差异
        var deltaOffset = _aiPredictedOffset - _UIControlPoint.anchoredPosition;
        
        // 移动控制点到预测位置
        AnimateToPosition(deltaOffset);
    }
    
    /// <summary>
    /// 将控制点移动到目标位置
    /// </summary>
    private void AnimateToPosition(Vector2 offset)
    {
        // 停止任何正在执行的动画
        if (_currentAnimationCoroutine != null)
        {
            StopCoroutine(_currentAnimationCoroutine);
            _currentAnimationCoroutine = null;
        }
        
        // 如果移动距离很小，直接设置位置
        if (offset.magnitude < 2f)
        {
            _relativeOffset += offset;
            _UIControlPoint.anchoredPosition += offset;
            return;
        }
        
        // 开始简化的平滑动画
        _currentAnimationCoroutine = StartCoroutine(SimpleAnimatePosition(offset));
    }
    
    /// <summary>
    /// 简化的平滑动画协程
    /// </summary>
    private IEnumerator SimpleAnimatePosition(Vector2 offset)
    {
        var startPosition = _UIControlPoint.anchoredPosition;
        var targetPosition = startPosition + offset;
        var startTime = Time.realtimeSinceStartup;
        
        // 主动画循环
        while (Time.realtimeSinceStartup - startTime < AnimationDuration)
        {
            // 计算已用时间比例
            var timeRatio = (Time.realtimeSinceStartup - startTime) / AnimationDuration;
            // 使用内置的平滑函数
            var smoothRatio = Mathf.SmoothStep(0, 1, timeRatio);
            
            // 计算新位置
            var newPosition = Vector2.Lerp(startPosition, targetPosition, smoothRatio);
            
            // 更新位置
            var delta = newPosition - _UIControlPoint.anchoredPosition;
            _relativeOffset += delta;
            _UIControlPoint.anchoredPosition = newPosition;
            
            yield return null;
        }
        
        // 确保最终位置精确
        var finalDelta = targetPosition - _UIControlPoint.anchoredPosition;
        _relativeOffset += finalDelta;
        _UIControlPoint.anchoredPosition = targetPosition;
        
        _currentAnimationCoroutine = null;
    }
    
    private async UniTask TakePicture(bool addFailCount = true)
    {
        if (_RImg_Preview.texture == null)
        {
            Log.d($"TakePicture");
            if (GranEye.IsUSB)
            {
                GranEye.GranEyeCore.GetPictureSingle(_cameraPos == GranEyeCore.CameraPos.Left ? 1 : 2);
            }
            else
            {
                var cameraRespondData = await GranEye.GranEyeCore.GetImage(_cameraPos);
                if (cameraRespondData.IsSuccess)
                {
                    _btn_ReloadCameraTex.gameObject.SetActive(false);
                    _RImg_Preview.texture = cameraRespondData.Texture;
                    _RImg_Preview.color = Color.white;
                    _RImg_Zoom.color = Color.white;
                    await InitializeAfterImageReady();
                }
                else
                {
                    _btn_ReloadCameraTex.gameObject.SetActive(true);
                    if (!addFailCount)
                    {
                        return;
                    }
                    _cameraTryGetTexCount++;
                    if (_cameraTryGetTexCount == GranEye.MaxGetTexCount)
                    {
                        _cameraTryGetTexCount = 0;
                        MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Tips.RestartCamera),
                            iconType: GranModal.IconType.OK,
                            positive: (
                                LocalizeString.GetLocalizedString(LocalizeKey.Labels.OK), async () =>
                                {
                                    var result = await GranEye.GranEyeCore.GetCamera(_cameraPos).Restart();
                                    if(result)
                                    {
                                        MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.CameraRestartCheck),
                                            iconType: GranModal.IconType.OK,
                                            positive: (
                                                LocalizeString.GetLocalizedString(LocalizeKey.Labels.OK), async () =>
                                                {
                                                    await TakePicture(false);
                                                }));
                                    }
                                }),
                            negative: (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Cancel), async () => { }));
                    }
                }
            }
        }
    }
}