using System.Threading;
using Cysharp.Threading.Tasks;
using R3;
using UnityEditor;
using UnityEngine;

namespace com.luxza.ui.utils
{
    public class ScreenSizeWatcher
    {
        private static ScreenSizeWatcher _instance;
        public static ScreenSizeWatcher Instance => _instance ??= new ScreenSizeWatcher();

        private readonly ReactiveProperty<Vector2Int> onScreenSizeChanged;
        public readonly ReadOnlyReactiveProperty<Vector2Int> OnScreenSizeChanged;

        private ScreenSizeWatcher()
        {
#if UNITY_EDITOR
            if (EditorApplication.isPlaying)
            {
                Observable.EveryUpdate().ForEachAsync(_ =>
                {
                    CheckUpdateScreenSize();
                });
            }
            else
            {
                EditorApplication.update += () =>
                {
                    CheckUpdateScreenSize();
                };
            }
#else
            Observable.EveryUpdate().ForEachAsync(_ =>
            {
                CheckUpdateScreenSize();
            });
#endif
            onScreenSizeChanged = new ReactiveProperty<Vector2Int>(Application.isEditor ? new Vector2Int(Screen.currentResolution.width, Screen.currentResolution.height) : new Vector2Int(Screen.width, Screen.height));
            OnScreenSizeChanged = onScreenSizeChanged.ToReadOnlyReactiveProperty();
        }

        private void CheckUpdateScreenSize()
        {
            var screenSize = Application.isEditor ? new Vector2Int(Screen.currentResolution.width, Screen.currentResolution.height) : new Vector2Int(Screen.width, Screen.height);
            if (onScreenSizeChanged.CurrentValue != screenSize)
            {
                Debug.Log($"ScreenSizeWatcher: Screen size changed! from {onScreenSizeChanged.CurrentValue} to {screenSize.x}x{screenSize.y}");
                onScreenSizeChanged.Value = screenSize;
            }
        }
    }
}