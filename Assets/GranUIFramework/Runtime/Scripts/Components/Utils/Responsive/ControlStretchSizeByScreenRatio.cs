using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace com.luxza.ui.components.utils.responsive
{
    [AddComponentMenu("Gran UI Framework/Responsive/RectTransform/Control Stretch Size By Screen Ratio")]
    [RequireComponent(typeof(RectTransform))]
    public class ControlStretchSizeByScreenRatio : ScreenSizeChangeListener
    {
        [SerializeField]
        private List<ConditionsByRatio> _conditions = ConditionsByRatioBase.DefaultBreakPoints.Select(v =>
        {
            return new ConditionsByRatio
            {
                _name = v._name,
                _breakPoint = v._breakPoint,
                _stretchSize = Vector4.zero
            };
        }).ToList();

        [SerializeField]
        private Vector4 _defaultStretchSize;

        [Serializable]
        private class ConditionsByRatio : ConditionsByRatioBase
        {
            [SerializeField]
            public Vector4 _stretchSize;
        }

        protected override void Initialized()
        {
            var rect = GetComponent<RectTransform>();
            _defaultStretchSize = new Vector4(
                rect.offsetMin.x,
                rect.offsetMin.y,
                -rect.offsetMax.x,
                -rect.offsetMax.y);
        }

        protected override void OnScreenSizeChanged()
        {
            var rect = GetComponent<RectTransform>();
            foreach (var c in _conditions.Where(c => IsGreaterThanBy(c._breakPoint.x) && IsLessThanBy(c._breakPoint.y)))
            {
                rect.offsetMin = new Vector2(c._stretchSize.x, c._stretchSize.y);
                rect.offsetMax = new Vector2(-c._stretchSize.z, -c._stretchSize.w);
                return;
            }

            rect.offsetMin = new Vector2(_defaultStretchSize.x, _defaultStretchSize.y);
            rect.offsetMax = new Vector2(-_defaultStretchSize.z, -_defaultStretchSize.w);
        }
    }

}