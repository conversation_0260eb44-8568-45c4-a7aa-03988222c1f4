using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Linq;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace com.luxza.ui.components.utils.responsive
{
    [RequireComponent(typeof(CanvasScaler))]
    [AddComponentMenu("Gran UI Framework/Responsive/Canvas/Control CanvasScaler By Screen Ratio")]
    public class ControlCanvasScalerByScreenRatio : ScreenSizeChangeListener
    {
        [SerializeField]
        private List<ConditionsByRatio> _conditions = ConditionsByRatioBase.DefaultBreakPoints.Select(v =>
        {
            return new ConditionsByRatio
            {
                _name = v._name,
                _breakPoint = v._breakPoint,
                _canvasScalerValue = 0f
            };
        }).ToList();

        [Header("Canvas scale with default")]
        [Range(0f, 1f)]
        [SerializeField] private float _defaultScale = 0.5f;

        private CanvasScaler __scaler;

        private CanvasScaler _scaler
        {
            get
            {
                if (__scaler == null) __scaler = GetComponent<CanvasScaler>();
                return __scaler;
            }
        }

        [Serializable]
        private class ConditionsByRatio : ConditionsByRatioBase
        {
            [SerializeField]
            [Range(0f, 1f)]
            public float _canvasScalerValue;
        }

        protected override void Initialized()
        {
            _defaultScale = GetComponent<CanvasScaler>().matchWidthOrHeight;
        }

        protected override void OnScreenSizeChanged()
        {
            foreach (var c in _conditions)
            {
                if (IsGreaterThanBy(c._breakPoint.x) && IsLessThanBy(c._breakPoint.y))
                {
                    _scaler.matchWidthOrHeight = c._canvasScalerValue;
                    return;
                }
            }

            _scaler.matchWidthOrHeight = _defaultScale;
        }
    }
}
