using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace com.luxza.ui.components.utils.responsive
{
    [AddComponentMenu("Gran UI Framework/Responsive/GameObject/Control GameObject Activation By Screen Ratio")]
    public class ControlGameObjectActivationByScreenRatio : ScreenSizeChangeListener
    {
        [SerializeField] private GameObject _target;
        [SerializeField]
        private List<ConditionsByRatio> _conditions = ConditionsByRatioBase.DefaultBreakPoints.Select(v =>
        {
            return new ConditionsByRatio
            {
                _name = v._name,
                _breakPoint = v._breakPoint,
                _enable = true
            };
        }).ToList();
        [Header("GameObject activation with default")]
        [SerializeField] private bool _defaultActivation = true;

        [Serializable]
        private class ConditionsByRatio : ConditionsByRatioBase
        {
            [SerializeField]
            public bool _enable;
        }

        protected override void Initialized()
        {
            if (_target == null)
            {
                _defaultActivation = false;
                return;
            }
            _defaultActivation = _target.activeSelf;
        }

        protected override void OnScreenSizeChanged()
        {
            if (_target == null) return;
            foreach (var c in _conditions)
            {
                if (IsGreaterThanBy(c._breakPoint.x) && IsLessThanBy(c._breakPoint.y))
                {
                    _target.SetActive(c._enable);
                    return;
                }
            }

            _target.SetActive(_defaultActivation);
        }
    }
}
