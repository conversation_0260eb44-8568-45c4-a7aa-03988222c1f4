using com.luxza.ui.utils;
using UnityEngine;
using R3;

namespace com.luxza.ui.components.utils.responsive
{
    /// <summary>
    /// 現在のScreenSizeから、何かの操作を切り分けるコンポーネントです。
    /// 実際の操作はこのコンポーネントを継承したコンポーネントが担当します。
    /// このコンポーネントは、操作をするタイミングを指定するためのものです。
    ///
    /// たとえば、Editorであれば、GameViewのサイズを変更することが、イベントの発火のトリガーとなります。
    /// </summary>
    [ExecuteAlways]
    public abstract class ScreenSizeChangeListener : MonoBehaviour
    {
        private double _currentRatio = 0;

        protected abstract void Initialized();

        protected bool IsLessThanBy(float edge)
        {
            return _currentRatio <= edge;
        }

        protected bool IsGreaterThanBy(float edge)
        {
            return _currentRatio >= edge;
        }

        protected virtual void Awake()

        {
            Initialized();
            ScreenSizeWatcher.Instance.OnScreenSizeChanged.Subscribe(v =>
            {
                _currentRatio = (double)v.x / v.y;
                OnScreenSizeChanged();
            }).AddTo(gameObject);
        }

        protected abstract void OnScreenSizeChanged();
    }
}
