using UnityEngine;
using UnityEngine.UI;
using R3;
using System;
using LitMotion;
using Cysharp.Threading.Tasks;
using System.Threading;
using com.luxza.ui.components.molecules;



#if UNITY_EDITOR
using UnityEditor;
#endif

namespace com.luxza.ui.components.atoms
{
    [ExecuteAlways]
    [RequireComponent(typeof(GranButton))]
    [RequireComponent(typeof(RawImage))]
    public class Portrait : MonoBehaviour
    {
        [SerializeField] private bool _interactable;

        [SerializeField] private bool _loading;

        [SerializeField] private BorderColor _borderColor;

        [SerializeField] private GameObject LoadingObject;

        public enum BorderColor
        {
            Gold,
            Silver,
            Bronze,
            Red,
            Green,
            Blue,
        }

        [SerializeField] Material _goldMaterial;
        [SerializeField] Material _silverMaterial;
        [SerializeField] Material _bronzeMaterial;
        [SerializeField] Material _redMaterial;
        [SerializeField] Material _greenMaterial;
        [SerializeField] Material _blueMaterial;

        public Observable<Unit> onClickAsObservable => _button.onClickAsObservable;

        private GranButton __button;

        private GranButton _button
        {
            get
            {
                if (__button == null)
                {
                    __button = GetComponent<GranButton>();
                }
                return __button;
            }
        }

        private CompositeMotionHandle motionHandles = new CompositeMotionHandle();
        private bool _previousInteractable;
        private bool _previousLoading;

        public bool interactable
        {
            get => _interactable;
            set => _interactable = value;
        }

        public bool loading
        {
            get => _loading;
            set
            {
                _loading = value;
                LoadingObject.SetActive(value);
            }
        }

        void Awake()
        {
            _previousInteractable = _interactable;
            _previousLoading = _loading;
        }

#if UNITY_EDITOR
        void OnValidate()
        {
            if (EditorApplication.isPlayingOrWillChangePlaymode) return;
            if (_previousLoading != loading)
            {
                loading = _loading;
                _previousLoading = _loading;
            }
            if (_previousInteractable != interactable)
            {
                interactable = _interactable;
                _previousInteractable = _interactable;
            }
            SetBorderColor(_borderColor);
        }
#endif

        public async UniTask LoadTexture(string imagePath, CancellationToken cancellationToken, Func<string, CancellationToken, UniTask<Texture2D>> loadImage)
        {
            if (loadImage == null) return;
            loading = true;
            var tex = await loadImage.Invoke(imagePath, cancellationToken);
            SetTexture(tex);
        }

        public void SetTexture(Texture2D texture)
        {
            GetComponent<RawImage>().texture = texture;
            loading = texture == null;
        }

        public void SetBorderColor(BorderColor color)
        {
            GetComponent<RawImage>().material = color switch
            {
                BorderColor.Gold => _goldMaterial,
                BorderColor.Silver => _silverMaterial,
                BorderColor.Bronze => _bronzeMaterial,
                BorderColor.Red => _redMaterial,
                BorderColor.Green => _greenMaterial,
                BorderColor.Blue => _blueMaterial,
                _ => throw new NotSupportedException($"{color} is not supported")
            };
        }
    }
}