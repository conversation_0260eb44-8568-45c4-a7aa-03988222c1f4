using com.luxza.ui.components.atoms;
using com.luxza.ui.utils;
using Cysharp.Threading.Tasks;
using LitMotion;
using R3;
using TMPro;
using UnityEngine;
using UnityEngine.UIElements;
using static com.luxza.ui.components.organizms.GranFooter;

namespace com.luxza.ui.components.organizms
{
    public class GranFooterMenus : MonoBehaviour
    {
        [SerializeField] private GranIconButton _home;
        [SerializeField] private GranIconButton _world;
        [SerializeField] private GranIconButton _localGame;
        [SerializeField] private GranIconButton _tournament;
        [SerializeField] private GranIconButton _menu;

        [SerializeField] private TMP_Text _homeLabel;
        [SerializeField] private TMP_Text _worldLabel;
        [SerializeField] private TMP_Text _localGameLabel;
        [SerializeField] private TMP_Text _tournamentLabel;
        [SerializeField] private TMP_Text _menuLabel;


        [SerializeField] private RectTransform _line;

        [SerializeField] private RectTransform _linePositionForHome;
        [SerializeField] private RectTransform _linePositionForWorld;
        [SerializeField] private RectTransform _linePositionForLocalGame;
        [SerializeField] private RectTransform _linePositionForTournament;
        [SerializeField] private RectTransform _linePositionForMenu;

        [SerializeField]
        private Ease LineMoveAnimationEase = Ease.OutCubic;

        [SerializeField]
        private float LineMoveAnimationDuration = 0.5f;

        [SerializeField]
        private Ease IconMoveAnimationEase = Ease.OutCubic;

        [SerializeField]
        private float IconMoveAnimationDuration = 0.5f;

        [SerializeField] private Color[] menuSelectedColors = new Color[5] { Color.white, Color.white, Color.white, Color.white, Color.white };
        [SerializeField] private Color[] menuDeselectedColors = new Color[5] { Color.gray, Color.gray, Color.gray, Color.gray, Color.gray };

        private CompositeMotionHandle _motionHandle = new CompositeMotionHandle();

        public Observable<Unit> onClickHome => _home.onClickAsObservable;
        public Observable<Unit> onClickWorld => _world.onClickAsObservable;
        public Observable<Unit> onClickLocalGame => _localGame.onClickAsObservable;
        public Observable<Unit> onClickTournament => _tournament.onClickAsObservable;
        public Observable<Unit> onClickMenu => _menu.onClickAsObservable;

        private FooterMenu _currentMenu = GranFooter.FooterMenu.Home;

        private async void Start()
        {
            onClickHome.Subscribe(_ =>
            {
                SelectMenu(FooterMenu.Home);
            }).AddTo(gameObject);
            await UniTask.Yield();
            if (this == null) return;
            onClickWorld.Subscribe(_ =>
            {
                SelectMenu(FooterMenu.World);
            }).AddTo(gameObject);
            await UniTask.Yield();
            if (this == null) return;
            onClickLocalGame.Subscribe(_ =>
            {
                SelectMenu(FooterMenu.LocalGame);
            }).AddTo(gameObject);
            await UniTask.Yield();
            if (this == null) return;
            onClickTournament.Subscribe(_ =>
            {
                SelectMenu(FooterMenu.Tournament);
            }).AddTo(gameObject);
            await UniTask.Yield();
            if (this == null) return;
            onClickMenu.Subscribe(_ =>
            {
                SelectMenu(FooterMenu.Menu);
            }).AddTo(gameObject);
            await UniTask.Yield();
            if (this == null) return;

            UniTask.Void(async () =>
            {
                //Wait for build ui layout.
                await new WaitUntil(() => GetLinePosition(FooterMenu.Home).position.x != 0);
                ApplyCurrentSelection();
            });
        }

        private void OnEnable()
        {
            ApplyCurrentSelection();
        }

        internal void ApplyCurrentSelection()
        {
            DeselectMenuWithoutAnimation(FooterMenu.Home);
            DeselectMenuWithoutAnimation(FooterMenu.World);
            DeselectMenuWithoutAnimation(FooterMenu.LocalGame);
            DeselectMenuWithoutAnimation(FooterMenu.Tournament);
            DeselectMenuWithoutAnimation(FooterMenu.Menu);
            SelectMenuWithoutAnimation(_currentMenu);
        }

        private void SelectMenuWithoutAnimation(FooterMenu footerMenu)
        {
            var icon = GetButton(footerMenu);
            GetMenuLabel(footerMenu).gameObject.SetActive(true);
            icon.rectTransform.anchoredPosition = icon.rectTransform.anchoredPosition.CopyWithY(-12);
            icon.color = menuSelectedColors[(int)footerMenu];

            _line.position = GetLinePosition(footerMenu).position;
        }

        private void DeselectMenuWithoutAnimation(FooterMenu footerMenu)
        {
            var icon = GetButton(footerMenu);
            GetMenuLabel(footerMenu).gameObject.SetActive(false);
            icon.rectTransform.anchoredPosition = icon.rectTransform.anchoredPosition.CopyWithY(-20);
            icon.color = menuDeselectedColors[(int)footerMenu];
        }

        internal void SelectMenuWithoutNotify(FooterMenu footerMenu)
        {
            SelectMenu(footerMenu);
        }

        private void SelectMenu(FooterMenu footerMenu)
        {
            if (_currentMenu == footerMenu) return;
            _motionHandle.Cancel();
            _motionHandle.Complete();
            _motionHandle.Clear();

            DeselectMenu(_currentMenu);
            _currentMenu = footerMenu;

            var icon = GetButton(footerMenu);
            LMotion.Create(icon.rectTransform.anchoredPosition.y, -12, IconMoveAnimationDuration)
                .WithOnComplete(() => GetMenuLabel(footerMenu).gameObject.SetActive(true))
                .WithEase(IconMoveAnimationEase)
                .Bind(x => icon.rectTransform.anchoredPosition = icon.rectTransform.anchoredPosition.CopyWithY(x))
                .AddTo(_motionHandle)
                .AddTo(this.gameObject);

            var position = GetLinePosition(footerMenu);
            var from = _line.position;
            var to = position.position;
            LMotion.Create(from, to, LineMoveAnimationDuration)
                .WithEase(LineMoveAnimationEase)
                .Bind(x =>
                {
                    _line.position = x;
                })
                .AddTo(_motionHandle)
                .AddTo(this.gameObject);

            LMotion.Create(icon.color, menuSelectedColors[(int)footerMenu], IconMoveAnimationDuration)
                .WithEase(IconMoveAnimationEase)
                .Bind(x => icon.color = x)
                .AddTo(_motionHandle)
                .AddTo(this.gameObject);
        }

        private void DeselectMenu(FooterMenu footerMenu)
        {
            var icon = GetButton(footerMenu);
            GetMenuLabel(footerMenu).gameObject.SetActive(false);
            LMotion.Create(icon.rectTransform.anchoredPosition.y, -20, IconMoveAnimationDuration)
                .WithEase(IconMoveAnimationEase)
                .WithOnCancel(() => icon.rectTransform.anchoredPosition = icon.rectTransform.anchoredPosition.CopyWithY(-20))
                .Bind(x => icon.rectTransform.anchoredPosition = icon.rectTransform.anchoredPosition.CopyWithY(x))
                .AddTo(_motionHandle)
                .AddTo(this.gameObject);

            LMotion.Create(icon.color, menuDeselectedColors[(int)footerMenu], IconMoveAnimationDuration)
                .WithEase(IconMoveAnimationEase)
                .WithOnCancel(() => icon.color = menuDeselectedColors[(int)footerMenu])
                .Bind(x => icon.color = x)
                .AddTo(_motionHandle)
                .AddTo(this.gameObject);
        }

        private RectTransform GetLinePosition(FooterMenu footerMenu)
        {
            return footerMenu switch
            {
                FooterMenu.Home => _linePositionForHome,
                FooterMenu.World => _linePositionForWorld,
                FooterMenu.LocalGame => _linePositionForLocalGame,
                FooterMenu.Tournament => _linePositionForTournament,
                FooterMenu.Menu => _linePositionForMenu,
                _ => _linePositionForHome
            };
        }

        private TMP_Text GetMenuLabel(FooterMenu footerMenu)
        {
            return footerMenu switch
            {
                FooterMenu.Home => _homeLabel,
                FooterMenu.World => _worldLabel,
                FooterMenu.LocalGame => _localGameLabel,
                FooterMenu.Tournament => _tournamentLabel,
                FooterMenu.Menu => _menuLabel,
                _ => _homeLabel
            };
        }

        private GranIconButton GetButton(FooterMenu footerMenu)
        {
            return footerMenu switch
            {
                FooterMenu.Home => _home,
                FooterMenu.World => _world,
                FooterMenu.LocalGame => _localGame,
                FooterMenu.Tournament => _tournament,
                FooterMenu.Menu => _menu,
                _ => _home
            };
        }
    }
}