using com.luxza.ui.components.molecules;
using UnityEngine;
using R3;
using System;
using LitMotion;
using Cysharp.Threading.Tasks;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace com.luxza.ui.components.templates.frames
{
    [ExecuteAlways]
    [RequireComponent(typeof(RectTransform))]
    public class AccordionFrame : MonoBehaviour
    {
        [SerializeField] GranButton _openButton;
        [SerializeField] private bool _isOpen;

        [SerializeField] RectTransform _arrowImage;
        [SerializeField] RectTransform _sectionAlwaysShow;
        [SerializeField] RectTransform _sectionHide;
        [SerializeField] RectTransform _contentsOwner;
        private CompositeMotionHandle _motionHandles = new CompositeMotionHandle();

        private RectTransform __rectTransform;

        private RectTransform _rectTransform
        {
            get
            {
                if (__rectTransform == null)
                {
                    __rectTransform = GetComponent<RectTransform>();
                }
                return __rectTransform;
            }
        }

        private void Awake()
        {
#if UNITY_EDITOR
            if (!EditorApplication.isPlaying) return;
#endif
            if (_openButton != null)
            {
                _openButton.onClickAsObservable.Subscribe(_ =>
                {
                    if (isOpen)
                    {
                        Close();
                    }
                    else
                    {
                        Open();
                    }
                    SetOpenWithoutNotify(!isOpen);
                }).RegisterTo(destroyCancellationToken);
            }
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (EditorApplication.isPlayingOrWillChangePlaymode) return;
            SetOpenWithoutNotify(_isOpen);
        }
#endif

        private Action<bool> _onOpenStateChanged;
        public Observable<bool> onOpenStateChanged => Observable.FromEvent<bool>(h => _onOpenStateChanged += h, h => _onOpenStateChanged -= h);

        public bool isOpen
        {
            get => _isOpen;
            set
            {
                _isOpen = value;
                _onOpenStateChanged?.Invoke(_isOpen);
                if (_isOpen)
                {
                    Open();
                }
                else
                {
                    Close();
                }
            }
        }

        public void SetOpenWithoutNotify(bool value)
        {
            _isOpen = value;
            if (_isOpen)
            {
                Open();
            }
            else
            {
                Close();
            }
        }

        private void Open()
        {
            if (_arrowImage != null)
            {
                _motionHandles.Cancel();
                _motionHandles.Complete();
                _sectionHide.gameObject.SetActive(true);
                LMotion.Create(_arrowImage.localRotation, Quaternion.Euler(0, 0, 180), 0.3f)
                    .WithEase(Ease.OutQuint)
                    .AddTo(gameObject)
                    .Bind(v => _arrowImage.localRotation = v)
                    .AddTo(_motionHandles);
                LMotion.Create(_rectTransform.rect.height, DefaultHeight + _sectionHide.rect.height, 0.5f)
                    .WithEase(Ease.OutQuint)
                    .AddTo(gameObject)
                    .Bind(v => _rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, v))
                    .AddTo(_motionHandles);
            }
        }

        private void Close()
        {
            if (_arrowImage != null)
            {
                _motionHandles.Cancel();
                _motionHandles.Complete();
                LMotion.Create(_arrowImage.localRotation, Quaternion.Euler(0, 0, 0), 0.3f)
                    .WithEase(Ease.OutQuint)
                    .AddTo(gameObject)
                    .Bind(v => _arrowImage.localRotation = v)
                    .AddTo(_motionHandles);
                LMotion.Create(_rectTransform.rect.height, DefaultHeight, 0.5f)
                    .WithEase(Ease.OutQuint)
                    .WithOnComplete(() =>
                    {
                        if (_sectionHide != null)
                        {
                            _sectionHide.gameObject.SetActive(false);
                        }
                    })
                    .AddTo(gameObject)
                    .Bind(v => _rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, v))
                    .AddTo(_motionHandles);
            }
        }

        private float DefaultHeight
        {
            get
            {
                if (_sectionAlwaysShow == null) return 0;
                return _sectionAlwaysShow.rect.height + _contentsOwner.offsetMax.y + _contentsOwner.offsetMin.y;
            }
        }
    }
}