using System.Linq;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.round
{
    /// <summary>
    /// Visit入力
    ///
    /// 内部的にはVisit入力ですが、入力方式として、Darts入力のような入力方法があります。
    /// S10,T20,D13のようなものです。この場合、シングルのInner、Outerは考慮されないため、厳密なDarts入力ではありません。
    /// そのため、この入力はVisit入力として扱います。
    ///
    /// Roundの途中でこの入力方式を切り替えることはできません。
    /// Roundの開始時のみ可能です。
    /// </summary>
    public class VisitInput : ARoundInput
    {
        public int Score => _score ?? 0;
        protected int? _score;

        /// <summary>
        /// チェックアウトを試みた回数
        /// </summary>
        public int CheckOutTryCount = 0;
        /// <summary>
        /// チェックアウトした投擲が何本目か
        /// </summary>
        public int CheckOutNumber = 0;

        public override void End()
        {
            if (!_score.HasValue)
            {
                _score = 0;
            }
        }

        public override RoundInputType InputType { get; }

        internal VisitInput(int throwCountPerRound) : base(throwCountPerRound)
        {
            InputType = RoundInputType.Visit;
            _score = null;
        }

        public void Input(int score)
        {
            if (!IsAcceptableInput())
            {
                throw new InvalidRoundInput("Visit input has not been acceptable in this round for now.");
            }
            _score = score;
        }

        public override void DiscardLatestInput()
        {
            _score = null;
        }

        public override void DiscardAllInput()
        {
            _score = null;
        }

        public override bool HasNoInput()
        {
            return !_score.HasValue;
        }

        public override int ThrowsCount()
        {
            return HasNoInput() ? 0 : 3;
        }

        public override bool IsAcceptableInput()
        {
            return !_score.HasValue;
        }

        public void ConfirmAsMiss()
        {
            _score = 0;
        }

        public override bool IsAllThrowsFixed()
        {
            return !HasNoInput();
        }

        public override string ComponentName => nameof(VisitInput);
    }
}