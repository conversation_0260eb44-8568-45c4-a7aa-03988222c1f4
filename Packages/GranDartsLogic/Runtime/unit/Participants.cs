using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game
{
    /// <summary>
    /// 試合の参加Unitです。
    /// </summary>
    public class Participants
    {
        private Dictionary<string, Unit> _units;
        private Unit[] _unitArray;

        public Participants(Unit[] units)
        {
            _units = units.ToDictionary(u => u.Id);
            _unitArray = units;
        }

        /// <summary>
        /// 与えられたUnitsを参加Unitとして、チームを考慮した並び順にしたParticipateを作成します。
        /// </summary>
        /// <param name="units"></param>
        /// <returns></returns>
        public static Participants CreateWithConsiderTeam
        (
            Unit[] units
        )
        {
            //一度TeamTag順に並べておく。同じKeyの場合、元のオーダーは保持される
            var ordered = units.OrderBy(u => u.TeamTag);
            // チームを考慮した並び順にする
            var unitsByTeam = ordered.GroupBy(u => u.TeamTag);
            var teamCount = unitsByTeam.Count();
            var sorted = new Unit[units.Length];

            for (int i = 0; i < sorted.Length; i++)
            {
                var team = unitsByTeam.ElementAt(i % teamCount);
                var unit = team.ElementAt(i / teamCount);
                sorted[i] = unit;
            }
            return new Participants(sorted);
        }

        /// <summary>
        /// 参加Unitが保有してるRoundデータを全て削除
        /// </summary>
        public void ResetUnitData()
        {
            foreach (var unit in _units.Values)
            {
                unit.Progress.ResetAllRounds();
            }
        }

        /// <summary>
        /// 参加Unit数です。
        /// </summary>
        public int Count => _units.Count;

        /// <summary>
        /// 参加Unitの順番をランダムに入れ替えます。
        /// </summary>
        public void Shuffle()
        {
            _unitArray = _units.Values.OrderBy(i => Guid.NewGuid()).ToArray();
            _units = _unitArray.ToDictionary(u => u.Id);
        }

        /// <summary>
        /// 指定したUnitIDのUnitを返します。
        /// </summary>
        /// <param name="UnitId">UnitのID</param>
        /// <returns>Unit</returns>
        public Unit Unit(string unitId)
        {
            if (string.IsNullOrEmpty(unitId))
            {
                throw new ArgumentNullException(nameof(unitId), "UnitId cannot be null or empty.");
            }

            if (!_units.ContainsKey(unitId))
            {
                throw new KeyNotFoundException($"Invalid UnitId => {unitId}.");
            }

            return _units[unitId];
        }
        /// <summary>
        /// 参加Unit一覧から、指定したインデックスのUnitを返します。
        /// </summary>
        /// <param name="UnitIndex">Unitのインデックス</param>
        /// <returns>Unit</returns>
        public Unit Unit(int unitIndex) => Unit(UnitId(unitIndex));

        /// <summary>
        /// Find Unit by GranId
        /// </summary>
        /// <param name="granId"></param>
        /// <returns></returns>
        /// <exception cref="NotMemberException"></exception>
        public Unit FindUnitByGranId(string granId)
        {
            foreach (var unit in _units.Values)
            {
                if (unit.IsMember(granId))
                {
                    return unit;
                }
            }

            throw new NotMemberException($"{granId} is not a member of any unit.");
        }

        public bool TryFindUnitByGranId(string granId, out Unit unit)
        {
            foreach (var u in _units.Values)
            {
                if (u.IsMember(granId))
                {
                    unit = u;
                    return true;
                }
            }

            unit = null;
            return false;
        }

        /// <summary>
        /// 指定したインデックスのUnitIDを取得します。
        /// </summary>
        /// <param name="unitIndex">Unitのインデックス</param>
        /// <returns>UnitID</returns>
        public string UnitId(int unitIndex) => _unitArray[unitIndex].Id;

        /// <summary>
        /// すべての参加Unitを取得します。
        /// </summary>
        public Unit[] AllUnits => _unitArray;

        /// <summary>
        /// TeamTagでGroupingされたUnitを取得します。
        /// </summary>
        public IEnumerable<IGrouping<int, Unit>> AllUnitsGroupByTeamTag => _units.Values.GroupBy(u => u.TeamTag);

        /// <summary>
        /// Teamに所属するUnitを返します。
        /// </summary>
        /// <param name="teamTag"></param>
        /// <returns></returns>
        public IEnumerable<Unit> UnitsOnSameTeam(int teamTag) => _units.Values.Where(u => u.TeamTag == teamTag);

        /// <summary>
        /// 指定したUnitのTeamTagを取得します。
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public int TeamTag
        (
            string unitId
        )
        {
            if (string.IsNullOrEmpty(unitId))
            {
                throw new ArgumentNullException(nameof(unitId), "UnitId cannot be null or empty.");
            }
            if (!_units.ContainsKey(unitId))
            {
                throw new KeyNotFoundException($"Invalid UnitId => {unitId}.");
            }
            return _units[unitId].TeamTag;
        }
    }
}