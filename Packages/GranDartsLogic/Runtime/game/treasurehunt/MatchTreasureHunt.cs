using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.interfaces;

namespace com.luxza.grandartslogic.domain.game.treasurehunt
{
    public class MatchTreasureHunt : IMatch<GameRuleTreasureHunt>
    {
        internal MatchTreasureHunt(Unit[] participants, GameRuleTreasureHunt rule)
        {
            _participants = new Participants(participants);
            Rule = rule;
            for (int i = 0; i < participants.Length; i++)
            {
                ParticipantTeams.Unit(i).InitializeProgress(rule.ThrowsPerRound, GameCode);
            }
        }

        public RefereeTreasureHunt CreateReferee()
        {
            return new RefereeTreasureHunt(this);
        }

        private Participants _participants { get; }

        public GameCode GameCode => Rule.Code.Value;
        public GameRuleTreasureHunt Rule { get; }

        public void StoreHit(int teamIndex, Segment actuaryHit, Segment virtualHit, Vector2? hitPosition = null)
        {
            var progress = ParticipantTeams.Unit(teamIndex).Progress;
            progress.FixThrow(actuaryHit, virtualHit, hitPosition);
        }

        public int TeamThrowCountOnLatestRound(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress?.CurrentRound.ThrowCount ?? 0;
        }

        public bool IsReachAllTeamsToMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team =>
                {
                    return team.Progress.CurrentRoundNo >= Rule.MaxRound &&
                           team.Progress.CurrentRound.GetRoundComponent<IRoundInput>().IsAllThrowsFixed();
                });
            }
        }

        public bool IsNextRoundToOverMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team => team.Progress.CurrentRoundNo >= Rule.MaxRound);
            }
        }



        public bool IsReachEndOfRound(int teamIndex)
        {
            return TeamThrowCountOnLatestRound(teamIndex) >= Rule.ThrowsPerRound;
        }

        public Throw LatestThrow(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress.LatestThrow;
        }

        public Participants ParticipantTeams => _participants;

        #region Create TreasureHunt Board

        public Dictionary<Segment, AreaEntity> CreateTreasureHuntBoard()
        {
            var list = new Dictionary<Segment, AreaEntity>();
            TreasureHuntHitArea item = TreasureHuntHitArea.Treasure_box_1;
            if (Rule.IsDebug)
            {
                for (int i = 1; i <= Rule.MaxBoxCount; i++)
                {
                    item = CheckArea(i, item);
                    var tEntity = new AreaEntity((SegmentCode)i, item);
                    list.Add(Segment.FromSegmentCode(tEntity.SegmentCode), tEntity);
                }
            }
            else
            {
                var tNumList = new List<int>();
                for (int i = 1; i <= Rule.MaxBoxCount; i++)
                {
                    tNumList.Add(i);
                }
                var tNumArray = new List<int>();
                Random rand = new Random();
                do
                {
                    var tId = rand.Next(0, tNumList.Count);
                    tNumArray.Add(tNumList[tId]);
                    tNumList.RemoveAt(tId);
                } while (tNumList.Count > 0);

                for (int i = 0; i < Rule.MaxBoxCount; i++)
                {
                    item = CheckArea(i, item);
                    // Log.i($"i = {i},tNumArray[i] = {tNumArray[i]},tHitArea = {tHitArea}");
                    var tEntity = new AreaEntity((SegmentCode)tNumArray[i], item);
                    list.Add(Segment.FromSegmentCode(tEntity.SegmentCode), tEntity);
                }
            }
            return list;
        }

        private TreasureHuntHitArea CheckArea(int i, TreasureHuntHitArea item)
        {
            if (ParticipantTeams.AllUnits.Length == 1)
            {
                switch (i)
                {
                    case 20:
                        item = TreasureHuntHitArea.Treasure_box_2;//10
                        break;
                    case 30:
                        item = TreasureHuntHitArea.Treasure_box_3;//10
                        break;
                    case 40:
                        item = TreasureHuntHitArea.Treasure_box_5;//7
                        break;
                    case 47:
                        item = TreasureHuntHitArea.Diamond;//4
                        break;
                    case 51:
                        item = TreasureHuntHitArea.Gold;//4
                        break;
                    case 55:
                        item = TreasureHuntHitArea.White_skull;//8
                        break;
                    case 63:
                        item = TreasureHuntHitArea.Purple_skull;//6
                        break;
                    case 69:
                        item = TreasureHuntHitArea.Red_skull;//4
                        break;
                    case 73:
                        item = TreasureHuntHitArea.Bomb;//3
                        break;
                    case 76:
                        item = TreasureHuntHitArea.BULL_skull;//3
                        break;
                    case 79:
                        item = TreasureHuntHitArea.Shield;//2
                        break;
                }
            }
            else
            {
                switch (i)
                {
                    case 20:
                        item = TreasureHuntHitArea.Treasure_box_2;//10
                        break;
                    case 30:
                        item = TreasureHuntHitArea.Treasure_box_3;//10
                        break;
                    case 40:
                        item = TreasureHuntHitArea.Treasure_box_5;//7
                        break;
                    case 47:
                        item = TreasureHuntHitArea.Diamond;//3
                        break;
                    case 50:
                        item = TreasureHuntHitArea.Gold;//3
                        break;
                    case 53:
                        item = TreasureHuntHitArea.White_skull;//8
                        break;
                    case 61:
                        item = TreasureHuntHitArea.Purple_skull;//6
                        break;
                    case 67:
                        item = TreasureHuntHitArea.Red_skull;//4
                        break;
                    case 71:
                        item = TreasureHuntHitArea.Bomb;//2
                        break;
                    case 73:
                        item = TreasureHuntHitArea.BULL_skull;//2
                        break;
                    case 75:
                        item = TreasureHuntHitArea.Knife;//2
                        break;
                    case 77:
                        item = TreasureHuntHitArea.Sword;//2
                        break;
                    case 79:
                        item = TreasureHuntHitArea.Shield;//2
                        break;
                }
            }
            return item;
        }

        #endregion
    }
}