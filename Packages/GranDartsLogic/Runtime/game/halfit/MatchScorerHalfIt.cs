using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;
using UnityEngine;

namespace com.luxza.grandartslogic.domain.game.halfit
{
    public class MatchScorerHalfIt : IMatchScorer
    {
        private readonly List<MemberGameData> _teamGameDataList = new List<MemberGameData>();

        internal int InitialScore = 40;

        internal MatchScorerHalfIt(MatchHalfIt match)
        {
            Match = match;
            var teams = match.ParticipantTeams.AllUnits;
            foreach (var t in match.ParticipantTeams.AllUnits)
            {
                foreach (var m in t.AllMember)
                {
                    _teamGameDataList.Add(new MemberGameData(t, m, InitialScore, match.Rule.MaxRound));
                }
            }
        }

        public MatchHalfIt Match { get; }

        public int CurrentScore(string unitId)
        {
            return GetUnitMemberData(unitId).UnitScore;
        }

        public int TotalScoreInRound(string teamId, int roundIndex)
        {
            var round = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex];
            if(round.InputType != RoundInputType.Segment) throw new InvalidOperationException("This Round is not SegmentInput");
            if (round.GetRoundComponent<SegmentInput>().Throws.All(t => t.IsMiss)) { return 0; }
            return round.GetRoundComponent<SegmentInput>().Throws.Sum(t => Score(t.VirtualHitArea));
        }

        public int Score(Segment hit)
        {
            return hit == null ? 0 : _scoreMap[hit];
        }

        public void Store(Segment hit, Unit unit)
        {
            foreach (var member in unit.AllMember)
            {
                GetUnitMemberData(member.GranId).UnitScore += Score(hit);
            }
        }

        /// <summary>
        /// halfitのUnitの合計スコアを半分にする
        /// 複数メンバーがいる場合を考慮する
        /// </summary>
        /// <param name="unit"></param>
        public void HalfScore(Unit unit)
        {
            //Debug.Log("HalfScore .UnitScore:" + unit.Id);

            foreach (var member in unit.AllMember)
            {
                //Debug.Log("before Score:" + CurrentScore(member.GranId));
                var memberGameDatas = GetUnitMemberData(member.GranId);

                float halfscore = (float)CurrentScore(member.GranId) / 2.0f;

                memberGameDatas.UnitScore = (int)Math.Ceiling(halfscore);
                //Debug.Log("after Score:" + CurrentScore(member.GranId));
            }
        }
        public void SetUnitScore(string teamId, int score)
        {
            foreach (var memberGameData in GetUnitData(teamId))
            {
                memberGameData.UnitScore = score;
            }
        }

        /// <summary>
        /// 指定したラウンドのスコアをリセットします。
        /// </summary>
        /// <param name="teamId">チームのID</param>
        /// <param name="roundIndex">ラウンドのインデックス</param>
        internal void ResetScore(string teamId, int roundIndex)
        {
            Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>().DiscardAllInput();
        }
        private MemberGameData[] GetUnitData(string teamId) => _teamGameDataList.Where(data => data.Unit.Id.Equals(teamId)).ToArray();
        private MemberGameData GetUnitMemberData(string granId) => _teamGameDataList.First(s => s.Member.GranId.Equals(granId));//todo teamIdとgranIdの入力を許容できる仕組みにする
        public double? PPRFor100(string granId) => GetUnitMemberData(granId).Stats.StatsFor100.PPR;
        public float KeepRate(string granId) => GetUnitMemberData(granId).Stats.keepRate;
        public float FailRate(string granId) => GetUnitMemberData(granId).Stats.failRate;
        public bool IsNoHalf(string granId) => GetUnitMemberData(granId).Stats.IsNoHalf;
        public int[] KeepArray(string granId) => GetUnitMemberData(granId).Stats.keepArray;
        public int[] FailArray(string granId) => GetUnitMemberData(granId).Stats.failArray;
        public int[] UnitMarkCountArray(string unitId)
        {
            List<int> markCountList = new List<int>();
            for (int i = 0; i < Match.Rule.MaxRound; i++)
            {
                markCountList.Add(Match.ParticipantTeams.Unit(unitId).Progress.Rounds[i].GetRoundComponent<SegmentInput>().Throws.Sum(t => t.VirtualHitArea.Multiplier));
            }
            return markCountList.ToArray();
        }

        /// <summary>
        /// RealTimeStatsの割り出し
        /// サーバにはデータとして送らないがゲーム中の表示に使う
        /// </summary>
        public float RealTimeStatsPPD100(Unit unit, string granId)
        {
            Throw[] throws = unit.AllThrowsByMember(granId);
            var totalScoreByHits = throws.Sum(t => Score(t.VirtualHitArea));
            float ppd = 0;
            if (throws.Length != 0 && totalScoreByHits != 0)
            {
                ppd = (float)totalScoreByHits / (float)throws.Length;
            }
            return (float)Math.Round(ppd, 2, MidpointRounding.AwayFromZero);
        }
        public float RealTimeStatsPPR(Unit unit, string granId)
        {
            Throw[] throws = unit.AllThrowsByMember(granId);
            var totalScoreByHits = throws.Sum(t => Score(t.VirtualHitArea));
            float ppr = 0;
            //Debug.Log($"totalScoreByHits {totalScoreByHits} / Match.MaxRoundCount {Match.MaxRoundCount}");
            if (throws.Length != 0 && totalScoreByHits != 0)
            {
                ppr = (float)totalScoreByHits / (float)throws.Length * 3.0f;
            }

            return (float)Math.Round(ppr, 2, MidpointRounding.AwayFromZero);
        }
        /// <summary>
        /// PPR100%の割り出し
        ///
        /// </summary>
        public void Confirm100PercentStats()
        {
            foreach (var unit in Match.ParticipantTeams.AllUnits)
            {
                foreach (var member in unit.AllMember)
                {
                    var ppr = RealTimeStatsPPR(unit, member.GranId);
                    //Debug.Log("PPR:" + ppr);
                    GetUnitMemberData(member.GranId).Stats.StatsFor100.PPR = ppr;
                }
            }
        }

        /// <summary>
        /// keepとfailしたラウンドとkeep率とfail率を割り出す処理
        /// </summary>
        public void ConfirmHalfItAnalysisData()
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits
                .SelectMany(team => team.AllMember, (team, player) => new
                {
                    teamId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                });

            foreach (var playerPerRound in allplayerRounds)
            {
                int roundindex = 0;
                foreach (var round in playerPerRound.round)
                {

                    GetUnitMemberData(playerPerRound.granId).Stats.keepArray[roundindex] = IsAllHitTargetArea(round) ? 1 : 0;
                    GetUnitMemberData(playerPerRound.granId).Stats.failArray[roundindex] = IsAllHitMiss(round) ? 1 : 0;
                    roundindex++;
                }



                int failCount = GetUnitMemberData(playerPerRound.granId).Stats.failArray.Sum();
                GetUnitMemberData(playerPerRound.granId).Stats.IsNoHalf = failCount == 0;
                float keeprate = (float)GetUnitMemberData(playerPerRound.granId).Stats.keepArray.Sum() / (float)GetUnitMemberData(playerPerRound.granId).Stats.keepArray.Length;
                GetUnitMemberData(playerPerRound.granId).Stats.keepRate = (float)Math.Round(keeprate, 3, MidpointRounding.AwayFromZero);
                float failrate = (float)failCount / (float)GetUnitMemberData(playerPerRound.granId).Stats.failArray.Length;
                GetUnitMemberData(playerPerRound.granId).Stats.failRate = (float)Math.Round(failrate, 3, MidpointRounding.AwayFromZero);
            }
        }

        //このラウンドは全てMissに当たってたかを返す
        public bool IsAllHitMiss(Round round) => round.GetRoundComponent<SegmentInput>().Throws.All(t => t.IsMiss);
        //このラウンドは全てTargetに当たってたかを返す
        public bool IsAllHitTargetArea(Round round) => round.GetRoundComponent<SegmentInput>().Throws.All(t => !t.IsMiss && !t.IsEmpty);

        private readonly Dictionary<Segment, int> _scoreMap = new Dictionary<Segment, int>()
        {
            {Segment.Single1In, 1},
            {Segment.Single1Out, 1},
            {Segment.Double1, 1 * 2},
            {Segment.Triple1, 1 * 3},
            {Segment.Single2In, 2},
            {Segment.Single2Out, 2},
            {Segment.Double2, 2 * 2},
            {Segment.Triple2, 2 * 3},
            {Segment.Single3In, 3},
            {Segment.Single3Out, 3},
            {Segment.Double3, 3 * 2},
            {Segment.Triple3, 3 * 3},
            {Segment.Single4In, 4},
            {Segment.Single4Out, 4},
            {Segment.Double4, 4 * 2},
            {Segment.Triple4, 4 * 3},
            {Segment.Single5In, 5},
            {Segment.Single5Out, 5},
            {Segment.Double5, 5 * 2},
            {Segment.Triple5, 5 * 3},
            {Segment.Single6In, 6},
            {Segment.Single6Out, 6},
            {Segment.Double6, 6 * 2},
            {Segment.Triple6, 6 * 3},
            {Segment.Single7In, 7},
            {Segment.Single7Out, 7},
            {Segment.Double7, 7 * 2},
            {Segment.Triple7, 7 * 3},
            {Segment.Single8In, 8},
            {Segment.Single8Out, 8},
            {Segment.Double8, 8 * 2},
            {Segment.Triple8, 8 * 3},
            {Segment.Single9In, 9},
            {Segment.Single9Out, 9},
            {Segment.Double9, 9 * 2},
            {Segment.Triple9, 9 * 3},
            {Segment.Single10In, 10},
            {Segment.Single10Out, 10},
            {Segment.Double10, 10 * 2},
            {Segment.Triple10, 10 * 3},
            {Segment.Single11In, 11},
            {Segment.Single11Out, 11},
            {Segment.Double11, 11 * 2},
            {Segment.Triple11, 11 * 3},
            {Segment.Single12In, 12},
            {Segment.Single12Out, 12},
            {Segment.Double12, 12 * 2},
            {Segment.Triple12, 12 * 3},
            {Segment.Single13In, 13},
            {Segment.Single13Out, 13},
            {Segment.Double13, 13 * 2},
            {Segment.Triple13, 13 * 3},
            {Segment.Single14In, 14},
            {Segment.Single14Out, 14},
            {Segment.Double14, 14 * 2},
            {Segment.Triple14, 14 * 3},
            {Segment.Single15In, 15},
            {Segment.Single15Out, 15},
            {Segment.Double15, 15 * 2},
            {Segment.Triple15, 15 * 3},
            {Segment.Single16In, 16},
            {Segment.Single16Out, 16},
            {Segment.Double16, 16 * 2},
            {Segment.Triple16, 16 * 3},
            {Segment.Single17In, 17},
            {Segment.Single17Out, 17},
            {Segment.Double17, 17 * 2},
            {Segment.Triple17, 17 * 3},
            {Segment.Single18In, 18},
            {Segment.Single18Out, 18},
            {Segment.Double18, 18 * 2},
            {Segment.Triple18, 18 * 3},
            {Segment.Single19In, 19},
            {Segment.Single19Out, 19},
            {Segment.Double19, 19 * 2},
            {Segment.Triple19, 19 * 3},
            {Segment.Single20In, 20},
            {Segment.Single20Out, 20},
            {Segment.Double20, 20 * 2},
            {Segment.Triple20, 20 * 3},
            {Segment.BullIn, 50},
            {Segment.BullOut, 25},
            {Segment.Miss, 0},
            {Segment.OUT, 0}
        };

        public HalfItRoundSummary[] RoundSummary(string granId)
        {
            var unit = Match.ParticipantTeams.AllUnits.First(u => u.IsMember(granId));
            var rounds = unit.AllRoundsByMember(granId);
            
            return rounds.Select(r =>new HalfItRoundSummary(r, CreateHalfItThrowSammary(r),
                                                            CreateSummaryScore(r.No,r.GetRoundComponent<SegmentInput>().Throws.Sum(t => Score(t.VirtualHitArea)),
                                                            r.GetRoundComponent<SegmentInput>().Throws.Sum(t => t.VirtualHitArea.Multiplier)))).ToArray();

            HalfItThrowSummary[] CreateHalfItThrowSammary(Round round)
            {
                if (round.TryGetRoundComponent<SegmentInput>(out var segment))
                {
                    return segment.Throws.Select(t => new HalfItThrowSummary(t,
                                                        Score(t.VirtualHitArea),
                                                        CreateSummaryScore(round.No,Score(t.VirtualHitArea),
                                                        t.VirtualHitArea.Multiplier))).ToArray();
                }
                else
                {
                    throw new InvalidRoundInput("This Round has no SegmentInput.");
                }
            }
            ThrowSummaryData CreateSummaryScore(int round,int points, int markCount)
            {

                switch (round)
                {
                    case 1:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount15 = markCount,
                                points = points
                            };
                        }
                    case 2:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount16 = markCount,
                                points = points
                            };
                        }
                    case 3:
                        {
                            return new ThrowSummaryData()
                            {
                                markCountDouble = markCount,
                                points = points
                            };
                        }
                    case 4:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount17 = markCount,
                                points = points
                            };
                        }
                    case 5:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount18 = markCount,
                                points = points
                            };
                        }
                    case 6:
                        {
                            return new ThrowSummaryData()
                            {
                                markCountTriple = markCount,
                                points = points
                            };
                        }
                    case 7:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount19 = markCount,
                                points = points
                            };
                        }
                    case 8:
                        {
                            return new ThrowSummaryData()
                            {
                                markCount20 = markCount,
                                points = points
                            };
                        }
                    case 9:
                        {
                            return new ThrowSummaryData()
                            {
                                markCountBull = markCount,
                                points = points
                            };
                        }
                    default:
                        {
                            throw new InvalidOperationException("Invalid round number for HalfItThrowSummary.");
                        }
                }
            }
        }

        private class MemberGameData
        {
            internal readonly Unit Unit;
            internal readonly Player Member;

            internal Stats Stats;
            internal int UnitScore;//halfitに関してはスコアの持ち点が半分になったりするので独自でスコアを持ってる
                                   //unitScoreはメンバーごとに持ってるが、その値はUnitないで同じ数字にする(メンバーごとだと計算ずれたりするので)

            internal MemberGameData(Unit team, Player member, int InitialScore, int maxround)
            {
                Unit = team;
                Member = member;
                Stats = new Stats()
                {
                    StatsFor100 = new HalfItStats(),
                    keepArray = new int[maxround],
                    failArray = new int[maxround],
                };

                UnitScore = InitialScore;
            }


        }

        private struct Stats
        {
            internal HalfItStats StatsFor100;
            internal float failRate;
            internal float keepRate;
            internal int[] keepArray;
            internal int[] failArray;
            internal bool IsNoHalf;
        }
        private struct HalfItStats
        {
            internal double? PPR;
        }


        public int Score(string teamId, int roundIndex, int throwIndex)
        {
            throw new System.NotImplementedException();
        }
    }
}