using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class MatchScorerShangHai : IMatchScorer
    {
        private readonly List<PlayerShanghaiData> _playerdatas = new List<PlayerShanghaiData>();
        private readonly GameRuleShangHai _ruleShangHai;
        public MatchShangHai Match { get; }
        private NotSupportedException _notSupportedInputTypeException =
            new NotSupportedException("Round must be either PerDartsInput or PerVisitInput");
        
        public MatchScorerShangHai(MatchShangHai match)
        {
            Match = match;
            foreach (var t in match.ParticipantTeams.AllUnits)
            {
                foreach (var m in t.AllMember)
                {
                    _playerdatas.Add(new PlayerShanghaiData(t.Id, m.GranId));
                }
            }
        }
        
        public int CurrentScore(string unitId)
        {
            var unit = Match.ParticipantTeams.Unit(unitId);
            return unit.Progress.CurrentTotalScore(Score);
        }

        public int Score(string teamId, int roundIndex, int throwIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            if (roundInput is SegmentInput perDartsInput)
            {
                return Score(perDartsInput.Throws[throwIndex].VirtualHitArea);
            }

            throw _notSupportedInputTypeException;
        }

        public int TotalScoreInRound(string teamId, int roundIndex)
        {
            var roundInput = Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>();
            if (roundInput is SegmentInput
                perDartsInput)
            {
                return perDartsInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }

            if (roundInput is VisitInput perVisitInput)
            {
                return perVisitInput.Score;
            }

            throw _notSupportedInputTypeException;
        }
        
        /// <summary>
        /// Roundのスコアを返します。
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public int TotalScoreInRound(Round round)
        {
            if (round.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                return segmentInput.Throws.Sum(t => Score(t.VirtualHitArea));
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visitInput))
            {
                return visitInput.Score;
            }

            throw new InvalidRoundInput("This round has no SegmentInput or VisitInput");
        }

        public int Score(Segment hit)
        {
            if (hit == null)
            {
                return 0;
            }
            if (hit.IsSingle)
            {
                return 1;
            }
            if (hit.IsDouble)
            {
                return 2;
            }
            if (hit.IsTriple)
            {
                return 3;
            }
            return 0;
        }

        public int GetTotalScore(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.TotalScore);
        }

        public int GetRoundScore(string unitId, int roundIndex)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Sum(p => p.GetRoundScore(roundIndex));
        }

        public bool IsShanghaiAchieved(string unitId)
        {
            return _playerdatas
                .Where(p => p.TeamId == unitId)
                .Any(p => p.IsShanghaiAchieved());
        }

        public void RecordHit(string teamId, string granId, int segmentValue, int roundIndex)
        {
            var playerData = _playerdatas.First(p => p.TeamId == teamId && p.GranId == granId);
            playerData.RecordHit(segmentValue, roundIndex);
        }

        // /// <summary>
        // /// 检查指定单位在指定轮次是否完成Shanghai
        // /// </summary>
        // /// <param name="unitId">单位ID</param>
        // /// <param name="roundIndex">轮次索引（0-based）</param>
        // /// <returns>是否在该轮完成Shanghai</returns>
        // public bool IsShanghaiAchievedInRound(string unitId, int roundIndex)
        // {
        //     return _playerdatas
        //         .Where(p => p.TeamId == unitId)
        //         .Any(p => p.IsShanghaiAchievedInRound(roundIndex));
        // }

        /// <summary>
        /// 获取指定单位完成Shanghai的轮次
        /// </summary>
        /// <param name="unitId">单位ID</param>
        /// <returns>完成Shanghai的轮次索引，如果没有则返回-1</returns>
        public int GetShanghaiCompletedRound(string unitId)
        {
            foreach (var playerData in _playerdatas.Where(p => p.TeamId == unitId))
            {
                int completedRound = playerData.GetShanghaiCompletedRound();
                if (completedRound >= 0)
                {
                    return completedRound;
                }
            }
            return -1;
        }

        /// <summary>
        /// 获取所有完成Shanghai的单位及其完成轮次
        /// </summary>
        /// <returns>字典：单位ID -> 完成轮次索引</returns>
        public Dictionary<string, int> GetAllShanghaiCompletions()
        {
            var completions = new Dictionary<string, int>();

            foreach (var unitId in _playerdatas.Select(p => p.TeamId).Distinct())
            {
                int completedRound = GetShanghaiCompletedRound(unitId);
                if (completedRound >= 0)
                {
                    completions[unitId] = completedRound;
                }
            }

            return completions;
        }

        public class PlayerShanghaiData
        {
            public string TeamId { get; }
            public string GranId { get; }
            private readonly List<int> _scores = new List<int>();
            private readonly Dictionary<int, List<int>> _roundHits = new Dictionary<int, List<int>>();

            public PlayerShanghaiData(string teamId, string granId)
            {
                TeamId = teamId;
                GranId = granId;
            }

            public void RecordHit(int segmentValue, int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    _roundHits[roundIndex] = new List<int>();
                }

                _roundHits[roundIndex].Add(segmentValue);
                
                // Calculate score for this throw
                int score = 0;
                if (segmentValue == 1) score = 1;
                else if (segmentValue == 2) score = 2;
                else if (segmentValue == 3) score = 3;
                
                _scores.Add(score);
            }

            public int GetRoundScore(int roundIndex)
            {
                return _roundHits.ContainsKey(roundIndex) ? 
                    _roundHits[roundIndex].Sum() : 0;
            }

            public int TotalScore => _scores.Sum();

            public bool IsShanghaiAchieved()
            {
                // Shanghai规则：在同一轮中命中该轮对应数字的Single(1)、Double(2)和Triple(3)
                return _roundHits.Values.Any(roundHits =>
                    roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3));
            }

            /// <summary>
            /// 检查指定轮次是否完成Shanghai
            /// </summary>
            /// <param name="roundIndex">轮次索引（0-based）</param>
            /// <returns>是否在该轮完成Shanghai</returns>
            public bool IsShanghaiAchievedInRound(int roundIndex)
            {
                if (!_roundHits.ContainsKey(roundIndex))
                {
                    return false;
                }

                var roundHits = _roundHits[roundIndex];
                return roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3);
            }

            /// <summary>
            /// 获取完成Shanghai的轮次（如果有的话）
            /// </summary>
            /// <returns>完成Shanghai的轮次索引，如果没有则返回-1</returns>
            public int GetShanghaiCompletedRound()
            {
                foreach (var kvp in _roundHits)
                {
                    var roundHits = kvp.Value;
                    if (roundHits.Contains(1) && roundHits.Contains(2) && roundHits.Contains(3))
                    {
                        return kvp.Key;
                    }
                }
                return -1;
            }
        }
    }
}
