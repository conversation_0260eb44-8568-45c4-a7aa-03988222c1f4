using System.Linq;
namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class GameRuleShangHai : IGameRule
    {
        public GameCode? Code { get; } = GameCode._ShangHai;
        public int MaxRound { get; }
        public int ThrowsPerRound { get; }
        public int MaximumNumberOfParticipants { get; }
        
        public GameRuleShangHai
        (
            int maxRound = 20,
            int throwsPerRound = 3,
            int maximumNumberOfParticipants = 4
        )
        {
            MaxRound = maxRound;
            ThrowsPerRound = throwsPerRound;
            MaximumNumberOfParticipants = maximumNumberOfParticipants;
        }

        public static readonly Award[] AchievableAwards = new Award[]
        {
            
        };
    }
}