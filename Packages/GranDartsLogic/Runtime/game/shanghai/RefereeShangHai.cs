using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.granlog;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class RefereeShangHai : BaseReferee<MatchShangHai, MatchScorerShangHai, GameEventPublisher>
    {
        internal RefereeShangHai(MatchShangHai match) 
            : base(match, new MatchScorerShangHai(match))
        {
        }
        protected override void ResetRoundData()
        {
            throw new NotImplementedException();
        }

        public override bool IsReachGameEnd
        {
            get
            {
                // Check if any player achieved Shanghai
                if (Match.ParticipantTeams.AllUnits.Any(unit => Scorer.IsShanghaiAchieved(unit.Id)))
                {
                    return true;
                }
                
                // Check if we've reached the end round
                return Match.IsReachEndRound;
            }
        }

        public override bool IsNextRoundOverMaxRound { get; }

        public override void AcceptHit(Segment segment, System.Numerics.Vector2? hitPosition)
        {
            if (IsReachGameEnd)
            {
                Log.d("Receive key. but already finished!");
                return;
            }

            // Determine the segment value (1 for single, 2 for double, 3 for triple)
            int segmentValue = 0;
            if (segment.IsSingle) segmentValue = 1;
            else if (segment.IsDouble) segmentValue = 2;
            else if (segment.IsTriple) segmentValue = 3;
            
            // Record the hit
            Scorer.RecordHit(CurrentUnit.Id, CurrentUnit.CurrentThrower.GranId, segmentValue, CurrentRoundAtCurrentTeam.No - 1);
            EventPublisher?.PublishUpdateProgress(CurrentUnit);
            // Check if Shanghai was achieved
            if (Scorer.IsShanghaiAchieved(CurrentUnit.Id))
            {
                GameEnd();
                return;
            }
            
            // Move to next turn
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                EventPublisher.PublishEndTurn(CurrentUnit);
            }

            // Check if game should end after this round
            if (Match.IsReachEndRound)
            {
                GameEnd();
            }
        }
        
        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = NextTeamIndex(_currentThrowingUnitIndex);
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }
        
        protected virtual int NextTeamIndex(int currentTeamIndex)
        {
            return (currentTeamIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : currentTeamIndex + 1;
        }

        public override int TotalScoreAtCurrentThrowingTeam { get; }

        public override int CurrentScore(string unitId)
        {
            return Scorer.GetTotalScore(unitId);
        }

        public override void RefreshGameData()
        {
            
        }

        protected override void GameEnd()
        {
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt(string unitId)
        {
            return AllRoundsAt(unitId).Select(r => (r, Scorer.TotalScoreInRound(r)));
        }

        public override Award[] AchievableAward => GameRuleShangHai.AchievableAwards;
    }
}
