using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.award;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.targethat
{
    public class RefereeTargetHat : BaseReferee<MatchTargetHat, MatchScorerTargetHat, GameEventPublisher>
    {
        public RefereeTargetHat
        (
            MatchTargetHat match
        ) : base(match, new MatchScorerTargetHat(match))
        {
            Scorer = new MatchScorerTargetHat(match);
        }

        public override bool IsReachGameEnd
        {
            get
            {
                //ラウンド満了か、目標回数達成で終わってるか
                return Match.IsReachAllTeamsToMaxRound ||
                       IsTargetCountCleared();
            }
        }

        public override int TotalScoreAtCurrentThrowingTeam => throw new NotImplementedException();

        //targetHatではhatとblackの合計回数をスコアにする
        public override int CurrentScore(string unitId)
        {
            return Participants.Unit(unitId).AllMember.Sum(m =>
            {
                return TotalAchievedAwardCount(m.GranId, Award.HatTrick);
            });
        }

        public override void AcceptHit(Segment segment, Vector2? hitPosition = null)
        {
            //todo:加算処理追加時に実装
            Segment virtualHit = (segment != Segment.BullIn && segment != Segment.BullOut) ? Segment.Miss : segment;
            //投げたsegmentを保存する
            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualHit, hitPosition);
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            //ラウンド終了時にアワード判定の通知
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                var award = AchievedAward(CurrentRoundAtCurrentTeam);
                if (award != null && award.Length > 0)
                {
                    EventPublisher.PublishAchieveAward(award, CurrentUnit);
                }
                EventPublisher.PublishEndTurn(CurrentUnit);
                ////ラウンド満了でゲーム終了の判断
                if (IsReachGameEnd)
                {
                    GameEnd();
                }
            }

        }


        ///目標回数達成してゲームが終わったのかの判断
        private bool IsTargetCountCleared()
        {
            return CurrentScore(CurrentThrowingUnitId) >= Match.Rule.ClearCount;
        }

        /// ゲーム終了処理
        protected override void GameEnd()
        {
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsReachGameEnd || IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                RefreshGameData();
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = (_currentThrowingUnitIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : _currentThrowingUnitIndex + 1;
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }

        protected override Award[] AchievedAward(Round round)
        {
            List<Award> awards = new List<Award>();
            var totalScoreInRound = round.GetRoundComponent<SegmentInput>().Throws.Sum(t => Scorer.Score(t.VirtualHitArea));
            foreach (var award in AchievableAward)
            {
                switch (award)
                {
                    case Award.HatTrick:
                        if (AwardService.IsAchieveHatTrick(round))
                        {
                            awards.Add(award);
                        }
                        break;
                    case Award.ThreeInTheBlack:
                        if (AwardService.IsAchieve3InTheBlack(round))
                        {
                            awards.Add(award);
                        }
                        break;
                    default:
                        throw new InvalidCodeException($"Invalid award. {award}");
                }
            }

            return awards.ToArray();
        }

        protected override void ResetRoundData()
        {
            Scorer.ResetScore(CurrentUnit.Id, CurrentUnitProgress.CurrentRoundNo - 1);
        }

        public override void RefreshGameData()
        {

        }


        public override Award[] AchievableAward => GameRuleTargetHat.AchievableAward;

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt
        (
            string unitId
        )
        {
            throw new NotImplementedException();
        }

        public override bool IsNextRoundOverMaxRound => Match.IsNextRoundToOverMaxRound;
    }
}