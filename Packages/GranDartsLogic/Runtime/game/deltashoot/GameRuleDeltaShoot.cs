namespace com.luxza.grandartslogic.domain.game.deltashoot
{
    public class GameRuleDeltaShoot : IGameRule
    {
        public GameCode? Code => GameCode._DeltaShoot;
        public int MaxRound => 16;
        public int ThrowsPerRound => 3;
        public int MaximumNumberOfParticipants { get; }

        public GameRuleDeltaShoot(int maximumNumberOfParticipants = 4)
        {
            MaximumNumberOfParticipants = maximumNumberOfParticipants;
        }

        public static Award[] AchievableAward = new Award[]
        {
            Award.TonEighty,
            Award.ThreeInTheBlack,
            Award.ThreeInABed,
            Award.<PERSON>,
            Award.HighTon,
            Award.LowTon
        };
    }
}