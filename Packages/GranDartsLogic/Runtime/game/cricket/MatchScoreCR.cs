using System.Linq;

namespace com.luxza.grandartslogic.domain.game.cricket
{
    internal class MatchScoreCR : MatchScore
    {
        public CricketMarkAtThrow[] Marks { get; private set; }

        public MatchScoreCR(int roundIndex, int[] scores) : base(roundIndex, scores)
        {
            Marks = new CricketMarkAtThrow[scores.Length];
        }

        public int TotalMarkCountByPosition(CricketMarkPosition position)
        {
            return Marks.Where(m => m.Position == position).Sum(m => m.MarkCount);
        }

        public int TotalMarkCountInRound()
        {
            return Marks.Sum(m => m.MarkCount);
        }

        public int TotalMarckCountAtThrow(CricketMarkPosition position, int throwIndex)  {
            var throws = Marks.Take(throwIndex + 1).Where(m => m.Position == position);
            if(throws.Count() == 0) return 0;
            return throws.Sum(m => m.MarkCount);
        }

        public int MarkCountAtThrow(CricketMarkPosition position, int throwIndex)
        {
            return Marks[throwIndex].Position == position ? Marks[throwIndex].MarkCount : 0;
        }

        public void ResetMarks()
        {
            Marks = new CricketMarkAtThrow[Scores.Length];
        }

        public struct CricketMarkAtThrow
        {
            public CricketMarkPosition Position
            {
                get;
                private set;
            }

            public int MarkCount
            {
                get;
                private set;
            }

            public CricketMarkAtThrow(CricketMarkPosition position, int markCount)
            {
                Position = position;

                MarkCount = markCount;
            }
        }
    }
}