using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandartslogic.domain.game.cricket
{
    public class MatchScorerStandardCR : IMatchScorer
    {
        private readonly Dictionary<string, List<MatchScoreCR>> _scoreDic = new();

        private readonly List<PlayerCRData> _playerdatas = new List<PlayerCRData>();
        private readonly IEnumerable<CricketHandicap> _Handicaps;

        public MatchScorerStandardCR
        (
            MatchStandardCR match,
            IEnumerable<CricketHandicap> handicaps
        )
        {
            Match = match;

            foreach (var t in match.ParticipantTeams.AllUnits)
            {
                _Handicaps = handicaps;

                foreach (var m in t.AllMember)
                {
                    _playerdatas.Add(new PlayerCRData(t, m.GranId));
                    _scoreDic[m.GranId] = new List<MatchScoreCR>();
                }
            }
        }

        internal void StartRound(int roundNo, string granId) {
            _scoreDic[granId].Add(new MatchScoreCR(roundNo - 1, new int[Match.Rule.ThrowsPerRound]));
        }

        private int beforeScore = 0;
        private int draftScore = 0;

        private int beforeMarkSum = 0;
        private CricketMarkPosition draftPosition = CricketMarkPosition.None;

        public MatchStandardCR Match { get; }

        private bool ScoreDicEntryCheck
        (
            string unitId
        )
        {
            foreach (var team in Match.ParticipantTeams.AllUnits)
            {
                if (team.Id.Equals(unitId))
                {
                    foreach (var member in team.AllMember)
                    {
                        if (_scoreDic.ContainsKey(member.GranId))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 指定したインデックスのチームのトータルのスコアを返します。
        /// </summary>
        /// <param name="unitId">チームのID</param>
        /// <returns>トータルのスコア</returns>
        /// <exception cref="KeyNotFoundException">指定したチームのインデックスが登録されていない場合</exception>
        public int CurrentScore
        (
            string unitId
        )
        {
            if (!ScoreDicEntryCheck(unitId))
            {
                throw new KeyNotFoundException($"teamIndex {unitId} is not found.");
            }

            int sum = 0;

            foreach (var member in Match.ParticipantTeams.AllUnits.First(t => t.Id.Equals(unitId)).AllMember)
            {
                sum += _scoreDic[member.GranId].Sum(m => m.TotalScore());
            }

            return sum + Handicap(unitId).Score;
        }

        /// <summary>
        /// 指定したスコアを返します。
        /// </summary>
        /// <param name="unitId">チームのインデックス</param>
        /// <param name="roundIndex">ラウンドのIndex</param>
        /// <param name="throwIndex">投擲のIndex</param>
        /// <returns>スコア</returns>
        /// <exception cref="KeyNotFoundException">指定したチームのインデックスが登録されていない場合</exception>
        /// <exception cref="IndexOutOfRangeException">指定したラウンドが範囲外、または投擲Noが範囲外の場合</exception>
        public int Score
        (
            string unitId,
            int roundIndex,
            int throwIndex
        )
        {
            if (!ScoreDicEntryCheck(unitId)) throw new KeyNotFoundException($"teamIndex {unitId} is not found.");

            if (roundIndex < 0 || roundIndex >= GetCurrentTeamRoundLength(unitId))
                throw new IndexOutOfRangeException($"roundNo {roundIndex} is out of range.");

            var scores = GetCurrentTeamMemberRound(unitId, roundIndex);

            if (scores == null)
            {
                throw new IndexOutOfRangeException($"roundNo {roundIndex} is out of range.");
            }

            if (throwIndex < 0 || throwIndex >= scores.Scores.Length)
                throw new IndexOutOfRangeException($"throwNo {throwIndex} is out of range.");
            return scores.Scores[throwIndex];
        }

        private int GetCurrentTeamRoundLength
        (
            string unitId
        )
        {
            int sum = 0;

            foreach (var team in Match.ParticipantTeams.AllUnits)
            {
                if (!team.Id.Equals(unitId)) continue;

                foreach (var member in team.AllMember)
                {
                    sum += _scoreDic[member.GranId].Count;
                }
            }

            return sum;
        }

        private MatchScoreCR GetCurrentTeamMemberRound
        (
            string unitId,
            int roundIndex
        )
        {
            return GetCurrentTeamMatchScores(unitId).FirstOrDefault(v => v.RoundIndex == roundIndex);
        }

        public int[] GetCurrentTeamRoundMarks
        (
            string unitId,
            int roundIndex
        )
        {
            var target = GetCurrentTeamMatchScores(unitId).FirstOrDefault(v => v.RoundIndex == roundIndex);
            return target?.Marks.Select(m => m.MarkCount).ToArray();
        }

        public int[] GetCurrentPlayerRoundMarks
        (
            string granId,
            int roundIndex
        )
        {
            var target = GetCurrentPlayerMatchScores(granId).FirstOrDefault(v => v.RoundIndex == roundIndex);
            return target?.Marks.Select(m => m.MarkCount).ToArray();
        }


        public int GetTeamCountAwardSum
        (
            string unitId,
            int markCount
        )
        {
            return GetCurrentTeamMatchScores(unitId).Count(v => v.TotalMarkCountInRound() == markCount);
        }


        private MatchScoreCR[] GetCurrentTeamMatchScores
        (
            string unitId
        )
        {
            var unit = Match.ParticipantTeams.AllUnits.FirstOrDefault(t => t.Id.Equals(unitId));
            if(unit ==  null) {
                return Array.Empty<MatchScoreCR>();
            }

            var members = unit.AllMember.Select(m => m.GranId);
            return members.SelectMany(m => _scoreDic[m]).OrderBy(s => s.RoundIndex).ToArray();
        }


        private MatchScoreCR[] GetPlayerMatchScores
        (
            string granId
        )
        {
            return _scoreDic[granId].OrderBy(s => s.RoundIndex).ToArray();
        }

        private MatchScoreCR[] GetCurrentPlayerMatchScores
        (
            string granId
        ) => _scoreDic[granId].OrderBy(s => s.RoundIndex).ToArray();


        /// <summary>
        /// 指定したラウンドのトータルのスコアを返します。
        /// </summary>
        /// <param name="unitId">チームのID</param>
        /// <param name="roundIndex">ラウンドのIndex</param>
        /// <returns>トータルのスコア</returns>
        /// <exception cref="KeyNotFoundException">指定したチームのインデックスが登録されていない場合</exception>
        /// <exception cref="IndexOutOfRangeException">指定したラウンドが範囲外、または投擲Noが範囲外の場合</exception>
        public int TotalScoreInRound
        (
            string unitId,
            int roundIndex
        )
        {
            if (!ScoreDicEntryCheck(unitId)) throw new KeyNotFoundException($"teamIndex {unitId} is not found.");

            if (roundIndex < 0 || roundIndex >= GetCurrentTeamRoundLength(unitId))
                throw new IndexOutOfRangeException($"roundNo {roundIndex} is out of range.");

            return GetCurrentTeamMemberRound(unitId, roundIndex).TotalScore();
        }

        public int Score
        (
            Segment hit
        )
        {
            return _scoreMap.FirstOrDefault(map => map.Segment.Equals(hit))?.Score ?? 0;
        }

        public int BaseScore
        (
            Segment hit
        )
        {
            return _scoreMap.FirstOrDefault(map => map.Segment.Equals(hit))?.SingleMarkScore ?? 0;
        }

        /// <summary>
        /// 試合状況によって、VirtualHitをどこにするかを決定します。
        /// また、その結果のスコアも同時に保持します。
        /// 例えば、20がクローズされている状態でT20にhitした場合、
        /// Scoreは0として保持され、Missを返却します。
        /// </summary>
        /// <param name="hit"></param>
        /// <param name="unit"></param>
        /// <param name="roundIndex"></param>
        /// <param name="throwIndex"></param>
        /// <returns>
        /// 計算結果によって算出された仮想的なHit情報
        /// </returns>
        internal Segment CalculateVirtualHitAndConfirmScore
        (
            Segment hit,
            Unit unit,
            int roundIndex,
            int throwIndex
        )
        {
            var position = hit.ToCricketMarkPosition();
            var scores = GetCurrentTeamMemberRound(unit.Id, roundIndex);

            draftScore = 0;
            beforeScore = CurrentScore(unit.Id);
            beforeMarkSum = 0;

            draftPosition = position;

            if (!Match.TargetPositions.Contains(position))
            {
                //TargetPositionに含まれていない
                scores.Scores[throwIndex] = 0;
                //OUTにヒットしてたらOUTと返す
                return hit == Segment.OUT ? Segment.OUT : Segment.Miss;
            }

            beforeMarkSum = GetCurrentTeamMatchScores(unit.Id).
                Sum
                (
                    v =>
                    {
                        var mark = GetMarkPosition(v, position);
                        return mark;
                    }
                );

            if (IsClosed(position))
            {
                scores.Scores[throwIndex] = 0;
                return Segment.Miss;
            }

            //もし全部のチームがオープンしてる場合
            if (IsAllOpenedByOthers(unit.Id, position))
            {
                var markCountInConsiderationOfClosing =
                    MarkCountInConsiderationOfClosing(unit.Id, position, hit.Multiplier);
                scores.Marks[throwIndex] = new MatchScoreCR.CricketMarkAtThrow
                    (position, markCountInConsiderationOfClosing);
                scores.Scores[throwIndex] = 0;
                UpdateCheckCloseList(unit.Id, unit.CurrentThrower.GranId);

                if (markCountInConsiderationOfClosing == 1)
                {
                    return Segment.AllSegments.First(s => s.PositionCode == hit.PositionCode && s.IsSingle);
                }

                if (markCountInConsiderationOfClosing == 2)
                {
                    return Segment.AllSegments.First(s => s.PositionCode == hit.PositionCode && s.IsDouble);
                }

                if (markCountInConsiderationOfClosing == 3)
                {
                    return hit;
                }
            }
            else
            {
                //もし全部のチームがオープンしてない場合

                if (IsOpenedBy(unit.Id, position))
                {
                    //すでにオープンしている場合は、何も考慮せずに点数加算とマーク数の加算

                    //オーバーキルしてなければそのままマーク数加算 & スコア加算
                    scores.Marks[throwIndex] =
                        new MatchScoreCR.CricketMarkAtThrow(position, IsOverKill() ? 0 : hit.Multiplier);
                    draftScore = Score(hit);
                    var hitScore = IsOverKill() ? 0 : draftScore;
                    scores.Scores[throwIndex] = hitScore;
                }
                else
                {
                    //オープンできていない場合は、スコア計算のために、オープンするまでのマーク数を考慮した上で点数計算を行う
                    var draftMarks = MarkCountAtPositionByUnit(unit.Id, position) + hit.Multiplier;
                    if (draftMarks > Match.Rule.MaxDisplayMarkCount)
                    {
                        draftScore = BaseScore(hit) * (draftMarks - Match.Rule.MaxDisplayMarkCount);
                        //マーク数が3を超えた場合、3までのマーク数カウントとしてカウントする。
                        //余分にヒットした数については、切り捨てる
                        //こちらもオーバーキルしてればスコアの加算は無し
                        scores.Scores[throwIndex] = IsOverKill() ? 0 : draftScore;
                    }
                    else
                    {
                        //まだオープンしていないので、0点
                        scores.Scores[throwIndex] = draftScore;
                    }
                }

                if (IsOverKill())
                {
                    //マーク数はオーバーキルしてるときはクローズのと同じマーク数を減らす処理を動かす

                    var markCountInConsiderationOfOverKill =
                        MarkCountInConsiderationOfOverKill(unit.Id, position, hit.Multiplier);
                    scores.Marks[throwIndex] =
                        new MatchScoreCR.CricketMarkAtThrow(position, markCountInConsiderationOfOverKill);

                    if (markCountInConsiderationOfOverKill == 1)
                    {
                        return Segment.AllSegments.First(s => s.PositionCode == hit.PositionCode && s.IsSingle);
                    }

                    if (markCountInConsiderationOfOverKill == 2)
                    {
                        return Segment.AllSegments.First(s => s.PositionCode == hit.PositionCode && s.IsDouble);
                    }

                    if (markCountInConsiderationOfOverKill == 3)
                    {
                        return hit;
                    }

                    if (markCountInConsiderationOfOverKill <= 0)
                    {
                        return Segment.Miss;
                    }
                }
                else
                {
                    //オーバーキルでなければそのままマーク数を加算させる
                    _scoreDic[unit.CurrentThrower.GranId].First(s => s.RoundIndex == roundIndex).Marks[throwIndex] =
                        new MatchScoreCR.CricketMarkAtThrow(position, hit.Multiplier);
                }
            }

            UpdateCheckOpenList(unit.Id, unit.CurrentThrower.GranId);

            return hit;
        }

        public int MarkCount(Throw thr)
        {
            if (thr == null) return 0;
            var position = thr.VirtualHitArea.ToCricketMarkPosition();
            if (position == CricketMarkPosition.None) return 0;
            if (!Match.TargetPositions.Contains(position)) return 0;
            if (thr.VirtualHitArea.IsTriple) return 3;
            if (thr.VirtualHitArea.IsDouble) return 2;
            if (thr.VirtualHitArea.IsSingle) return 1;
            return 0;
        }


        /// <summary>
        /// 指定したチームで指定箇所がオープンされているかどうか
        /// </summary>
        /// <param name="unitId">チームのIDです</param>
        /// <param name="position">マークの場所です。</param>
        /// <returns>オープンしているかどうか</returns>
        /// <exception cref="NoTeamInParticipantExcepetion">参加リストにチームがいません。</exception>
        public bool IsOpenedBy
        (
            string teamId,
            CricketMarkPosition position
        )
        {
            return MarkCountAtPositionByUnit(teamId, position) >= Match.Rule.MaxDisplayMarkCount;
        }

        /// <summary>
        /// Openが発生してたらtargetOpenListの該当箇所に1を入れてOpenしたプレイヤーと記録する処理
        /// 先にCloseが発生してたら1を入れない
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="granId"></param>
        private void UpdateCheckOpenList
        (
            string unitId,
            string granId
        )
        {
            int[] targets = new int[Match.TargetPositions.Length];
            int[] closes = GetPlayerStats(granId).playdata.TargetCloses;
            int index = 0;

            foreach (var pos in Match.TargetPositions)
            {
                if (IsOpenedBy(unitId, pos) && closes[index] == 0)
                {
                    targets[index] = 1;
                }
                index++;
            }

            GetPlayerStats(granId).playdata.TargetOpens = targets;
        }

        /// <summary>
        /// Closeが発生してたらtargetCloseListの該当箇所に1を入れてCloseしたプレイヤーと記録する処理
        /// 先にopenが発生してたら1を入れない
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="granId"></param>
        private void UpdateCheckCloseList
        (
            string unitId,
            string granId
        )
        {
            int[] targets = new int[Match.TargetPositions.Length];
            int[] opens = GetPlayerStats(granId).playdata.TargetOpens;
            int index = 0;

            foreach (var pos in Match.TargetPositions)
            {
                if (IsClosed(pos) && opens[index] == 0)
                {
                    targets[index] = 1;
                }
                index++;
            }

            GetPlayerStats(granId).playdata.TargetCloses = targets;
        }

        //オーバーキルの判断処理
        //2UnitのみかつSteelPlayerがいない対戦のみ適応
        //自分の点数と最低のスコアが200点以上開きがあるときは0点になる
        public bool IsOverKill()
        {
            if (Match.ParticipantTeams.Count != 2 || Match.ParticipantTeams.AllUnits.SelectMany(u => u.AllMember).Any(m => m.Board == BoardSize.Steel)) return false;
            bool result = (beforeScore - MinTeamScore() >= 200);

            return result;
        }

        //オーバーキルのエフェクト再生
        //オーバーキルの状態かつヒットした値が0点でなければtrueを返す
        public bool IsPlayOverKillEffect()
        {
            return IsOverKill() && (draftScore > 0);
        }

        //クローズのエフェクト再生
        //クローズが発生したときのみtrueになる
        public bool IsCloseEffect()
        {
            if (!Match.TargetPositions.Contains(draftPosition)) return false;
            return IsClosed(draftPosition) && (beforeMarkSum < Match.Rule.MaxDisplayMarkCount);
        }

        /// <summary>
        /// 指定したチームの指定したポジションの総マーク数を返します。
        /// </summary>
        /// <param name="unitId">チームのID</param>
        /// <param name="pos">Position</param>
        /// <returns>総マーク数</returns>
        /// <exception cref="KeyNotFoundException"></exception>
        public int MarkCountAtPositionByUnit
        (
            string unitId,
            CricketMarkPosition pos
        )
        {
            if (!ScoreDicEntryCheck(unitId)) throw new KeyNotFoundException($"unit:{unitId} is not found.");
            return GetCurrentTeamMatchScores(unitId).Sum(m => m.TotalMarkCountByPosition(pos)) +
                   GetHandicapMark(unitId, pos);
        }

        /// <summary>
        /// Returns the total number of marks at the specified position by the player.
        /// </summary>
        /// <param name="granId"></param>
        /// <param name="pos"></param>
        /// <returns></returns>
        public int TotalMarkCountAtPositionByPlayer
        (
            string granId,
            CricketMarkPosition pos
        )
        {
            return GetPlayerMatchScores(granId).Sum(m => m.TotalMarkCountByPosition(pos));
        }

        public int TotalMarkCountAtPositionByPlayer
        (
            string granId,
            CricketMarkPosition pos,
            int roundIndex,
            int throwIndex
        )
        {
            if (roundIndex < 0 || throwIndex < 0) return 0;
            var scores = GetPlayerMatchScores(granId);
            foreach (var score in scores) {
                if(score.RoundIndex == roundIndex) {
                    return score.MarkCountAtThrow(pos, throwIndex);
                }
            }

            return 0;
        }

        /// <summary>
        /// 指定したチームのラウンドのマーク数合計を返します
        /// </summary>
        /// <param name="unitId"></param>
        /// <param name="roundIndex"></param>
        /// <returns></returns>
        public int MarkCountAtRound
        (
            string unitId,
            int roundIndex
        )
        {
            if (!ScoreDicEntryCheck(unitId)) throw new KeyNotFoundException($"teamIndex {unitId} is not found.");

            var ms = GetCurrentTeamMemberRound(unitId, roundIndex).Marks;
            return GetCurrentTeamMemberRound(unitId, roundIndex).Marks.Select(m => m.MarkCount).Sum();
        }

        /// <summary>
        /// 全てのチームの該当箇所のマーク数が3以上なら、Close
        /// </summary>
        /// <param name="position">Markの位置</param>
        /// <returns>Closeされているかどうか</returns>
        /// <returns>もし1チームにしかいない場合は必ずfalse</returns>
        public bool IsClosed
        (
            CricketMarkPosition position
        )
        {
            if (Match.ParticipantTeams.AllUnits.Length == 1) return false;

            return Match.ParticipantTeams.AllUnits.All
            (
                team =>
                {
                    var marksum = GetCurrentTeamMatchScores(team.Id).
                        Sum
                        (
                            v =>
                            {
                                var mark = GetMarkPosition(v, position);
                                return mark;
                            }
                        );
                    int handicapmark = GetHandicapMark(team.Id, position);

                    return marksum + handicapmark >= Match.Rule.MaxDisplayMarkCount;
                });
        }

        private int GetMarkPosition
        (
            MatchScoreCR scoreCR,
            CricketMarkPosition pos
        )
        {
            var p = scoreCR.TotalMarkCountByPosition(pos);
            return p;
        }
        //渡したUnitIDのユニットの画面に描画されているマーク数を返す(0 ~ 3)
        public int DisplayMarkCountByPosition(string unitId, CricketMarkPosition pos)
        {
            var currentUnitMarks = GetCurrentTeamMatchScores(unitId);

            var positionMarkSumCount = currentUnitMarks.Sum(m => m.TotalMarkCountByPosition(pos));
            int handicapmark = GetHandicapMark(unitId, pos);
            int markCount = positionMarkSumCount + handicapmark;
            return markCount >= Match.Rule.MaxDisplayMarkCount ? Match.Rule.MaxDisplayMarkCount : markCount;
        }

        /// <summary>
        /// 投擲でクローズされることを考慮したマーク数
        /// </summary>
        /// <param name="throwunitId"></param>
        /// <param name="position"></param>
        /// <param name="markCount"></param>
        /// <returns>Closeを考慮した上で達成したマーク数</returns>
        private int MarkCountInConsiderationOfClosing
        (
            string throwunitId,
            CricketMarkPosition position,
            int markCount
        )
        {
            //まだ他の人全員がクローズしていない
            if (!IsAllOpenedByOthers(throwunitId, position)) return markCount;

            int draftMarks = MarkCountAtPositionByUnit(throwunitId, position);
            if (draftMarks + markCount <= Match.Rule.MaxDisplayMarkCount) return markCount;

            var result = Match.Rule.MaxDisplayMarkCount - draftMarks;

            return result <= 0 ? 0 : result;
        }

        public int MarkCountInConsiderationOfClosing
        (
            string throwunitId,
            Segment segment
        )
        {
            var position = segment.ToCricketMarkPosition();
            if (!Match.Rule.TargetPositions.Contains(position)) return 0;
            return MarkCountInConsiderationOfClosing(throwunitId, position, segment.Multiplier);
        }

        /// <summary>
        /// オーバーキルでマーク数を減らすときの処理
        /// </summary>
        /// <param name="throwunitId"></param>
        /// <param name="position"></param>
        /// <param name="markCount"></param>
        /// <returns>オーバーキルを考慮した上で達成したマーク数</returns>
        private int MarkCountInConsiderationOfOverKill
        (
            string throwunitId,
            CricketMarkPosition position,
            int markCount
        )
        {
            int draftMarks = MarkCountAtPositionByUnit(throwunitId, position);
            if (draftMarks + markCount <= Match.Rule.MaxDisplayMarkCount) return markCount;
            var result = Match.Rule.MaxDisplayMarkCount - draftMarks;

            return result <= 0 ? 0 : result;
        }

        /// <summary>
        /// すでに他のチーム全員がオープンしているかどうか
        /// </summary>
        /// <param name="throwunitId">投擲するチームのID</param>
        /// <param name="position">hitしたPosition</param>
        /// <returns>他のチームすべてがオープンしている場合true</returns>
        /// <returns>もし1チームにしかいない場合は必ずfalse</returns>
        private bool IsAllOpenedByOthers
        (
            string throwunitId,
            CricketMarkPosition position
        )
        {
            if (Match.ParticipantTeams.AllUnits.Length == 1) return false;

            return Match.ParticipantTeams.AllUnits.Where
                    (team => !team.Id.Equals(throwunitId)).
                All(team => IsOpenedBy(team.Id, position));
        }

        /// <summary>
        /// 指定したチームのオープンしているターゲット数を返します。
        /// </summary>
        /// <returns></returns>
        internal int TotalOpenCountBy
        (
            string unitId
        )
        {
            return Match.TargetPositions.Sum(pos => IsOpenedBy(unitId, pos) ? 1 : 0);
        }

        /// <summary>
        /// 各チームのスコアの最大数を返します。
        /// </summary>
        /// <returns></returns>
        internal int MaxTeamScore()
        {
            return Match.ParticipantTeams.AllUnits.Max(team => CurrentScore(team.Id));
        }

        /// <summary>
        /// 各チームのスコアの最小数を返します。
        /// </summary>
        /// <returns></returns>
        internal int MinTeamScore()
        {
            return Match.ParticipantTeams.AllUnits.Min(team => CurrentScore(team.Id));
        }

        private CricketScore[] _scoreMap => new CricketScore[] {
            new CricketScore(Segment.Single1In, 1),
            new CricketScore(Segment.Single1Out, 1),
            new CricketScore(Segment.Double1, 1),
            new CricketScore(Segment.Triple1, 1),
            new CricketScore(Segment.Single2In, 2),
            new CricketScore(Segment.Single2Out, 2),
            new CricketScore(Segment.Double2, 2),
            new CricketScore(Segment.Triple2, 2),
            new CricketScore(Segment.Single3In, 3),
            new CricketScore(Segment.Single3Out, 3),
            new CricketScore(Segment.Double3, 3),
            new CricketScore(Segment.Triple3, 3),
            new CricketScore(Segment.Single4In, 4),
            new CricketScore(Segment.Single4Out, 4),
            new CricketScore(Segment.Double4, 4),
            new CricketScore(Segment.Triple4, 4),
            new CricketScore(Segment.Single5In, 5),
            new CricketScore(Segment.Single5Out, 5),
            new CricketScore(Segment.Double5, 5),
            new CricketScore(Segment.Triple5, 5),
            new CricketScore(Segment.Single6In, 6),
            new CricketScore(Segment.Single6Out, 6),
            new CricketScore(Segment.Double6, 6),
            new CricketScore(Segment.Triple6, 6),
            new CricketScore(Segment.Single7In, 7),
            new CricketScore(Segment.Single7Out, 7),
            new CricketScore(Segment.Double7, 7),
            new CricketScore(Segment.Triple7, 7),
            new CricketScore(Segment.Single8In, 8),
            new CricketScore(Segment.Single8Out, 8),
            new CricketScore(Segment.Double8, 8),
            new CricketScore(Segment.Triple8, 8),
            new CricketScore(Segment.Single9In, 9),
            new CricketScore(Segment.Single9Out, 9),
            new CricketScore(Segment.Double9, 9),
            new CricketScore(Segment.Triple9, 9),
            new CricketScore(Segment.Single10In, 10),
            new CricketScore(Segment.Single10Out, 10),
            new CricketScore(Segment.Double10, 10),
            new CricketScore(Segment.Triple10, 10),
            new CricketScore(Segment.Single11In, 11),
            new CricketScore(Segment.Single11Out, 11),
            new CricketScore(Segment.Double11, 11),
            new CricketScore(Segment.Triple11, 11),
            new CricketScore(Segment.Single12In, 12),
            new CricketScore(Segment.Single12Out, 12),
            new CricketScore(Segment.Double12, 12),
            new CricketScore(Segment.Triple12, 12),
            new CricketScore(Segment.Single13In, 13),
            new CricketScore(Segment.Single13Out, 13),
            new CricketScore(Segment.Double13, 13),
            new CricketScore(Segment.Triple13, 13),
            new CricketScore(Segment.Single14In, 14),
            new CricketScore(Segment.Single14Out, 14),
            new CricketScore(Segment.Double14, 14),
            new CricketScore(Segment.Triple14, 14),
            new CricketScore(Segment.Single15In, 15),
            new CricketScore(Segment.Single15Out, 15),
            new CricketScore(Segment.Double15, 15),
            new CricketScore(Segment.Triple15, 15),
            new CricketScore(Segment.Single16In, 16),
            new CricketScore(Segment.Single16Out, 16),
            new CricketScore(Segment.Double16, 16),
            new CricketScore(Segment.Triple16, 16),
            new CricketScore(Segment.Single17In, 17),
            new CricketScore(Segment.Single17Out, 17),
            new CricketScore(Segment.Double17, 17),
            new CricketScore(Segment.Triple17, 17),
            new CricketScore(Segment.Single18In, 18),
            new CricketScore(Segment.Single18Out, 18),
            new CricketScore(Segment.Double18, 18),
            new CricketScore(Segment.Triple18, 18),
            new CricketScore(Segment.Single19In, 19),
            new CricketScore(Segment.Single19Out, 19),
            new CricketScore(Segment.Double19, 19),
            new CricketScore(Segment.Triple19, 19),
            new CricketScore(Segment.Single20In, 20),
            new CricketScore(Segment.Single20Out, 20),
            new CricketScore(Segment.Double20, 20),
            new CricketScore(Segment.Triple20, 20),
            new CricketScore(Segment.BullIn, 25),
            new CricketScore(Segment.BullOut, 25),
            new CricketScore(Segment.Miss, 0),
            new CricketScore(Segment.OUT, 0)
        };

        public double? RealtimeStats
        (
            string granId
        ) => GetPlayerStats(granId).playdata.RealTimeStats;

        public double? TeamRealtimeStats
        (
            Unit unit
        ) => unit.AllMember.Sum(m => GetPlayerStats(m.GranId).playdata.RealTimeStats);

        public double? MPRFor80
        (
            string granId
        ) => GetPlayerStats(granId).playdata.StatsFor80.MPR;

        public double? MPRFor100
        (
            string granId
        ) => GetPlayerStats(granId).playdata.StatsFor100.MPR;

        public int FiveMarksCount(string granId)
        {
            return GetPlayerStats(granId).playdata.FiveMarksCount;
        }

        public int SixMarksCount(string granId)
        {
            return GetPlayerStats(granId).playdata.SixMarksCount;
        }

        public int SevenMarksCount(string granId)
        {
            return GetPlayerStats(granId).playdata.SevenMarksCount;
        }

        public int EightMarksCount(string granId)
        {
            return GetPlayerStats(granId).playdata.EightMarksCount;
        }

        public int NineMarksCount(string granId)
        {
            return GetPlayerStats(granId).playdata.NineMarksCount;
        }

        public float FiveMarksRate(string granId)
        {
            return GetPlayerStats(granId).playdata.FiveMarksRate;
        }

        public float SixMarksRate(string granId)
        {
            return GetPlayerStats(granId).playdata.SixMarksRate;
        }

        public float SevenMarksRate(string granId)
        {
            return GetPlayerStats(granId).playdata.SevenMarksRate;
        }

        public float EightMarksRate(string granId)
        {
            return GetPlayerStats(granId).playdata.EightMarksRate;
        }

        public float NineMarksRate(string granId)
        {
            return GetPlayerStats(granId).playdata.NineMarksRate;
        }

        public float PlayerTripleHitRates
        (
            string granId
        ) => GetPlayerStats(granId).playdata.TripleRate;

        public int[] PlayerTargetMarkCount
        (
            string granId
        ) => GetPlayerStats(granId).playdata.TargetMarkCounts;

        public float PlayerShootRate
        (
            string granId
        ) => GetPlayerStats(granId).playdata.Shoot;

        public float PlayerKeepRate
        (
            string granId
        ) => GetPlayerStats(granId).playdata.Keep;

        public int[] PlayerTargetOpens
        (
            string granId
        ) => GetPlayerStats(granId).playdata.TargetOpens;

        public int[] PlayerTargetCloses
        (
            string granId
        ) => GetPlayerStats(granId).playdata.TargetCloses;

        public bool[] PlayerHasOpenedAreas
        (
            string granId
        ) => GetPlayerStats(granId).playdata.HasOpenedAreas;

        public bool[] PlayerHasClosedAreaBeforeOpponentScored
        (
            string granId
        ) => GetPlayerStats(granId).playdata.HasClosedAreaBeforeOpponentScored;

        public int[] PlayerDartCountToGet3Marks
        (
            string granId
        ) => GetPlayerStats(granId).playdata.DartCountToGet3Marks;

        public bool IsFirstRoundOpen
        (
            string granId
        ) => GetPlayerStats(granId).playdata.IsFirstRoundOpen;

        public int PlayerFirstRoundMarkCount
        (
            string granId
        ) => GetPlayerStats(granId).playdata.FirstRoundMarkCount;

        public bool IsHavingControl
        (
            string granId
        ) => GetPlayerStats(granId).playdata.IsHavingControl;

        public bool IsComeback
        (
            string granId
        ) => GetPlayerStats(granId).playdata.IsComeback;

        public CricketRoundSummary[] RoundSummaries
        (
            string granId
        ) => GetPlayerStats(granId).playdata.RoundSummaries;

        //SendPlayDatav2系にて追加されたScoreの項目で使用するようのデータを返す
        //ゲーム終了時点の各ユニット別に　オープンするまでに獲得したマーク数,加算に計上されたマーク数
        public Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> TotalMarksCounts(Unit unit)
        {

            Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> datas = new();
            foreach (var pos in Match.Rule.TargetPositions)
            {
                var totalposMarkCount = MarkCountAtPositionByUnit(unit.Id, pos);
                datas.Add(pos, (totalposMarkCount > 3 ? 3 : totalposMarkCount,//オープンするまでに獲得したマーク数 markCount20ToGetArea
                                totalposMarkCount <= 3 ? 0 : totalposMarkCount - 3//加算に計上されたマーク数 markCount20ToGetPoint
                                )
                          );
            }

            return datas;
        }

        public Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> TotalMarksCounts(string granId)
        {
            Dictionary<CricketMarkPosition, (int openMarkCount, int additionMarkCount)> datas = new();
            foreach (var pos in Match.Rule.TargetPositions)
            {
                var totalposMarkCount = TotalMarkCountAtPositionByPlayer(granId, pos);
                datas.Add(pos, (totalposMarkCount > 3 ? 3 : totalposMarkCount,//オープンするまでに獲得したマーク数 markCount20ToGetArea
                                totalposMarkCount <= 3 ? 0 : totalposMarkCount - 3//加算に計上されたマーク数 markCount20ToGetPoint
                                )
                          );
            }

            return datas;
        }

        internal void CalculateRealtimeStats
        (
            Unit unit
        )
        {
            string granId = unit.CurrentThrower.GranId;
            Throw[] throws = unit.AllThrowsByMember(granId);
            if (throws.Length == 0)
            {
                GetPlayerStats(granId).playdata.RealTimeStats = 0.0;
            }
            else
            {
                GetPlayerStats(granId).playdata.RealTimeStats = Math.Round(
                    MarksAllRoundMember(unit.Id, granId) / (double)throws.Length * 3.0, 2,
                    MidpointRounding.AwayFromZero);
            }
        }

        internal void SetMPRFor80
        (
            string granId,
            double? value
        )
        {
            if (value.HasValue)
            {
                ////Debug.Log("Set Mpr80%: " + value.Value);
                GetPlayerStats(granId).playdata.StatsFor80.MPR = Math.Round
                    (value.Value, 2, MidpointRounding.AwayFromZero);
            }
            else
            {
                ////Debug.Log("Set Mpr80%: null");
                GetPlayerStats(granId).playdata.StatsFor80.MPR = null;
            }
        }

        internal void SetMPRFor100
        (
            string granId,
            double? value
        )
        {
            if (value.HasValue)
            {
                GetPlayerStats(granId).playdata.StatsFor100.MPR = Math.Round
                    (value.Value, 2, MidpointRounding.AwayFromZero);
            }
            else
            {
                GetPlayerStats(granId).playdata.StatsFor100.MPR = null;
            }
        }
        /// <summary>
        /// ユニットが保持してる
        /// スタッツの計算などに用いる累計取得マーク数合計を返す
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public int MarkTotalCount(string unitId) => GetCurrentTeamMatchScores(unitId).Sum(m => m.TotalMarkCountInRound());
        /// <summary>
        /// 画面描画されてるマーク数の合計数を返す(0 ~ 21)
        /// </summary>
        /// <param name="unitId"></param>
        /// <returns></returns>
        public int DisplayMarkTotalCount(string unitId) => Match.TargetPositions.Sum(pos => DisplayMarkCountByPosition(unitId, pos));

        internal int MarksAllRoundMember
        (
            string unitId,
            string granId
        )
        {
            var marksPerRound = _scoreDic[granId].Sum(m => m.TotalMarkCountInRound());
            return marksPerRound;
        }

        private int GetHandicapMark
        (
            string unitId,
            CricketMarkPosition pos
        )
        {
            return Handicap(unitId).MarkCountByPosition(pos);
        }

        private CricketHandicap Handicap
        (
            string unitId
        )
        {
            //_Handicapはかならず全チーム分ある
            return _Handicaps.First(h => h.TeamId.Equals(unitId));
        }

        internal void ResetRoundData
        (
            string unitId,
            int roundIndex
        )
        {
            Match.ParticipantTeams.Unit(unitId).
                Progress.Rounds[roundIndex].
                GetRoundComponent<IRoundInput>().
                DiscardAllInput();

            ResetCRData(Match.ParticipantTeams.Unit(unitId), roundIndex);
            CalculateRealtimeStats(Match.ParticipantTeams.Unit(unitId));
        }

        internal void ResetThrowData
        (
            string unitId,
            int roundIndex
        )
        {
            var unit = Match.ParticipantTeams.Unit(unitId);
            ResetCRData(unit, roundIndex);

            //もしThrowのデータがある場合はThrowReverseなのでデータ入れ直す
            var throwIndex = 0;

            foreach (var throwdata in unit.Progress.Rounds[roundIndex].GetRoundComponent<SegmentInput>().Throws)
            {
                //Debug.Log($"ResetCRData throwdata actual:{throwdata.ActuaryHitArea} virtal:{throwdata.VirtualHitArea}");
                if (!throwdata.IsEmpty)
                {
                    var virtualHit = CalculateVirtualHitAndConfirmScore
                        (throwdata.ActuaryHitArea, unit, roundIndex, throwIndex);
                    //Debug.Log($"ResetCRData CalculateVirtualHitAndConfirmScore virtal:{virtualHit}");
                }

                throwIndex++;
            }

            CalculateRealtimeStats(unit);
        }

        private void ResetCRData
        (
            Unit unit,
            int roundIndex
        )
        {
            if (!ScoreDicEntryCheck(unit.Id)) throw new KeyNotFoundException($"unitId {unit.Id} is not found.");
            if (roundIndex < 0 || roundIndex >= GetCurrentTeamMatchScores(unit.Id).Length)
                throw new IndexOutOfRangeException($"roundNo is out of range.");
            GetCurrentTeamMemberRound(unit.Id, roundIndex).ResetScores();
            GetCurrentTeamMemberRound(unit.Id, roundIndex).ResetMarks();
        }

        internal void DiscardStats()
        {
            foreach (var team in Match.ParticipantTeams.AllUnits)
            {
                foreach (var member in team.AllMember)
                {
                    SetMPRFor80(member.GranId, null);
                    SetMPRFor100(member.GranId, null);
                    GetPlayerStats(member.GranId).playdata.TripleRate = 0.0f;
                }
            }
        }
        //どのラウンドで現在のスコアに到達したかを返す
        private int FindRoundNoForCurrentScore(Unit unit)
        {
            var roundCount = 1;
            int finalScore = CurrentScore(unit.Id);
            int currentScore = 0;
            foreach (var round in unit.Progress.Rounds)
            {
                currentScore += TotalScoreInRound(unit.Id, roundCount - 1);
                if (currentScore == finalScore)
                {
                    //Debug.Log($"RoundthatResultedinCurrentScore {unit.Id}:roundCount:{roundCount} currentScore:{currentScore} finalScore:{finalScore}");
                    break;
                }
                roundCount++;
            }
            return roundCount;
        }

        //どのラウンドで現在の累計マーク数に到達したかを返す
        private int FindRoundNoForTotalMarkCount(Unit unit)
        {
            var roundCount = 1;
            int finalMarkTotalCount = MarkTotalCount(unit.Id);
            int currentMarkTotalCount = 0;
            foreach (var round in unit.Progress.Rounds)
            {
                currentMarkTotalCount += MarkCountAtRound(unit.Id, roundCount - 1);
                if (currentMarkTotalCount == finalMarkTotalCount)
                {
                    //Debug.Log($"RoundthatResultedinMarkTotalCount {unit.Id}:roundCount:{roundCount} finalMarkTotalCount:{finalMarkTotalCount} currentMarkTotalCount:{currentMarkTotalCount}");
                    break;
                }
                roundCount++;
            }
            return roundCount;
        }

        //どのユニットが先に最終スコアになったのかチェックしてその順番通りにGameRanking割り当て直して返す
        internal Unit[] ReassignTheRankforSameRanks(Unit[] units)
        {
            var ordered = units.OrderBy(unit =>
            {
                var round = FindRoundNoForCurrentScore(unit);
                //1.現在のスコアに到達ラウンド数が少ない順
                //Debug.Log($"RoundthatResultedinCurrentScore .Unit({unit.Id}).Score round:" + round);
                return round;
            }).ThenBy(unit =>
            {
                var roundCount = FindRoundNoForTotalMarkCount(unit);
                //2.現在の累計マーク数に到達ラウンド数が少ない順
                //Debug.Log($"RoundthatResultedinMarkTotalCount Unit({unit.Id}).Mark round:" + roundCount);
                return roundCount;
            }).ToList();

            //順位を決めたい初期値を入れる
            //ここに入っているUnitの配列は全て同率順位なのでFirstでも問題はない
            var rank = units.First().GameRanking;
            foreach (var unit in ordered)
            {
                //Debug.Log($"ReassignTheRankforSameRanks:{unit.Id}:{rank}");
                unit.GameRanking = rank;
                rank++;
            }

            return ordered.ToArray();
        }


        /// <summary>
        /// ゲームが終わった時に計算するAnalysisdataをまとめたもの
        /// </summary>
        internal void CreatePlayerData()
        {
            ConfirmMarkCountRate();
            ConfirmTripleHitRate();
            ConfirmShootRate();
            ConfirmKeepRate();

            CricketMarkPosition[] cricketTargetMarks = Match.TargetPositions;
            ConfirmFirstRoundMarkCount(cricketTargetMarks);
            ConfirmAllRoundTargetMarkCount(cricketTargetMarks);
            ConfirmDartCountToGet3Marks(cricketTargetMarks);
            ConfirmOpenClosedArea();

            ConfirmIsHavingControl();
            ConfirmIsComeBack();

            ConfirmRoundSummary();
        }

        #region Analysis

        /// <summary>
        /// 相手に点数を 1 点以上上回られずに勝利したかどうか 1 度でも 1 点以上上回られたら false　0 点で引き分けた場合は両者とも false
        /// </summary>
        private void ConfirmIsHavingControl()
        {
            //0 点で引き分けた場合は両者とも false
            if (Match.ParticipantTeams.AllUnits.All(unit => CurrentScore(unit.Id) == 0))
            {
                foreach (var member in Match.ParticipantTeams.AllUnits.SelectMany(u => u.AllMember))
                    GetPlayerStats(member.GranId).playdata.IsHavingControl = false;
            }
            else if (Match.ParticipantTeams.AllUnits.Count() == 2)
            {
                //ちょっと処理が面倒なので2Unitでなければ集計取らないようにする

                Unit FirstHavingUnit = null;
                bool isHavingControl = true;
                Dictionary<Unit, int> roundUnitScore = new Dictionary<Unit, int>
                {
                    { Match.ParticipantTeams.AllUnits[0], 0 },
                    { Match.ParticipantTeams.AllUnits[1], 0 }
                };

                //最初に得点を得たユニットがその後で一番スコアが高いユニットでいられたのかを確認する
                //もしそうでなくなったのであればfalseにして処理を終了する
                for (int roundIndex = 0; roundIndex < Match.Rule.MaxRound; roundIndex++)
                {
                    //全てのラウンドを回す

                    foreach (var unit in Match.ParticipantTeams.AllUnits)
                    {
                        roundUnitScore[unit] += _scoreDic[unit.AllMember[0].GranId].Sum(s => s.TotalScore());
                    }

                    if (FirstHavingUnit == null)
                    {
                        FirstHavingUnit = roundUnitScore.OrderByDescending(v => v.Value).FirstOrDefault().Key;
                    }
                    else
                    {
                        if (!roundUnitScore.OrderByDescending
                                    (v => v.Value).
                                FirstOrDefault().
                                Key.Id.Equals(FirstHavingUnit.Id))
                        {
                            isHavingControl = false;
                            break;
                        }
                    }
                }
                var isFirstHavingUnit = Match.ParticipantTeams.AllUnits.First
                    (unit => unit.Id.Equals(FirstHavingUnit.Id));

                foreach (var member in isFirstHavingUnit.AllMember)
                    GetPlayerStats(member.GranId).playdata.IsHavingControl = isHavingControl;
            }
        }

        /// <summary>
        /// 相手に 100 点以上のリードをつけられた状態から点数を逆転して勝利したかどうか
        /// </summary>
        private void ConfirmIsComeBack()
        {
            //勝者のUnitが過去100点以上の差を付けられていたのかを確認する
            if (Match.ParticipantTeams.AllUnits.Count() == 2 &&
                !Match.ParticipantTeams.AllUnits.All(u => u.GameRanking == 1))
            {
                //処理が面倒なので2Unitでなければ集計取らないようにする

                bool isComeBack = false;
                Dictionary<Unit, int> roundUnitScore = new Dictionary<Unit, int>();

                Unit winner = Match.ParticipantTeams.AllUnits.First(u => u.GameRanking == 1);
                Unit loser = Match.ParticipantTeams.AllUnits.First(u => u.GameRanking == 2);
                roundUnitScore.Add(winner, 0);
                roundUnitScore.Add(loser, 0);

                for (int roundIndex = 0; roundIndex < Match.Rule.MaxRound; roundIndex++) //全てのラウンドを回す
                {
                    roundUnitScore[winner] += _scoreDic[winner.AllMember[0].GranId].Sum(s => s.TotalScore());
                    roundUnitScore[loser] += _scoreDic[loser.AllMember[0].GranId].Sum(s => s.TotalScore());

                    if ((roundUnitScore[loser] - roundUnitScore[winner]) >= 100)
                    {
                        //勝者は100点以上の差を付けて負けてたときがあればloop抜ける
                        isComeBack = true;
                        break;
                    }
                }

                foreach (var member in winner.AllMember)
                    GetPlayerStats(member.GranId).playdata.IsComeback = isComeBack;
            }
        }


        /// <summary>
        /// Triple率の割り出し処理
        /// </summary>
        private void ConfirmTripleHitRate()
        {
            var playerThrows = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    granId = player.GranId,
                    throws = team.AllThrowsByMember(player.GranId)
                }
            );

            foreach (var throwsPerPlayer in playerThrows)
            {
                //Debug.Log("throwsPerPlayer:" + throwsPerPlayer.granId);
                int HitCount = throwsPerPlayer.throws.Count(t => { return t.VirtualHitArea.IsTriple; });
                int throwCount = throwsPerPlayer.throws.Count(t => { return !t.IsEmpty; });
                float hittripleRate = throwCount == 0 ? 0.00f : (float)HitCount / (float)throwCount;
                //Debug.Log($"{hittripleRate} =(float)HitCount{HitCount} / (float)throwCount {throwCount}");

                GetPlayerStats(throwsPerPlayer.granId).playdata.TripleRate = (float)Math.Round
                    (hittripleRate, 3, MidpointRounding.AwayFromZero);
            }
        }

        /// <summary>
        /// MarkCount数とMarkCount率を割り出す処理
        /// </summary>
        public void ConfirmMarkCountRate()
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                foreach (var round in playerPerRound.round)
                {
                    int markCount = GetCurrentPlayerRoundMarks(playerPerRound.granId, round.No - 1).Sum();
                    if(markCount == 5) {
                        GetPlayerStats(playerPerRound.granId).playdata.FiveMarksCount++;
                    }
                    else if(markCount == 6) {
                        GetPlayerStats(playerPerRound.granId).playdata.SixMarksCount++;
                    }
                    else if(markCount == 7) {
                        GetPlayerStats(playerPerRound.granId).playdata.SevenMarksCount++;
                    }
                    else if(markCount == 8) {
                        GetPlayerStats(playerPerRound.granId).playdata.EightMarksCount++;
                    }
                    else if(markCount == 9) {
                        GetPlayerStats(playerPerRound.granId).playdata.NineMarksCount++;
                    }
                }

                if(playerPerRound.round.Length == 0) {
                    return;
                }

                GetPlayerStats(playerPerRound.granId).playdata.FiveMarksRate = (float)Math.Round(GetPlayerStats(playerPerRound.granId).playdata.FiveMarksCount / (float)playerPerRound.round.Length, 3, MidpointRounding.AwayFromZero);
                GetPlayerStats(playerPerRound.granId).playdata.SixMarksRate = (float)Math.Round(GetPlayerStats(playerPerRound.granId).playdata.SixMarksCount / (float)playerPerRound.round.Length, 3, MidpointRounding.AwayFromZero);
                GetPlayerStats(playerPerRound.granId).playdata.SevenMarksRate = (float)Math.Round(GetPlayerStats(playerPerRound.granId).playdata.SevenMarksCount / (float)playerPerRound.round.Length, 3, MidpointRounding.AwayFromZero);
                GetPlayerStats(playerPerRound.granId).playdata.EightMarksRate = (float)Math.Round(GetPlayerStats(playerPerRound.granId).playdata.EightMarksCount / (float)playerPerRound.round.Length, 3, MidpointRounding.AwayFromZero);
                GetPlayerStats(playerPerRound.granId).playdata.NineMarksRate = (float)Math.Round(GetPlayerStats(playerPerRound.granId).playdata.NineMarksCount / (float)playerPerRound.round.Length, 3, MidpointRounding.AwayFromZero);
            }
        }

        /// <summary>
        /// ShootRateを割り出す処理
        /// </summary>
        private void ConfirmShootRate()
        {
            var playerThrows = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    granId = player.GranId,
                    throws = team.AllThrowsByMember(player.GranId)
                }
            );

            foreach (var throwsPerPlayer in playerThrows)
            {
                int HitCount = throwsPerPlayer.throws.Count
                (
                    t =>
                    {
                        if (t.IsMiss || t.IsEmpty) return false;
                        return !t.VirtualHitArea.IsMiss && !t.VirtualHitArea.IsOutBoard;
                    }
                );
                float ShootRate = throwsPerPlayer.throws.Length == 0
                    ? 0.00f
                    : (float)HitCount / (float)throwsPerPlayer.throws.Length;
                GetPlayerStats(throwsPerPlayer.granId).playdata.Shoot = (float)Math.Round
                    (ShootRate, 3, MidpointRounding.AwayFromZero);
            }
        }

        /// <summary>
        /// Keep率を割り出す処理
        /// (3 投とも有効打だったラウンド)/ ( 総ラウンド数 )
        /// </summary>
        private void ConfirmKeepRate()
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    unitId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                ////Debug.Log("throwsPerPlayer:" + playerPerRound.granId);
                int keepcount = 0;
                float keeprate = 0.0f;
                if (GetPlayerStats(playerPerRound.granId).playdata.Keep != 0.0f) continue;

                foreach (var round in playerPerRound.round)
                {
                    keepcount += round.GetRoundComponent<SegmentInput>().Throws.All
                    (
                        t =>
                        {
                            if (t.IsEmpty || t.IsMiss) return false;
                            return !t.VirtualHitArea.IsMiss && !t.VirtualHitArea.IsOutBoard;
                        }
                    )
                        ? 1
                        : 0;
                }
                ////Debug.Log($"keepcount{keepcount} / playerPerRound.round.Length{playerPerRound.round.Length}");
                keeprate = playerPerRound.round.Length == 0
                    ? 0.00f
                    : (float)keepcount / (float)playerPerRound.round.Length;
                GetPlayerStats(playerPerRound.granId).playdata.Keep = (float)Math.Round
                    (keeprate, 3, MidpointRounding.AwayFromZero);
            }
        }


        /// <summary>
        /// 1ラウンド目のMarkCount数とOpenしたかを割り出す処理
        /// </summary>
        private void ConfirmFirstRoundMarkCount
        (
            CricketMarkPosition[] cricketTargetMarks
        )
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    unitId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                GetPlayerStats(playerPerRound.granId).playdata.FirstRoundMarkCount = GetCurrentTeamRoundMarks
                        (playerPerRound.unitId, 0).
                    Sum();

                var firstRound = GetCurrentTeamMemberRound(playerPerRound.unitId, 0);
                bool openFirstRound = false;

                foreach (var item in cricketTargetMarks)
                {
                    if (firstRound.TotalMarkCountByPosition(item) >= Match.Rule.MaxDisplayMarkCount)
                    {
                        openFirstRound = true;
                        break;
                    }
                }
                GetPlayerStats(playerPerRound.granId).playdata.IsFirstRoundOpen = openFirstRound;
            }
        }

        /// <summary>
        /// 全ラウンドのMarkCount数を割り出す処理
        /// </summary>
        private void ConfirmAllRoundTargetMarkCount
        (
            CricketMarkPosition[] cricketTargetMarks
        )
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    unitId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                int[] targetmarks = new int[cricketTargetMarks.Length];

                foreach (var crround in playerPerRound.round)
                {
                    int targetmarksIndex = 0;

                    foreach (var item in cricketTargetMarks)
                    {
                        targetmarks[targetmarksIndex] += GetCurrentTeamMemberRound
                                (playerPerRound.unitId, crround.No - 1).
                            TotalMarkCountByPosition(item);
                        targetmarksIndex++;
                    }
                }

                GetPlayerStats(playerPerRound.granId).playdata.TargetMarkCounts = targetmarks;
            }
        }

        /// <summary>
        /// 20~Bullのエリアそれぞれに 3マーク以上いれるために何投かかったか　最小 0、最大 3
        /// </summary>
        private void ConfirmDartCountToGet3Marks
        (
            CricketMarkPosition[] cricketTargetMarks
        )
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    unitId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                Dictionary<CricketMarkPosition, (int hitCount, int markCount, bool open)> dartCountToGet3MarksDic =
                    new Dictionary<CricketMarkPosition, (int, int, bool)>() {
                        {
                            CricketMarkPosition.MarkPosition15, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPosition16, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPosition17, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPosition18, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPosition19, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPosition20, (0, 0, false)
                        }, {
                            CricketMarkPosition.MarkPositionBull, (0, 0, false)
                        }
                    };
                ////Debug.Log($"(playerPerRound {playerPerRound.granId}) ");
                int[] dartCountToGet3Marks = new int[cricketTargetMarks.Length];

                foreach (var crRound in playerPerRound.round)
                {
                    var throws = GetCurrentTeamMemberRound(playerPerRound.unitId, crRound.No - 1).Marks;

                    foreach (var t in throws)
                    {
                        if (dartCountToGet3MarksDic.Keys.Any(key => key == t.Position))
                        {
                            var isOpen = dartCountToGet3MarksDic[t.Position].open;

                            if (!isOpen)
                            {
                                //もしこのスローの時点でOpenしてなければマーク数合計とhitCountの合計数を1加算
                                var currentmarkCount = dartCountToGet3MarksDic[t.Position].markCount;
                                var currenthitCount = dartCountToGet3MarksDic[t.Position].hitCount;

                                currentmarkCount += t.MarkCount;
                                currenthitCount++;
                                //その後もし3マーク超えてればopenしたことにして次はこの処理に入らないようにする
                                isOpen = currentmarkCount >= Match.Rule.MaxDisplayMarkCount;
                                ////Debug.Log($"(t.Position{t.Position})  currentmarkCount:{currentmarkCount} currenthitCount:{currenthitCount} isOpen:{isOpen}");
                                dartCountToGet3MarksDic[t.Position] = (currenthitCount, currentmarkCount, isOpen);
                            }
                            //
                        }
                    }
                }

                var resultArray = new int[] {
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition15].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition15].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition16].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition16].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition17].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition17].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition18].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition18].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition19].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition19].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition20].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPosition20].hitCount
                        : 0,
                    dartCountToGet3MarksDic[CricketMarkPosition.MarkPositionBull].open
                        ? dartCountToGet3MarksDic[CricketMarkPosition.MarkPositionBull].hitCount
                        : 0
                };

                GetPlayerStats(playerPerRound.granId).playdata.DartCountToGet3Marks = resultArray;
            }
        }

        /// <summary>
        /// HasOpenedAreasを割り出す処理//todo ここただ単にopenしてるかだけを見るのではなく一番最初にこのエリアをopenしたかどうかを調べる
        /// </summary>
        private void ConfirmHasOpenedAreas
        (
            CricketMarkPosition[] cricketTargetMarks
        )
        {
            var allplayerRounds = Match.ParticipantTeams.AllUnits.SelectMany
            (
                team => team.AllMember,
                (
                    team,
                    player
                ) => new
                {
                    unitId = team.Id,
                    granId = player.GranId,
                    round = team.AllRoundsByMember(player.GranId)
                }
            );

            foreach (var playerPerRound in allplayerRounds)
            {
                GetPlayerStats(playerPerRound.granId).playdata.HasOpenedAreas = GetPlayerStats
                        (playerPerRound.granId).
                    playdata.TargetOpens.Select(t => t == 1 ? true : false).
                    ToArray();
            }
        }

        /// <summary>
        /// HasOpenedAreas ClosedAreaBeforeOpponentScoredを割り出す処理
        /// </summary>
        private void ConfirmOpenClosedArea()
        {
            if (Match.ParticipantTeams.AllUnits.Count() == 2 &&
                Match.ParticipantTeams.AllUnits.SelectMany(u => u.AllMember).Count() == 2)
            {
                //処理が面倒なので2Unitのシングルスでなければ集計取らないようにする

                Dictionary<int, (int unitAmarkCount, int unitBmarkCount, string openGranId, int openRoundNo, string
                    closeGranId)> centerMarkDic = CreateCenterMarkDic();
                var index = 0;

                foreach (var item in centerMarkDic)
                {
                    if (!item.Value.openGranId.Equals(string.Empty))
                    {
                        ////Debug.Log("open pos centerMarkDic pos:" + item.Key);
                        ////Debug.Log($"Open UnitAmarkCount{item.Value.unitAmarkCount} UnitBmarkCount{item.Value.unitBmarkCount} , openGranId{item.Value.openGranId} closeGranid{item.Value.closeGranId}:");
                        GetPlayerStats(item.Value.openGranId).playdata.HasOpenedAreas[index] = true;
                    }

                    if (!item.Value.closeGranId.Equals(string.Empty))
                    {
                        ////Debug.Log("close pos centerMarkDic pos:" + item.Key);
                        ////Debug.Log($"Close UnitAmarkCount{item.Value.unitAmarkCount} UnitBmarkCount{item.Value.unitBmarkCount} , openGranId{item.Value.openGranId} closeGranid{item.Value.closeGranId}:");
                        ////Debug.Log("checkClose " + (!item.Value.closeGranId.Equals(item.Value.openGranId)));
                        GetPlayerStats(item.Value.closeGranId).playdata.HasClosedAreaBeforeOpponentScored[index] =
                            !item.Value.closeGranId.Equals(item.Value.openGranId);
                    }

                    index++;
                }
            }
        }

        void ConfirmRoundSummary() {
            var roundsPerPlayers = Match.ParticipantTeams.AllUnits
                .SelectMany(unit => unit.AllMember, (unit, player) =>
                (
                    unit : unit,
                    player : player,
                    rounds : unit.AllRoundsByMember(player.GranId)
                ));


            foreach (var roundsPerPlayer in roundsPerPlayers) {
                List<CricketRoundSummary> roundSummaries = new();
                foreach (var round in roundsPerPlayer.rounds) {
                    var throwSummary = round.GetRoundComponent<SegmentInput>().Throws
                        .Where(th => !th.IsEmpty && th.ActuaryHitArea.Code != SegmentCode.Change)
                        .Select(th =>
                        {
                            var marks = Match.Rule.TargetPositions
                                .ToDictionary(
                                    pos => pos,
                                    pos =>
                                    {
                                        var totalposMarkCountUntilPreviousThrow = TotalMarkCountAtPositionByPlayer(round.Thrower.GranId, pos, round.No - 1, th.No - 2);
                                        var markCountAtThrow = TotalMarkCountAtPositionByPlayer(round.Thrower.GranId, pos, round.No - 1, th.No - 1);
                                        bool alreadyOpened = totalposMarkCountUntilPreviousThrow >= 3;
                                        bool openByThisThrow = markCountAtThrow + totalposMarkCountUntilPreviousThrow >= 3;
                                        (int markCountToOpen, int markCountToEarnPoint) datas = (
                                            alreadyOpened ? 0 : openByThisThrow ? 3 - totalposMarkCountUntilPreviousThrow : markCountAtThrow,
                                            alreadyOpened ? markCountAtThrow : openByThisThrow ? markCountAtThrow - (3 - totalposMarkCountUntilPreviousThrow) : 0
                                        );
                                        return datas;
                                    });
                            return new CricketThrowsSummary(
                                th,
                                Score(roundsPerPlayer.unit.Id, round.No - 1, th.No - 1),
                                marks
                            );
                        });
                    roundSummaries.Add(new CricketRoundSummary(round, throwSummary.ToArray()));
                }
                GetPlayerStats(roundsPerPlayer.player.GranId).playdata.RoundSummaries = roundSummaries.ToArray();
            }
        }

        private Dictionary<int, (int unitAmarkCount, int unitBmarkCount, string openGranId, int openRoundNo, string
            closeGranId)> CreateCenterMarkDic()
        {
            Dictionary<int, (int unitAmarkCount, int unitBmarkCount, string openGranId, int openRoundNo, string
                closeGranId)> centerMarkDic = new Dictionary<int, (int, int, string, int, string)>() {
                {
                    Segment.Single15In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.Single16In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.Single17In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.Single18In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.Single19In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.Single20In.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }, {
                    Segment.BullOut.PositionCode, (0, 0, string.Empty, 0, string.Empty)
                }
            };

            for (int roundIndex = 1; roundIndex <= Match.Rule.MaxRound; roundIndex++)
            {
                var unitA = Match.ParticipantTeams.AllUnits[0];
                var unitB = Match.ParticipantTeams.AllUnits[1];
                //unitA->unitBの順でラウンドに入ってるthrowSegmentの値を足し算させて
                //先にOpenしたのはいつで誰かを履歴を追う
                //また同時期に 相手ユニットがオープンしたエリアを、後のラウンドで追加得点される前にクローズしたかどうか　（相手がオープンしたラウンドに得点した分は、追加得点に含まない）も追う
                int lastRoundNo = unitA.Progress.CurrentRoundNo;

                if (roundIndex <= lastRoundNo)
                {
                    var unitARound = unitA.Progress.Round(roundIndex);
                    var currentThrowerGranId = unitARound.Thrower.GranId;

                    if (!unitARound.IsEmptyRound)
                    {
                        var throws = unitARound.GetRoundComponent<SegmentInput>().Throws;

                        foreach (var throwSegment in throws)
                        {
                            if (throwSegment.IsEmpty || throwSegment.IsMiss) continue;
                            if (throwSegment.VirtualHitArea.IsMiss || throwSegment.VirtualHitArea.IsOutBoard) continue;
                            var targetCodition = centerMarkDic[throwSegment.VirtualHitArea.PositionCode];

                            var result = CheckMarkOpenorClose
                            (
                                roundIndex,
                                throwSegment,
                                (targetCodition.unitAmarkCount, targetCodition.openGranId, targetCodition.openRoundNo,
                                    targetCodition.closeGranId),
                                currentThrowerGranId
                            );
                            centerMarkDic[throwSegment.VirtualHitArea.PositionCode] = (result.markCount,
                                targetCodition.unitBmarkCount, result.openGranId, result.openRoundNo,
                                result.closeGranId);
                        }
                    }
                }
                lastRoundNo = unitB.Progress.CurrentRoundNo;

                if (roundIndex <= lastRoundNo)
                {
                    var unitBRound = unitB.Progress.Round(roundIndex);
                    var currentThrowerGranId = unitBRound.Thrower.GranId;

                    if (!unitBRound.IsEmptyRound)
                    {
                        var throws = unitBRound.GetRoundComponent<SegmentInput>().Throws;

                        foreach (var throwSegment in throws)
                        {
                            if (throwSegment.IsEmpty || throwSegment.IsMiss) continue;
                            if (throwSegment.VirtualHitArea.IsMiss || throwSegment.VirtualHitArea.IsOutBoard) continue;

                            var targetCodition = centerMarkDic[throwSegment.VirtualHitArea.PositionCode];

                            var result = CheckMarkOpenorClose
                            (
                                roundIndex,
                                throwSegment,
                                (targetCodition.unitBmarkCount, targetCodition.openGranId, targetCodition.openRoundNo,
                                    targetCodition.closeGranId),
                                currentThrowerGranId
                            );
                            centerMarkDic[throwSegment.VirtualHitArea.PositionCode] = (targetCodition.unitAmarkCount,
                                result.markCount, result.openGranId, result.openRoundNo, result.closeGranId);
                        }
                    }
                }
            }
            return centerMarkDic;
        }

        private (int markCount, string openGranId, int openRoundNo, string closeGranId) CheckMarkOpenorClose
        (
            int currentRoundNo,
            Throw throwSegment,
            (int markCount, string openGranId, int openRoundNo, string closeGranId) markAreaCondition,
            string currentThrowerGranId
        )
        {

            var currentMarkCount = markAreaCondition.markCount;

            var openGranId = markAreaCondition.openGranId;
            var openRoundNo = markAreaCondition.openRoundNo;
            var closeGranId = markAreaCondition.closeGranId;

            bool isOpen = !openGranId.Equals(string.Empty);
            bool isClose = !closeGranId.Equals(string.Empty);
            currentMarkCount += throwSegment.VirtualHitArea.Multiplier;

            //if (throwSegment.VirtualHitArea.PositionCode == 20)
            //{
            //    //Debug.Log("throwSegment.VirtualHitArea.PositionCode:" + throwSegment.VirtualHitArea.PositionCode);
            //    //Debug.Log($"openRoundNo{openRoundNo}, currentRoundNo{currentRoundNo})");
            //    //Debug.Log($"openGranId{openGranId}, currentThrowerGranId{currentThrowerGranId})");
            //}
            if (currentMarkCount >= Match.Rule.MaxDisplayMarkCount)
            {
                if (!isOpen)
                {
                    openGranId = currentThrowerGranId;
                    openRoundNo = currentRoundNo;
                }
                else if (isOpen && !isClose)
                {
                    if (openRoundNo != currentRoundNo)
                    {
                        closeGranId = currentThrowerGranId;
                    }
                    else if (!openGranId.Equals(currentThrowerGranId) && !openGranId.Equals(closeGranId))
                    {
                        closeGranId = currentThrowerGranId;
                    }
                    // //Debug.Log($"(currentMarkCount{currentMarkCount}, openGranId{openGranId}, closeGranId{closeGranId})");
                }
            }
            //if (throwSegment.VirtualHitArea.PositionCode == 20) {
            //    //Debug.Log($"(currentMarkCount{currentMarkCount}, openGranId{openGranId}, closeGranId{closeGranId})");
            //}
            return (currentMarkCount, openGranId, openRoundNo, closeGranId);
        }

        #endregion

        private PlayerCRData GetPlayerStats
        (
            string granId
        )
        {
            return _playerdatas.First(s => s.GranId.Equals(granId));
        }

        private class PlayerCRData
        {
            internal readonly string unitId;
            internal readonly string GranId;
            internal PlaydataCR playdata;

            internal PlayerCRData
            (
                Unit unit,
                string granId
            )
            {
                unitId = unit.Id;
                GranId = granId;
                playdata = new PlaydataCR()
                {
                    RealTimeStats = 0,
                    StatsFor80 = new CricketStats(),
                    StatsFor100 = new CricketStats(),
                    TargetOpens = new int[7],
                    TargetCloses = new int[7],

                    HasOpenedAreas = new bool[7],
                    HasClosedAreaBeforeOpponentScored = new bool[7],
                    DartCountToGet3Marks = new int[7],
                    IsFirstRoundOpen = false,
                    FirstRoundMarkCount = 0,
                    IsHavingControl = false,
                    IsComeback = false,
                    FiveMarksCount = 0,
                    SixMarksCount = 0,
                    SevenMarksCount = 0,
                    EightMarksCount = 0,
                    NineMarksCount = 0,
                    FiveMarksRate = 0,
                    SixMarksRate = 0,
                    SevenMarksRate = 0,
                    EightMarksRate = 0,
                    NineMarksRate = 0
                };
            }
        }

        private struct PlaydataCR
        {
            internal double RealTimeStats;
            internal CricketStats StatsFor80;
            internal CricketStats StatsFor100;
            internal float Shoot;
            internal float Keep;

            internal int FiveMarksCount;
            internal int SixMarksCount;
            internal int SevenMarksCount;
            internal int EightMarksCount;
            internal int NineMarksCount;
            internal float FiveMarksRate;
            internal float SixMarksRate;
            internal float SevenMarksRate;
            internal float EightMarksRate;
            internal float NineMarksRate;
            internal float TripleRate;

            internal int[] TargetOpens;
            internal int[] TargetCloses;

            internal bool[] HasOpenedAreas;
            internal bool[] HasClosedAreaBeforeOpponentScored;
            internal int[] DartCountToGet3Marks;
            internal bool IsFirstRoundOpen;
            internal int FirstRoundMarkCount;
            internal bool IsHavingControl;
            internal bool IsComeback;

            internal int[] TargetMarkCounts;

            internal CricketRoundSummary[] RoundSummaries;
        }

        private struct CricketStats
        {
            internal double? MPR;
        }
    }
}