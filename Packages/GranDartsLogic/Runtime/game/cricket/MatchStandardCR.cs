using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.interfaces;

namespace com.luxza.grandartslogic.domain.game.cricket
{
    public class MatchStandardCR : IMatch<GameRuleStandardCR>
    {
        public MatchStandardCR
        (
            Unit[] teams,
            GameRuleStandardCR rule,
            bool isMedley
        )
        {
            Rule = rule;
            Participants = new Participants(teams);
            IsMedley = isMedley;
            for (int i = 0; i < Participants.Count; i++)
            {
                ParticipantTeams.Unit(i).InitializeProgress(rule.ThrowsPerRound, GameCode);
            }
        }

        public readonly bool IsMedley;

        internal CricketHandicap SetManuelHandicap(string teamId, int score, int[] marks) => Rule.SetManuelHandicap(teamId, score, marks);
        internal CricketHandicap CreateAutoHandicap(Unit unit, double ratingDifference) => Rule.CreateAutoHandicap(unit, ratingDifference);

        internal CricketHandicap NoneHandicap(string teamId) => Rule.CreateNoneHandicap(teamId);

        public CricketMarkPosition[] TargetPositions => Rule.TargetPositions;

        public bool IsHiddenMode => Rule.MarkDisplayOption != CricketMarkDisplayOption.Open;
        private Participants Participants { get; }

        public bool IsNextRoundToOverMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team => team.Progress.CurrentRoundNo >= Rule.MaxRound);
            }
        }

        public bool IsReachAllTeamsToMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team =>
                {
                    return team.Progress.CurrentRoundNo >= Rule.MaxRound &&
                           team.Progress.CurrentRound.GetRoundComponent<IRoundInput>().IsAllThrowsFixed();
                });
            }
        }

        public bool IsReachEndOfRound(int teamIndex)
        {
            return TeamThrowCountOnLatestRound(teamIndex) >= Rule.ThrowsPerRound;
        }

        public Throw LatestThrow(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress.LatestThrow;
        }

        public Participants ParticipantTeams => Participants;

        public GameCode GameCode => GameCode._StandardCR;
        public GameRuleStandardCR Rule { get; }

        public void StoreHit(int teamIndex, Segment actuaryHit, Segment virtualHit, Vector2? hitPosition = null)
        {
            var progress = ParticipantTeams.Unit(teamIndex).Progress;
            progress.FixThrow(actuaryHit, virtualHit, hitPosition);
        }

        public int TeamThrowCountOnLatestRound(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress?.CurrentRound.ThrowCount ?? 0;
        }

        public RefereeStandardCR CreateReferee(IEnumerable<CricketHandicap> handicap)
        {
            return new RefereeStandardCR(this, handicap);
        }
    }
}