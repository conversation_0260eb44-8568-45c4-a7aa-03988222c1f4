using System;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandartslogic.domain.game.KickDown
{
    public class GameRuleKickDown : IGameRule
    {
        public KickDownOverScoreSetting OverScoreSetting { get; }
        public InCondition InCondition { get; }
        public OutCondition OutCondition { get; }
        public BullOption BullOption { get; }
        public GameCode? Code { get; }
        public int MaxRound { get; }
        public int ThrowsPerRound { get; }
        public int MaximumNumberOfParticipants { get; }
        public ZeroOneEndScore EndScore => Code.ToEndScore();

        public int EightyPercentScore => EndScore.EightyPercentScore();

        public GameRuleKickDown(
            GameCode code,
            int maxRound = 20,
            int throwsPerRound = 3,
            int maximumNumberOfParticipants = 4,
            InCondition inCondition = InCondition.OpenIn,
            OutCondition outCondition = OutCondition.OpenOut,
            BullOption option = BullOption.FatBull,
            KickDownOverScoreSetting KickDownOverScoreSetting = KickDownOverScoreSetting.Random
        )
        {
            if (maxRound <= 0 && maxRound != -1)
            {
                throw new ArgumentException($"invalid max of round. -> {maxRound}");
            }
            Code = code;
            OverScoreSetting = KickDownOverScoreSetting;
            MaxRound = maxRound;
            ThrowsPerRound = throwsPerRound;
            InCondition = inCondition;
            OutCondition = outCondition;
            MaximumNumberOfParticipants = maximumNumberOfParticipants;
            BullOption = option;
        }

        public static readonly Award[] AchievableAward = new Award[] {
            Award.TonEighty,
            Award.ThreeInTheBlack,
            Award.ThreeInABed,
            Award.HatTrick,
            Award.LowTon,
            Award.HighTon,
        };

        public GameRuleKickDown CloneWithCode
        (
            GameCode code
        )
        {
            return new GameRuleKickDown
            (
                code: code,
                maxRound: MaxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: InCondition,
                outCondition: OutCondition,
                option: BullOption,
                KickDownOverScoreSetting: OverScoreSetting
            );
        }

        public GameRuleKickDown CloneWithMaxRound
        (
            int maxRound
        )
        {
            return new GameRuleKickDown
            (
                code: Code.Value,
                maxRound: maxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: InCondition,
                outCondition: OutCondition,
                option: BullOption,
                KickDownOverScoreSetting: OverScoreSetting
            );
        }

        public GameRuleKickDown CloneWithBullOption
        (
            BullOption bullOption
        )
        {
            return new GameRuleKickDown
            (
                code: Code.Value,
                maxRound: MaxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: InCondition,
                outCondition: OutCondition,
                option: bullOption,
                KickDownOverScoreSetting: OverScoreSetting
            );
        }

        public GameRuleKickDown CloneWithInCondition
        (
            InCondition inCondition
        )
        {
            return new GameRuleKickDown
            (
                code: Code.Value,
                maxRound: MaxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: inCondition,
                outCondition: OutCondition,
                option: BullOption,
                KickDownOverScoreSetting: OverScoreSetting
            );
        }

        public GameRuleKickDown CloneWithOutCondition
        (
            OutCondition outCondition
        )
        {
            return new GameRuleKickDown
            (
                code: Code.Value,
                maxRound: MaxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: InCondition,
                outCondition: outCondition,
                option: BullOption,
                KickDownOverScoreSetting: OverScoreSetting
            );
        }

        public GameRuleKickDown CloneWithOverScoreSetting
        (
            KickDownOverScoreSetting overScoreSetting
        )
        {
            return new GameRuleKickDown
            (
                code: Code.Value,
                maxRound: MaxRound,
                throwsPerRound: ThrowsPerRound,
                maximumNumberOfParticipants: MaximumNumberOfParticipants,
                inCondition: InCondition,
                outCondition: OutCondition,
                option: BullOption,
                KickDownOverScoreSetting: overScoreSetting
            );
        }
    }

    /// <summary>
    /// KickDownでOverScoreが発生した時引く点数
    /// Random 1~180がランダムで選ばれてその点数を引く
    /// 100 100点引かれる
    /// 150 150点引かれる
    /// </summary>
    public enum KickDownOverScoreSetting
    {
        Random,
        Minus100,
        Minus150,
    }
}