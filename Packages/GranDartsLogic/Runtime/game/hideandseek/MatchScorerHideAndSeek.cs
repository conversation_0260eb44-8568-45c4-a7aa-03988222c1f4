using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandartslogic.domain.game.hideandseek
{
    public class MatchScorerHideAndSeek : IMatchScorer
    {
        private readonly List<UnitGameData> _unitGameDataList = new List<UnitGameData>();
        /// <summary>
        /// 具体的打镖详情
        /// </summary>
        private Dictionary<(int, int, int), List<WindowEntity>> _hitArea = new Dictionary<(int, int, int), List<WindowEntity>>();
        private Dictionary<(int, int), int> _angryAnimalRecord = new Dictionary<(int, int), int>();
        /// <summary>
        /// 打中bull增加生气动物的上限
        /// </summary>
        private int _maxPenaltyCount = 6;
        /// <summary>
        /// 动物得分设定
        /// </summary>
        private int[] _animalScoreList = new int[9] { 10, 30, 50, 70, 90, 110, 130, 150, 170 };
        //打中已打开窗户有动物时的奖励分
        private readonly int _singleHitBonus = 20;
        private readonly int _doubleHitBonus = 100;
        private readonly int _tripleHitBonus = 150;
        private readonly List<int> _missCount = new List<int>();
        private readonly List<int> _noFoundCount = new List<int>();
        private readonly List<int> _foundCount = new List<int>();
        private object[] _hitObjects = new object[10];

        internal MatchScorerHideAndSeek(MatchHideAndSeek match)
        {
            Match = match;
            var units = match.ParticipantTeams.AllUnits;
            for (int i = 0; i < units.Length; i++)
            {
                _unitGameDataList.Add(new UnitGameData(units[i].Id));
            }
            RefreshPlayerData(units[0].Id, 0, 1, 0);
            ResetWindows(0, 1);
        }

        public MatchHideAndSeek Match
        {
            get;
        }

        /// <summary>
        /// 指定したインデックスのチームのトータルのスコアを返します。
        /// </summary>
        /// <param name="unitId">チームのインデックス</param>
        /// <returns>トータルのスコア</returns>
        /// <exception cref="KeyNotFoundException">指定したチームのインデックスが登録されていない場合</exception>
        public int CurrentScore(string unitId)
        {
            var roundCount = Match.ParticipantTeams.Unit(unitId).Progress.Rounds.Count;
            var score = 0;
            for (int i = 1; i <= roundCount; i++)
            {
                foreach (var item in GetUnitData(unitId).RoundScore.Where(x => x.Key.Item1 == i))
                {
                    score += item.Value;
                    if (score < 0)
                    {
                        score = 0;
                    }

                }
            }
            return score;
        }

        /// <summary>
        /// 指定したスコアを返します。
        /// </summary>
        /// <param name="unitId">チームのID</param>
        /// <param name="roundIndex">ラウンドのNo</param>
        /// <param name="throwIndex">投擲のNo</param>
        /// <returns>スコア</returns>
        /// <exception cref="KeyNotFoundException">指定したチームのインデックスが登録されていない場合</exception>
        /// <exception cref="IndexOutOfRangeException">指定したラウンドが範囲外、または投擲Noが範囲外の場合</exception>
        public int Score(string unitId, int roundIndex, int throwIndex)
        {
            var round = Match.ParticipantTeams.Unit(unitId).Progress.Rounds[roundIndex];
            if(round.InputType != RoundInputType.Segment) throw new InvalidOperationException("This round is not segment input type.");
            return Score(round.GetRoundComponent<SegmentInput>().Throws[throwIndex].VirtualHitArea);
        }

        public int TotalScoreInRound(string unitId, int roundIndex)
        {
            var score = 0;
            if (GetUnitData(unitId).RoundScore.Count <= 0)
            {
                return score;
            }

            var scoreList = GetUnitData(unitId).RoundScore.Where(x => x.Key.Item1 == roundIndex + 1);
            foreach (var item in GetUnitData(unitId).RoundScore.Where(x => x.Key.Item1 == roundIndex + 1))
            {
                if (score < 0 && item.Value > 0)
                {
                    score = 0;
                }
                score += item.Value;
            }
            return score;
        }

        /// <summary>
        /// セグメントに対応するスコアを返します。
        /// </summary>
        /// <param name="hit">セグメント</param>
        /// <returns>スコア</returns>
        public int Score(Segment hit)
        {
            return hit == null ? 0 : _scoreMap[hit];
        }

        /// <summary>
        /// 指定したラウンドのスコアをリセットします。
        /// </summary>
        /// <param name="unitId">チームのID</param>
        /// <param name="roundIndex">ラウンドのインデックス</param>
        internal void ResetScore(string unitId, int roundIndex)
        {
            Match.ParticipantTeams.Unit(unitId).Progress.Rounds[roundIndex].GetRoundComponent<IRoundInput>().DiscardAllInput();
        }

        private readonly Dictionary<Segment, int> _scoreMap = new Dictionary<Segment, int>()
        {
            {Segment.Single1In, 1},
            {Segment.Single1Out, 1},
            {Segment.Double1, 1 * 2},
            {Segment.Triple1, 1 * 3},
            {Segment.Single2In, 2},
            {Segment.Single2Out, 2},
            {Segment.Double2, 2 * 2},
            {Segment.Triple2, 2 * 3},
            {Segment.Single3In, 3},
            {Segment.Single3Out, 3},
            {Segment.Double3, 3 * 2},
            {Segment.Triple3, 3 * 3},
            {Segment.Single4In, 4},
            {Segment.Single4Out, 4},
            {Segment.Double4, 4 * 2},
            {Segment.Triple4, 4 * 3},
            {Segment.Single5In, 5},
            {Segment.Single5Out, 5},
            {Segment.Double5, 5 * 2},
            {Segment.Triple5, 5 * 3},
            {Segment.Single6In, 6},
            {Segment.Single6Out, 6},
            {Segment.Double6, 6 * 2},
            {Segment.Triple6, 6 * 3},
            {Segment.Single7In, 7},
            {Segment.Single7Out, 7},
            {Segment.Double7, 7 * 2},
            {Segment.Triple7, 7 * 3},
            {Segment.Single8In, 8},
            {Segment.Single8Out, 8},
            {Segment.Double8, 8 * 2},
            {Segment.Triple8, 8 * 3},
            {Segment.Single9In, 9},
            {Segment.Single9Out, 9},
            {Segment.Double9, 9 * 2},
            {Segment.Triple9, 9 * 3},
            {Segment.Single10In, 10},
            {Segment.Single10Out, 10},
            {Segment.Double10, 10 * 2},
            {Segment.Triple10, 10 * 3},
            {Segment.Single11In, 11},
            {Segment.Single11Out, 11},
            {Segment.Double11, 11 * 2},
            {Segment.Triple11, 11 * 3},
            {Segment.Single12In, 12},
            {Segment.Single12Out, 12},
            {Segment.Double12, 12 * 2},
            {Segment.Triple12, 12 * 3},
            {Segment.Single13In, 13},
            {Segment.Single13Out, 13},
            {Segment.Double13, 13 * 2},
            {Segment.Triple13, 13 * 3},
            {Segment.Single14In, 14},
            {Segment.Single14Out, 14},
            {Segment.Double14, 14 * 2},
            {Segment.Triple14, 14 * 3},
            {Segment.Single15In, 15},
            {Segment.Single15Out, 15},
            {Segment.Double15, 15 * 2},
            {Segment.Triple15, 15 * 3},
            {Segment.Single16In, 16},
            {Segment.Single16Out, 16},
            {Segment.Double16, 16 * 2},
            {Segment.Triple16, 16 * 3},
            {Segment.Single17In, 17},
            {Segment.Single17Out, 17},
            {Segment.Double17, 17 * 2},
            {Segment.Triple17, 17 * 3},
            {Segment.Single18In, 18},
            {Segment.Single18Out, 18},
            {Segment.Double18, 18 * 2},
            {Segment.Triple18, 18 * 3},
            {Segment.Single19In, 19},
            {Segment.Single19Out, 19},
            {Segment.Double19, 19 * 2},
            {Segment.Triple19, 19 * 3},
            {Segment.Single20In, 20},
            {Segment.Single20Out, 20},
            {Segment.Double20, 20 * 2},
            {Segment.Triple20, 20 * 3},
            {Segment.BullIn, 50},
            {Segment.BullOut, 50},
            {Segment.Miss, 0},
            {Segment.OUT, 0}
        };

        public List<WindowEntity> UnitWindowEntities(int currentUnitIndex)
        {
            return _hitArea.Last(item => item.Key.Item1 == currentUnitIndex).Value;
        }

        public object[] HitObjects()
        {
            return _hitObjects;
        }

        public int GetUnitSmileAnimal(string unitId)
        {
            return GetUnitData(unitId).SmileAnimalCount.Count > 0 ? GetUnitData(unitId).SmileAnimalCount.Last().Value : 0;
        }

        public int GetUnitAngryAnimal(string unitId)
        {
            return GetUnitData(unitId).AngryAnimalCount.Count > 0 ? GetUnitData(unitId).AngryAnimalCount.Last().Value : 0;
        }

        public int GetUnitHitTarget(string unitId)
        {
            return GetUnitData(unitId).HitTargetCount.Count > 0 ? GetUnitData(unitId).HitTargetCount.Last().Value : 0;
        }

        public int GetUnitHalf(string unitId)
        {
            return GetUnitData(unitId).HalfCount.Count > 0 ? GetUnitData(unitId).HalfCount.Last().Value : 0;
        }

        public int GetWindowsAngryAnimalCount(int unitIndex, int roundIndex)
        {
            return _angryAnimalRecord[(unitIndex, roundIndex)];
        }

        private UnitGameData GetUnitData(string unitIndex)
        {
            return _unitGameDataList.First(s => s.UnitId.Equals(unitIndex));
        }

        /// <summary>
        /// 字典的key是回合ID从1开始，value是int类型的是回合累加数据
        /// </summary>
        private class UnitGameData
        {
            internal readonly string UnitId;
            // 用于显示在左侧面板的回合分数
            internal Dictionary<(int, int), int> RoundScore;
            internal Dictionary<(int, int), int> SmileAnimalCount;
            internal Dictionary<(int, int), int> AngryAnimalCount;
            internal Dictionary<(int, int), int> HitTargetCount;
            internal Dictionary<int, int> HalfCount;

            internal UnitGameData(string unitId)
            {
                UnitId = unitId;
                RoundScore = new Dictionary<(int, int), int>();
                SmileAnimalCount = new Dictionary<(int, int), int>();
                AngryAnimalCount = new Dictionary<(int, int), int>();
                HitTargetCount = new Dictionary<(int, int), int>();
                HalfCount = new Dictionary<int, int>();

                InitData();
            }

            public void InitData()
            {
                SmileAnimalCount.Clear();
                AngryAnimalCount.Clear();
                HitTargetCount.Clear();

                SmileAnimalCount.Add((1, 1), 0);
                AngryAnimalCount.Add((1, 1), 0);
                HitTargetCount.Add((1, 1), 0);
            }
        }

        public void ChangeHalf(string unitId, int roundIndex, int throwIndex)
        {
            _hitObjects = new object[10];
            _hitObjects[6] = false;
            if (Match.Rule.MaxRound - throwIndex + _missCount.Sum() == 3)
            {
                _hitObjects[6] = true;
                // _missCount = Match.MaxThrowCountPerRound;
                GetUnitData(unitId).HalfCount[roundIndex]++;
                GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] += -CurrentScore(unitId) / 2;
            }
        }

        public void NoHalf()
        {
            _hitObjects[6] = false;
        }

        public int CrockCount()
        {
            return _missCount.Sum();
        }

        public void RefreshPlayerData(string unitId, int currentUnitIndex, int roundNo, int throwIndex)
        {
            if (!_angryAnimalRecord.ContainsKey((currentUnitIndex, roundNo)))
            {
                if (roundNo == 1 && currentUnitIndex == 0)
                {
                    _angryAnimalRecord.Clear();
                    _angryAnimalRecord.Add((0, 1), 1);
                }
                else
                {
                    var units = Match.ParticipantTeams.AllUnits;
                    var previousId = currentUnitIndex - 1 < 0 ? units.Length - 1 : currentUnitIndex - 1;
                    var number = _angryAnimalRecord[(previousId, previousId > currentUnitIndex ? roundNo - 1 : roundNo)];
                    _angryAnimalRecord.Add((currentUnitIndex, roundNo), number);
                }
            }

            if (!GetUnitData(unitId).RoundScore.ContainsKey((roundNo, throwIndex)))
            {
                GetUnitData(unitId).RoundScore.Add((roundNo, throwIndex), 0);
            }
            else
            {
                GetUnitData(unitId).RoundScore[(roundNo, throwIndex)] = 0;
            }
            if (!GetUnitData(unitId).SmileAnimalCount.ContainsKey((roundNo, throwIndex)))
            {
                GetUnitData(unitId).SmileAnimalCount.Add((roundNo, throwIndex), roundNo > 1 ? GetUnitData(unitId).SmileAnimalCount.Last().Value : 0);
            }
            else
            {
                GetUnitData(unitId).SmileAnimalCount[(roundNo, throwIndex)] = roundNo > 1 ? GetUnitData(unitId).SmileAnimalCount.Last().Value : 0;
            }
            if (!GetUnitData(unitId).AngryAnimalCount.ContainsKey((roundNo, throwIndex)))
            {
                GetUnitData(unitId).AngryAnimalCount.Add((roundNo, throwIndex), roundNo > 1 ? GetUnitData(unitId).AngryAnimalCount.Last().Value : 0);
            }
            else
            {
                GetUnitData(unitId).AngryAnimalCount[(roundNo, throwIndex)] = roundNo > 1 ? GetUnitData(unitId).AngryAnimalCount.Last().Value : 0;
            }
            if (!GetUnitData(unitId).HitTargetCount.ContainsKey((roundNo, throwIndex)))
            {
                GetUnitData(unitId).HitTargetCount.Add((roundNo, throwIndex), roundNo > 1 ? GetUnitData(unitId).HitTargetCount.Last().Value : 0);
            }
            else
            {
                GetUnitData(unitId).HitTargetCount[(roundNo, throwIndex)] = roundNo > 1 ? GetUnitData(unitId).HitTargetCount.Last().Value : 0;
            }
            if (!GetUnitData(unitId).HalfCount.ContainsKey(roundNo))
            {
                GetUnitData(unitId).HalfCount.Add(roundNo, roundNo > 1 ? GetUnitData(unitId).HalfCount.Last().Value : 0);
            }
            else
            {
                GetUnitData(unitId).HalfCount[roundNo] = roundNo > 1 ? GetUnitData(unitId).HalfCount.Last().Value : 0;
            }
        }

        public void ResetPlayerRound(string unitId, int currentUnitIndex, int roundNo)
        {
            _missCount.Clear();
            _noFoundCount.Clear();
            _foundCount.Clear();

            if (_angryAnimalRecord.ContainsKey((currentUnitIndex, roundNo)))
            {
                _angryAnimalRecord.Remove((currentUnitIndex, roundNo));
                if (currentUnitIndex == 0 && roundNo == 1)
                {
                    _angryAnimalRecord.Add((currentUnitIndex, roundNo), 1);
                }
                else
                {
                    var units = Match.ParticipantTeams.AllUnits;
                    var previousId = currentUnitIndex - 1 < 0 ? units.Length - 1 : currentUnitIndex - 1;
                    var number = _angryAnimalRecord[(previousId, previousId > currentUnitIndex ? roundNo - 1 : roundNo)];
                    _angryAnimalRecord.Add((currentUnitIndex, roundNo), number);
                }
            }

            for (int i = Match.Rule.ThrowsPerRound; i >= 1; i--)
            {
                if (_hitArea.ContainsKey((currentUnitIndex, roundNo, i)))
                {
                    _hitArea.Remove((currentUnitIndex, roundNo, i));
                }
                if (GetUnitData(unitId).RoundScore.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).RoundScore.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).SmileAnimalCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).SmileAnimalCount.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).AngryAnimalCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).AngryAnimalCount.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).HitTargetCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).HitTargetCount.Remove((roundNo, i));
                }
            }

            if (roundNo == 1)
            {
                GetUnitData(unitId).InitData();
            }

            if (GetUnitData(unitId).HalfCount.ContainsKey(roundNo))
            {
                if (roundNo == 1)
                {
                    GetUnitData(unitId).HalfCount[roundNo] = 0;
                }
                else
                {
                    GetUnitData(unitId).HalfCount.Remove(roundNo);
                }
            }
        }

        public void ResetPlayerThrow(string unitId, int currentUnitIndex, int roundNo, int throwIndex)
        {
            for (int i = Match.Rule.ThrowsPerRound - 1; i >= throwIndex - 1; i--)
            {
                if (_missCount.Count > i)
                {
                    _missCount.RemoveAt(i);
                }
            }
            for (int i = Match.Rule.ThrowsPerRound - 1; i >= throwIndex - 1; i--)
            {
                if (_noFoundCount.Count > i)
                {
                    _noFoundCount.RemoveAt(i);
                }
            }
            for (int i = Match.Rule.ThrowsPerRound - 1; i >= throwIndex - 1; i--)
            {
                if (_foundCount.Count > i)
                {
                    _foundCount.RemoveAt(i);
                }
            }

            for (int i = Match.Rule.ThrowsPerRound; i >= throwIndex; i--)
            {
                if (_hitArea.ContainsKey((currentUnitIndex, roundNo, i)))
                {
                    _hitArea.Remove((currentUnitIndex, roundNo, i));
                }
                if (GetUnitData(unitId).RoundScore.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).RoundScore.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).SmileAnimalCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).SmileAnimalCount.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).AngryAnimalCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).AngryAnimalCount.Remove((roundNo, i));
                }
                if (GetUnitData(unitId).HitTargetCount.ContainsKey((roundNo, i)))
                {
                    GetUnitData(unitId).HitTargetCount.Remove((roundNo, i));
                }
            }
            if (GetUnitData(unitId).HalfCount.ContainsKey(roundNo))
            {
                GetUnitData(unitId).HalfCount[roundNo] = roundNo > 1 ? GetUnitData(unitId).HalfCount.Last().Value : 0;
            }
        }

        public void Store(Segment hit, string unitId, int currentUnitIndex, int roundIndex, int throwIndex)
        {
            if (!GetUnitData(unitId).RoundScore.ContainsKey((roundIndex, throwIndex)))
            {
                GetUnitData(unitId).RoundScore.Add((roundIndex, throwIndex), 0);
            }
            if (!_hitArea.ContainsKey((currentUnitIndex, roundIndex, throwIndex)))
            {
                var copyDictionary = _hitArea.Last(item => item.Key.Item1 == currentUnitIndex).Value.Select(window => window.Clone()).ToList();
                _hitArea.Add((currentUnitIndex, roundIndex, throwIndex), copyDictionary);
            }

            _hitObjects = new object[10];
            // 是否初次打开窗户
            _hitObjects[4] = false;
            // 是否bull添加生气动物
            _hitObjects[5] = false;
            // 是否half
            _hitObjects[6] = false;
            // 是否打到窗户却没有动物
            _hitObjects[7] = false;
            // 是否有找到3只不同的微笑动物奖励
            _hitObjects[8] = false;
            var isHitWindows = false;
            for (int i = 0; i < _hitArea[(currentUnitIndex, roundIndex, throwIndex)].Count; i++)
            {
                if (_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].PositionCode == hit.PositionCode)
                {
                    // 窗户id，从0开始
                    _hitObjects[1] = i;
                    // 窗户信息
                    _hitObjects[2] = _hitArea[(currentUnitIndex, roundIndex, throwIndex)][i];
                    // 获得分数
                    _hitObjects[3] = 0;

                    var score = 0;
                    isHitWindows = true;
                    GetUnitData(unitId).HitTargetCount[(roundIndex, throwIndex)]++;
                    _missCount.Add(0);
                    switch (_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].AnimalType)
                    {
                        case AnimalType.None:
                            {
                                _foundCount.Add(0);
                                _noFoundCount.Add(1);
                                if (!_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].IsOpened)
                                {
                                    _hitObjects[4] = true;
                                }
                            }
                            break;
                        case AnimalType.Smile:
                            {
                                _foundCount.Add(1);
                                _noFoundCount.Add(0);
                                if (!_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].IsOpened)
                                {
                                    _hitObjects[4] = true;
                                    GetUnitData(unitId).SmileAnimalCount[(roundIndex, throwIndex)]++;
                                    score = _animalScoreList[_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].AnimalId - 1];
                                }
                                else
                                {
                                    switch (hit.Multiplier)
                                    {
                                        case 1:
                                            score = _singleHitBonus;
                                            break;
                                        case 2:
                                            score = _doubleHitBonus;
                                            break;
                                        case 3:
                                            score = _tripleHitBonus;
                                            break;
                                    }
                                }
                            }
                            break;
                        case AnimalType.Angry:
                            {
                                _foundCount.Add(0);
                                _noFoundCount.Add(1);
                                if (!_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].IsOpened)
                                {
                                    _hitObjects[4] = true;
                                    GetUnitData(unitId).AngryAnimalCount[(roundIndex, throwIndex)]++;
                                    score = -_animalScoreList[_hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].AnimalId - 1];
                                }
                                else
                                {
                                    score = -100;
                                }
                            }
                            break;
                    }
                    if (_foundCount.Sum() == 3)
                    {
                        _hitObjects[8] = true;
                        GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] += 200;
                    }
                    _hitObjects[3] = score;
                    GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] += score;
                    _hitArea[(currentUnitIndex, roundIndex, throwIndex)][i].IsOpened = true;
                    break;
                }
            }
            // 是否打中窗户
            _hitObjects[0] = isHitWindows;
            if (!isHitWindows)
            {
                _foundCount.Add(0);
                if (hit.IsBull)
                {
                    if (_angryAnimalRecord[(currentUnitIndex, roundIndex)] < _maxPenaltyCount)
                    {
                        _noFoundCount.Add(0);
                        _missCount.Add(0);
                        _angryAnimalRecord[(currentUnitIndex, roundIndex)]++;
                        _hitObjects[5] = true;
                    }
                    else
                    {
                        _noFoundCount.Add(1);
                        _missCount.Add(1);
                        if (_missCount.Sum() == 3)
                        {
                            GetUnitData(unitId).HalfCount[roundIndex]++;
                            GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] += -CurrentScore(unitId) / 2;
                            _hitObjects[6] = true;
                            return;
                        }
                        if (_noFoundCount.Sum() == 3 && throwIndex == Match.Rule.MaxRound)
                        {
                            _hitObjects[7] = true;
                            GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] -= 100;
                        }
                    }
                }
                else
                {
                    _noFoundCount.Add(1);
                    _missCount.Add(1);
                    if (_missCount.Sum() == 3)
                    {
                        GetUnitData(unitId).HalfCount[roundIndex]++;
                        GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] += -CurrentScore(unitId) / 2;
                        _hitObjects[6] = true;
                        return;
                    }
                    if (_noFoundCount.Sum() == 3 && throwIndex == Match.Rule.MaxRound)
                    {
                        _hitObjects[7] = true;
                        GetUnitData(unitId).RoundScore[(roundIndex, throwIndex)] -= 100;
                    }
                }
            }
            // else
            // {
            //     if (_noFoundCount.Sum() == 3 && throwIndex == Match.Rule.MaxRound)
            //     {
            //         _hitObjects[7] = true;
            //         GetUnitData(unitId).RoundScore[(roundIndex,throwIndex)] -= 100;
            //     }
            // }

            if (throwIndex < 3)
            {
                this.RefreshPlayerData(unitId, currentUnitIndex, roundIndex, throwIndex + 1);
            }
        }

        public void ResetWindows(int currentUnitIndex, int roundIndex)
        {
            _missCount.Clear();
            _noFoundCount.Clear();
            _foundCount.Clear();
            var units = Match.ParticipantTeams.AllUnits;
            var angryAnimalNum = 1;
            if (currentUnitIndex == 0 && roundIndex == 1)
            {

            }
            else
            {
                var previousId = currentUnitIndex - 1 < 0 ? units.Length - 1 : currentUnitIndex - 1;
                var targetRound = previousId > currentUnitIndex ? roundIndex - 1 : roundIndex;
                angryAnimalNum = _angryAnimalRecord[(previousId, targetRound)];
            }
            var windowList = Match.ResetWindows(angryAnimalNum);
            if (!_hitArea.ContainsKey((currentUnitIndex, roundIndex, 0)))
            {
                _hitArea.Add((currentUnitIndex, roundIndex, 0), windowList);
            }
            else
            {
                _hitArea[(currentUnitIndex, roundIndex, 0)] = windowList;
            }
        }
    }

    public class WindowEntity
    {
        public int PositionCode = 0;
        public bool IsOpened = false;
        public AnimalType AnimalType;
        //动物数组从1开始
        public int AnimalId = 0;

        public WindowEntity()
        {

        }

        public WindowEntity(int positionCode)
        {
            PositionCode = positionCode;
        }

        // 深拷贝方法
        public WindowEntity Clone()
        {
            return new WindowEntity
            {
                PositionCode = this.PositionCode,
                IsOpened = this.IsOpened,
                AnimalType = this.AnimalType,
                AnimalId = this.AnimalId
            };
        }

        public void SetAnimal(AnimalType animalType, int animalId)
        {
            AnimalType = animalType;
            AnimalId = animalId;
        }
        public void ClearAnimal()
        {
            AnimalType = AnimalType.None;
            AnimalId = 0;
        }
    }

    public enum AnimalType
    {
        None,
        Smile,
        Angry
    }
}