namespace com.luxza.grandartslogic.domain.game.zeroone
{
    public enum InCondition
    {
        /// <summary>
        /// 何も設定されていません。
        /// </summary>
        OpenIn,

        /// <summary>
        /// ダブルイン
        /// 基本的には01のみで使用する設定。
        /// ゲーム開始後、ダブル,<PERSON>-<PERSON>,<PERSON>-<PERSON>に当てるまでは、Miss扱いとなります。
        /// セパレートブルを設定中はS-BullもMiss扱いになります。
        /// </summary>
        DoubleIn,

        /// <summary>
        /// マスターイン
        /// 基本的には01のみで使用する設定。
        /// ゲーム開始後、ダブル,トリプル,D-Bull,S-<PERSON>当てるまでは、Miss扱いとなります。
        /// こちらはセパレートブルの設定は反映されません。
        /// </summary>
        MasterIn,
    }
}