namespace com.luxza.grandartslogic.domain.game.rotation
{
    public class RotationOption
    {
        public readonly RotationDirectionOption DirectionOption = RotationDirectionOption.NumberAsc;
        public readonly RotationTargetOption TargetOption = RotationTargetOption.AllTarget;
        public bool NoJump = false;
        public bool Continues = false;

        public RotationOption(RotationDirectionOption directionOption, RotationTargetOption targetOption, bool noJump = false, bool continues = false)
        {
            DirectionOption = directionOption;
            TargetOption = targetOption;
            NoJump = noJump;
            Continues = continues;
        }

        private RotationOption()
        {

        }

        public RotationOption CloneWithDirectionOption(RotationDirectionOption option)
        {
            return new RotationOption(option, TargetOption, NoJump, Continues);
        }

        public RotationOption CloneWithTargetOption(RotationTargetOption option)
        {
            return new RotationOption(DirectionOption, option, NoJump, Continues);
        }
        
        public RotationOption CloneWithNoJump(bool noJump)
        {
            return new RotationOption(DirectionOption, TargetOption, noJump, Continues);
        }
        
        public RotationOption CloneWithContinues(bool continues)
        {
            return new RotationOption(DirectionOption, TargetOption, NoJump, continues);
        }

        public static RotationOption Default => new RotationOption();
    }
}