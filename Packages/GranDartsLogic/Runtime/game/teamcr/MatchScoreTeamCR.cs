using System.Linq;

namespace com.luxza.grandartslogic.domain.game.teamcr
{
    /// <summary>
    /// 玩家打镖Mark信息
    /// </summary>
    internal class MatchScoreTeamCR : MatchScore
    {
        public CricketMarkAtThrow[] Marks { get; private set; }

        public bool IsHit { get; set; } = false;

        public MatchScoreTeamCR(int roundIndex, int[] scores) : base(roundIndex, scores)
        {
            Marks = new CricketMarkAtThrow[scores.Length];
        }

        public int TotalMarkCountByPosition(CricketMarkPosition position)
        {
            return Marks.Where(m => m.Position == position).Sum(m => m.MarkCount);
        }

        public int TotalMarkCountInRound()
        {
            return Marks.Sum(m => m.MarkCount);
        }

        public void ResetMarks()
        {
            Marks = new CricketMarkAtThrow[Scores.Length];
        }

        public void ResetTargetThrow(int throwIndex)
        {
            Marks[throwIndex - 1] = new CricketMarkAtThrow();
        }

        public void ResetTargetScore(int throwIndex)
        {
            Scores[throwIndex - 1] = 0;
        }

        public struct CricketMarkAtThrow
        {
            public CricketMarkPosition Position
            {
                get;
                private set;
            }

            public int MarkCount
            {
                get;
                private set;
            }

            public CricketMarkAtThrow(CricketMarkPosition position, int markCount)
            {
                Position = position;

                MarkCount = markCount;
            }
        }
    }
}