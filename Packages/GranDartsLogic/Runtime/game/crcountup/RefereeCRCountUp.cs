using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.extentions;

namespace com.luxza.grandartslogic.domain.game.crcountup
{
    public class RefereeCRCountUp : BaseReferee<MatchCRCountUp, MatchScorerCRCountUp, GameEventPublisher>
    {
        public RefereeCRCountUp
        (
            MatchCRCountUp match
        ) : base(
            match,
            new MatchScorerCRCountUp(match))
        {
        }

        public override bool IsReachGameEnd => Match.IsReachAllTeamsToMaxRound;
        public override bool IsNextRoundOverMaxRound => Match.IsNextRoundToOverMaxRound;


        public override int TotalScoreAtCurrentThrowingTeam => throw new NotImplementedException();

        public override int CurrentScore(string unitId)
        {
            return Scorer.CurrentScore(unitId);
        }

        public override void AcceptHit(Segment segment, Vector2? hitPosition = null)
        {
            //現在のラウンドのターゲットか確認
            //もしtargetListに同じものが含まれてなければ0点となる
            Segment virtualSegment = !Match.TargetSegmentsInRound(CurrentRoundAtCurrentTeam.No - 1).Any(s => s.Equals(segment)) ? Segment.Miss : segment;

            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualSegment, hitPosition);
            var sender = CurrentUnit;

            EventPublisher.PublishUpdateProgress(sender);
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex))
            {
                var award = AchievedAward(CurrentRoundAtCurrentTeam);
                if (award != null && award.Length > 0)
                {
                    EventPublisher.PublishAchieveAward(award, sender);
                }
                EventPublisher.PublishEndTurn(sender);
            }

            if (IsReachGameEnd)
            {
                GameEnd();
            }
        }

        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            var sender = CurrentUnit;
            if (IsReachGameEnd || IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                RefreshGameData();
                _currentThrowingUnitIndex = (_currentThrowingUnitIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : _currentThrowingUnitIndex + 1;
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }

        protected override void GameEnd()
        {
            Scorer.Confirm100PercentStats();
            Scorer.ConfirmAllRoundTargetMarkCount();
            Scorer.ConfirmTripleHitRate();
            Scorer.Confirm9MarkHitRate();
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt
        (
            string unitId
        )
        {
            return AllRoundsAt(unitId).Select(r => (r, TotalScoreOfRound(r)));
        }


        private int TotalScoreOfRound(Round round)
        {
            if (round.TryGetRoundComponent<SegmentInput>(out var segment))
            {
                int result = 0;
                foreach (var th in segment.Throws)
                {
                    result += CalculateScore(th);
                }
                return result;
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visit))
            {
                return visit.Score;
            }
            else
            {
                throw new InvalidRoundInput("This Round has no SegmentInput or VisitInput.");
            }
        }

        public override void RefreshGameData()
        {

        }

        public override Award[] AchievableAward => GameRuleCRCountUp.AchievableAward;

        protected override void ResetRoundData()
        {
            Scorer.ResetScore(CurrentThrowingUnitId, CurrentUnitProgress.CurrentRoundNo - 1);
        }

    }
}