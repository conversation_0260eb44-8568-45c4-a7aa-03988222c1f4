/// <summary>
/// EN: Match Scorer Oniren
/// </summary>

using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandartslogic.domain.game.Oniren
{
    public class MatchScorerOniren : IMatchScorer
    {
        private List<UnitGameData> _unitGameDataList = new List<UnitGameData>();
        public MatchOniren Match { get; }
        private int UnitLifeDefault => 8; //Unitごとに初期に振られるライフ値

        private bool isDirChange = false;
        private int nextTeamIndex;

        public enum HitStatus
        {
            Clear,
            Miss,
            Empty
        }

        internal MatchScorerOniren
        (
            MatchOniren match
        )
        {
            Match = match;
            var units = Match.ParticipantTeams.AllUnits;

            for (int i = 0; i < units.Length; i++)
            {
                _unitGameDataList.Add(new UnitGameData(units[i].Id, UnitLifeDefault, Match.Rule.TargetQuantity));
            }

            if (Match.Rule.IsDebugMode)
            {
                Match.CreateIsDebugModeSegments();
            }
            else
            {
                Match.CreateCurrentTargetSegment();
            }
        }

        private bool IsHit = false;

        public void SetCurrentThrowResult
        (
            bool isHit
        ) => IsHit = isHit;

        public bool IsCurrentThrowSuccess => IsHit;

        private void ResetUnitData
        (
            Unit[] units
        )
        {
            _unitGameDataList = new List<UnitGameData>();

            for (int i = 0; i < units.Length; i++)
            {
                _unitGameDataList.Add(new UnitGameData(units[i].Id, UnitLifeDefault, Match.Rule.TargetQuantity));
            }
        }

        private UnitGameData GetunitData
        (
            string unitId
        ) => _unitGameDataList.First(s => s.UnitId.Equals(unitId));

        public int CurrentScore
        (
            string unitId
        ) => Match.ParticipantTeams.Unit(unitId).Progress.AllThrows.Sum(t => Score(t.VirtualHitArea));

        public int TotalScoreInRound
        (
            string teamId,
            int roundIndex
        ) => Match.ParticipantTeams.Unit(teamId).Progress.Rounds[roundIndex].GetRoundComponent<SegmentInput>().Throws.Sum(t => Score(t.VirtualHitArea));

        //このラウンドは全てMissに当たってたかを返す
        public bool IsAllThrowMissed
        (
            Round round
        ) => round.GetRoundComponent<SegmentInput>().Throws.All(t => t.IsMiss);

        public List<Segment> GetOnirenTargetSegments => Match.Rule.OnirenTargetSegments;

        /// <summary>
        /// 現在のラウンドのターゲットセグメントを返す
        /// 1オリジンで引数を渡す
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public Segment TargetSegmentInRound
        (
            int round
        ) => GetOnirenTargetSegments[round - 1];


        public int Score
        (
            string unitId,
            int roundIndex,
            int throwIndex
        )
        {
            throw new System.NotImplementedException();
        }

        public int Score
        (
            Segment segment
        )
        {
            if (segment == null) return 0;

            return _scoreMap[segment];
        }

        public (Segment virtualHit, bool isHit) CheckHitSegment
        (
            Segment segment,
            int round
        )
        {
            //Hitの判定
            //Bullがターゲットの場合　インナーアウターはどちらでもOK //todo:暂时不做这个处理
            //Singleがターゲットの場合　インナーシングルにヒットしたらNG
            //それ以外ならヒットしたセグメントとターゲットのセグメントが同じならOK
            bool isHit = TargetSegmentInRound(round) == segment;

            return (isHit ? segment : Segment.Miss, isHit);
        }

        private int AllThrowsSumScore
        (
            Unit unit
        ) => unit.Progress.AllThrows.Sum(t => Score(t.VirtualHitArea));


        public bool IsAllUnitGameOver() => Match.ParticipantTeams.AllUnits.All
            (unit => GetUnitLife(unit.Id, unit.Progress.CurrentRoundNo) == 0);


        public void CreateSuccessTargetList()
        {
            foreach (var unitData in Match.ParticipantTeams.AllUnits)
            {
                foreach (var round in unitData.Progress.Rounds)
                {
                    if (round.GetRoundComponent<SegmentInput>().Throws.All(t => t.IsEmpty)) continue;
                    int throwIndex = 0;

                    //Throwsに入ってるActuaryHitAreaを使用して再割り当て
                    var virtualHits = new List<Segment>();

                    foreach (var hit in round.GetRoundComponent<SegmentInput>().Throws)
                    {
                        // Console.Write("\nhit.ActuaryHitArea = "+hit.ActuaryHitArea);
                        virtualHits.Add(CheckHitSegment(hit.ActuaryHitArea, round.No).virtualHit);
                        throwIndex++;
                    }

                    if (IsClear(round.No, virtualHits.ToArray()))
                    {
                        GetunitData(unitData.Id).SetUnitSuccessList(GetOnirenTargetSegments[round.No - 1]);
                    }
                }
            }
        }

        public void ReStartGame()
        {
            var units = Match.ParticipantTeams.AllUnits;
            ResetUnitData(units);
            Match.CreateCurrentTargetSegment();
        }

        internal void ResetScore
        (
            int roundIndex
        )
        {
            var units = Match.ParticipantTeams.AllUnits;
            //全チームの保持してたデータを全て削除

            // ResetUnitData(units); //todo:会导致RoundRevert显示异常

            //ゲーム再構築処理
            //ラウンドリバースしたラウンドまで
            //１Round1Throwずつ投げていきゲームを再現させる
            for (int i = 0; i <= roundIndex; i++)
            {
                // Console.Write("\ni = "+i);
                foreach (var unit in units)
                {
                    //このチームが保有してる最大ラウンドデータまで行ったらスキップ
                    if (unit.Progress.Rounds.Count <= i) continue;
                    Round round = unit.Progress.Round(i + 1);
                    //もしこのラウンドのThrowsが全て空ならスキップ
                    if (round.GetRoundComponent<SegmentInput>().Throws.All(t => t.IsEmpty)) continue;
                    int throwIndex = 0;

                    //Throwsに入ってるActuaryHitAreaを使用して再割り当て
                    var virtualHits = new List<Segment>();

                    foreach (var hit in round.GetRoundComponent<SegmentInput>().Throws)
                    {
                        // Console.Write("\nhit.ActuaryHitArea = "+hit.ActuaryHitArea);
                        virtualHits.Add(CheckHitSegment(hit.ActuaryHitArea, round.No).virtualHit);
                        throwIndex++;
                    }
                    //CheckRoundClear(round.No, virtualHits.ToArray(), unit);
                }
            }
        }

        public bool IsClear
        (
            int roundCount,
            Segment[] roundthrows
        )
        {
            var target = TargetSegmentInRound(roundCount);

            if (target.IsBull)
            {
                var sbullHitCount = roundthrows.Count(t => t == target && t == Segment.BullOut);
                var dbullHitCount = roundthrows.Count(t => t == target && t == Segment.BullIn);

                bool sbullHitClear = IsOniSingle ? sbullHitCount >= 3 : sbullHitCount >= 1;
                bool dbullHitClear = IsDoubleHitClear(dbullHitCount);
                return sbullHitClear || dbullHitClear;
            }
            else if (target.IsDouble)
            {
                return IsDoubleHitClear(roundthrows.Count(t => t == target && t.IsDouble));
            }
            else if (target.IsTriple)
            {
                return IsTripleHitClear(roundthrows.Count(t => t == target && t.IsTriple));
            }
            else
            {
                return IsSingleHitClear(roundthrows.Count(t => t == target && t.IsSingle));
            }
        }

        public void RevertLife
        (
            string unitId,
            int roundCount
        )
        {
            var roundClearStatus = GetUnitRoundHitStatusList(unitId);

            if (roundClearStatus[roundCount - 1] == HitStatus.Miss)
            {
                int preRoundLife = GetUnitLife(unitId, roundCount);
                int finalLife = preRoundLife + 1;
                SetUnitLife(unitId, roundCount, finalLife);
                ForceSetRoundHitStatusToEmpty(unitId, roundCount);
            }
            else
            {
                int preRoundLife = GetUnitLife(unitId, roundCount);
                SetUnitLife(unitId, roundCount, preRoundLife);
                ForceSetRoundHitStatusToEmpty(unitId, roundCount);
            }
        }

        public void CheckRoundClear
        (
            int roundCount,
            Segment[] segments,
            Unit unit
        )
        {
            //現在のターゲットにヒットした回数をみてクリアか、MISSかを判断する
            //ミスなら一つライフを失う
            if (!IsClear(roundCount, segments))
            {
                var life = GetUnitLife(unit.Id, roundCount) - 1;
                SetUnitLife(unit.Id, roundCount, life);
                SetUnitRoundHitStatusList(unit.Id, roundCount, HitStatus.Miss);
                var missTarget = TargetSegmentInRound(roundCount);
                SetUnitMissList(unit.Id, missTarget);
            }
            else
            {
                SetUnitRoundHitStatusList(unit.Id, roundCount, HitStatus.Clear);
            }
        }

        public void isDirChangeStatus
        (
            bool v,
            int roundCount = 0,
            Segment[] segments = null,
            Unit unit = null
        )
        {
            isDirChange = v;

            if (v)
            {
                CheckRoundClear(roundCount, segments, unit);
            }
        }

        public int getNextTeamIndex => nextTeamIndex;

        public void GetNextTeamIndex
        (
            int v
        )
        {
            nextTeamIndex = v;
        }

        public void UpdateUnitLife
        (
            int roundIndex,
            string unitId
        )
        {
            if (roundIndex > 1)
            {
                var unitLife = GetUnitLife(unitId, roundIndex - 1);

                if (unitLife == 0)
                {
                    SetUnitLifeToZero(unitId, roundIndex);
                }
                else
                {
                    SetUnitLife(unitId, roundIndex, unitLife == 0 ? 0 : unitLife);
                }
            }
        }

        private bool IsOniSingle => Match.Rule.Mode == GameRuleOniren.OnirenMode.OniSingle;
        private bool IsOniDouble => Match.Rule.Mode == GameRuleOniren.OnirenMode.OniDouble;
        private bool IsOniTriple => Match.Rule.Mode == GameRuleOniren.OnirenMode.OniTriple;

        private bool IsSingleHitClear
        (
            int hitCount
        ) => IsOniSingle ? hitCount >= 3 : hitCount >= 2;

        private bool IsDoubleHitClear
        (
            int hitCount
        ) => IsOniDouble ? hitCount >= 2 : hitCount >= 1;

        private bool IsTripleHitClear
        (
            int hitCount
        ) => IsOniTriple ? hitCount >= 2 : hitCount >= 1;

        public int GetUnitLife
        (
            string unitId,
            int roundIndex
        ) => GetunitData(unitId).GetUnitLife(roundIndex);

        public void SetUnitLife
        (
            string unitId,
            int roundIndex,
            int life
        ) => GetunitData(unitId).SetUnitLife(roundIndex, life);

        public void SetUnitLifeToZero
        (
            string unitId,
            int roundIndex
        ) => GetunitData(unitId).SetUnitLifeToZero(roundIndex);

        public List<Segment> GetUnitSuccessList
        (
            string unitId
        ) => GetunitData(unitId).GetSuccessList();

        public List<Segment> GetUnitMissList
        (
            string unitId
        ) => GetunitData(unitId).GetMissList();

        public void SetUnitMissList
        (
            string unitId,
            Segment missTarget
        ) => GetunitData(unitId).SetUnitMissList(missTarget);

        public void ClearUnitMissTarget
        (
            string unitId
        ) => GetunitData(unitId).ClearMissTargetAtRevert();

        public void SetUnitRoundHitStatusList
        (
            string unitId,
            int roundIndex,
            HitStatus hitStatus
        ) => GetunitData(unitId).SetUnitRoundHitStatusList(roundIndex, hitStatus);

        public void ForceSetRoundHitStatusToEmpty
        (
            string unitId,
            int roundIndex
        ) => GetunitData(unitId).ForceSetToEmpty(roundIndex);

        public List<HitStatus> GetUnitRoundHitStatusList
        (
            string unitId
        ) => GetunitData(unitId).GetRoundHitStatusList();

        public int GetUnitKeepRoundCount
        (
            string unitId
        ) => GetunitData(unitId).GetRoundHitStatusList().Count(state => state == HitStatus.Clear);

        public void AddNewRoundsData
        (
            string unitId,
            int roundIndex,
            int targetQuantity
        ) => GetunitData(unitId).AddNewRoundsData(roundIndex, targetQuantity);

        private class UnitGameData
        {
            internal readonly string UnitId;
            internal List<int> UnitLife;
            internal List<Segment> SuccessTargetList;
            internal List<Segment> MissTargetList;
            internal List<HitStatus> RoundHitStatus;

            internal UnitGameData
            (
                string unitId,
                int life,
                int targetQuantity
            )
            {
                UnitId = unitId;
                UnitLife = Enumerable.Repeat(life, targetQuantity).ToList();
                SuccessTargetList = new List<Segment>();
                MissTargetList = new List<Segment>();
                RoundHitStatus = new List<HitStatus>(Enumerable.Repeat(HitStatus.Empty, targetQuantity));
            }

            internal void AddNewRoundsData
            (
                int roundIndex,
                int targetQuantity
            )
            {
                if (roundIndex == UnitLife.Count)
                {
                    var newUnitLife = Enumerable.Repeat(UnitLife.Last(), targetQuantity).ToList();
                    UnitLife.AddRange(newUnitLife);
                    var newRoundHitStatus = new List<HitStatus>(Enumerable.Repeat(HitStatus.Empty, targetQuantity));
                    RoundHitStatus.AddRange(newRoundHitStatus);
                }
            }

            //unitの残りライフを更新
            internal void SetUnitLife
            (
                int roundIndex,
                int life
            )
            {
                UnitLife[roundIndex - 1] = life;
            }

            internal void SetUnitLifeToZero
            (
                int roundIndex
            )
            {
                for (int i = roundIndex - 1; i < UnitLife.Count; i++)
                {
                    UnitLife[i] = 0;
                }
            }

            //unitの残りライフを取得
            internal int GetUnitLife
            (
                int roundIndex
            )
            {
                return UnitLife[roundIndex - 1];
            }

            //unitの成功リストを更新
            //ゲーム終了時の集計処理時に使用する
            internal void SetUnitSuccessList
            (
                Segment segment
            )
            {
                SuccessTargetList.Add(segment);
            }

            internal void SetUnitMissList
            (
                Segment segment
            )
            {
                MissTargetList.Add(segment);
            }

            internal void ClearMissTargetAtRevert()
            {
                if (MissTargetList.Count > 0)
                {
                    MissTargetList.Remove(MissTargetList[MissTargetList.Count - 1]);
                }
            }

            internal void SetUnitRoundHitStatusList
            (
                int roundIndex,
                HitStatus hitStatus
            )
            {
                if (RoundHitStatus[roundIndex - 1] == HitStatus.Clear ||
                    RoundHitStatus[roundIndex - 1] == HitStatus.Miss)
                {
                    roundIndex++;
                }
                RoundHitStatus[roundIndex - 1] = hitStatus;
            }

            internal void ForceSetToEmpty
            (
                int roundIndex
            )
            {
                RoundHitStatus[roundIndex - 1] = HitStatus.Empty;
            }

            //unitの成功リスト取得
            internal List<Segment> GetSuccessList() => SuccessTargetList;
            internal List<Segment> GetMissList() => MissTargetList;
            internal List<HitStatus> GetRoundHitStatusList() => RoundHitStatus;
        }

        public static readonly Dictionary<Segment, int> _scoreMap = new Dictionary<Segment, int>() {
            {
                Segment.Single1In, 1
            }, {
                Segment.Single1Out, 1
            }, {
                Segment.Double1, 1 * 2
            }, {
                Segment.Triple1, 1 * 3
            }, {
                Segment.Single2In, 2
            }, {
                Segment.Single2Out, 2
            }, {
                Segment.Double2, 2 * 2
            }, {
                Segment.Triple2, 2 * 3
            }, {
                Segment.Single3In, 3
            }, {
                Segment.Single3Out, 3
            }, {
                Segment.Double3, 3 * 2
            }, {
                Segment.Triple3, 3 * 3
            }, {
                Segment.Single4In, 4
            }, {
                Segment.Single4Out, 4
            }, {
                Segment.Double4, 4 * 2
            }, {
                Segment.Triple4, 4 * 3
            }, {
                Segment.Single5In, 5
            }, {
                Segment.Single5Out, 5
            }, {
                Segment.Double5, 5 * 2
            }, {
                Segment.Triple5, 5 * 3
            }, {
                Segment.Single6In, 6
            }, {
                Segment.Single6Out, 6
            }, {
                Segment.Double6, 6 * 2
            }, {
                Segment.Triple6, 6 * 3
            }, {
                Segment.Single7In, 7
            }, {
                Segment.Single7Out, 7
            }, {
                Segment.Double7, 7 * 2
            }, {
                Segment.Triple7, 7 * 3
            }, {
                Segment.Single8In, 8
            }, {
                Segment.Single8Out, 8
            }, {
                Segment.Double8, 8 * 2
            }, {
                Segment.Triple8, 8 * 3
            }, {
                Segment.Single9In, 9
            }, {
                Segment.Single9Out, 9
            }, {
                Segment.Double9, 9 * 2
            }, {
                Segment.Triple9, 9 * 3
            }, {
                Segment.Single10In, 10
            }, {
                Segment.Single10Out, 10
            }, {
                Segment.Double10, 10 * 2
            }, {
                Segment.Triple10, 10 * 3
            }, {
                Segment.Single11In, 11
            }, {
                Segment.Single11Out, 11
            }, {
                Segment.Double11, 11 * 2
            }, {
                Segment.Triple11, 11 * 3
            }, {
                Segment.Single12In, 12
            }, {
                Segment.Single12Out, 12
            }, {
                Segment.Double12, 12 * 2
            }, {
                Segment.Triple12, 12 * 3
            }, {
                Segment.Single13In, 13
            }, {
                Segment.Single13Out, 13
            }, {
                Segment.Double13, 13 * 2
            }, {
                Segment.Triple13, 13 * 3
            }, {
                Segment.Single14In, 14
            }, {
                Segment.Single14Out, 14
            }, {
                Segment.Double14, 14 * 2
            }, {
                Segment.Triple14, 14 * 3
            }, {
                Segment.Single15In, 15
            }, {
                Segment.Single15Out, 15
            }, {
                Segment.Double15, 15 * 2
            }, {
                Segment.Triple15, 15 * 3
            }, {
                Segment.Single16In, 16
            }, {
                Segment.Single16Out, 16
            }, {
                Segment.Double16, 16 * 2
            }, {
                Segment.Triple16, 16 * 3
            }, {
                Segment.Single17In, 17
            }, {
                Segment.Single17Out, 17
            }, {
                Segment.Double17, 17 * 2
            }, {
                Segment.Triple17, 17 * 3
            }, {
                Segment.Single18In, 18
            }, {
                Segment.Single18Out, 18
            }, {
                Segment.Double18, 18 * 2
            }, {
                Segment.Triple18, 18 * 3
            }, {
                Segment.Single19In, 19
            }, {
                Segment.Single19Out, 19
            }, {
                Segment.Double19, 19 * 2
            }, {
                Segment.Triple19, 19 * 3
            }, {
                Segment.Single20In, 20
            }, {
                Segment.Single20Out, 20
            }, {
                Segment.Double20, 20 * 2
            }, {
                Segment.Triple20, 20 * 3
            }, {
                Segment.BullIn, 50
            }, {
                Segment.BullOut, 25
            }, {
                Segment.Miss, 0
            }, {
                Segment.OUT, 0
            }
        };
    }
}