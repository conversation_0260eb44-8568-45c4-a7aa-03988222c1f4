using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.extentions;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests
{
    public partial class UnitTest
    {
        public static IEnumerable<TestCaseData> InvalidPlayersArguments
        {
            get
            {
                yield return new TestCaseData(new object[] { null });
                yield return new TestCaseData(new object[] { new Player[0] });
            }
        }

        [TestCaseSource("InvalidPlayersArguments")]
        public void RaiseTeamCreationExceptionWhenInstanceWith(Player[] players)
        {
            try
            {
                UnitFactory.Instance.TeamUp(players, 1);
                Assert.Fail();
            }
            catch (UnitCreationFailedException e)
            {
                Assert.Pass();
            }
        }

        public static IEnumerable<TestCaseData> TeamUpPlayers
        {
            get
            {
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                        },
                        1
                    ),
                    1,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                        },
                        1
                    ),
                    2,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                        },
                        1
                    ),
                    8,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                        },
                        1
                    ),
                    1,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                        },
                        2
                    ),
                    2,
                    "B"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                        },
                        2
                    ),
                    8,
                    "B"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                        },
                        1
                    ),
                    1,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                        },
                        2
                    ),
                    2,
                    "B"
                });
                yield return new TestCaseData
                (
                    new object[] {
                        UnitFactory.Instance.TeamUp
                        (
                            new Player[] {
                                new Player("A", 1),
                                new Player("B", 2),
                                new Player("C", 3),
                            },
                            3
                        ),
                        3,
                        "C"
                    }
                );
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                        },
                        2
                    ),
                    5,
                    "B"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                            new Player("D", 4),
                            new Player("E", 5),
                            new Player("F", 6),
                        },
                        1
                    ),
                    1,
                    "A"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                            new Player("D", 4),
                            new Player("E", 5),
                            new Player("F", 6),
                        },
                        3
                    ),
                    3,
                    "C"
                });
                yield return new TestCaseData(new object[]
                {
                    UnitFactory.Instance.TeamUp
                    (
                        new Player[] {
                            new Player("A", 1),
                            new Player("B", 2),
                            new Player("C", 3),
                            new Player("D", 4),
                            new Player("E", 5),
                            new Player("F", 6),
                        },
                        2
                    ),
                    8,
                    "B"
                });
            }
        }

        [TestCaseSource("TeamUpPlayers")]
        public void GetThrowerPerRound(Unit unit, int round, string expectedGranId)
        {
            var player = unit.Thrower(round);
            Assert.AreEqual(player.GranId, expectedGranId);
        }
    }
}