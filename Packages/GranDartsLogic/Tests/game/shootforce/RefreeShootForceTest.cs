using System.Collections.Generic;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.shootforce;


namespace com.luxza.grandartslogic.tests.game.shootforce
{
    public partial class RefreeShootForceTest
    {
        private string[] testplayernameArray = { "A", "B", "C", "D", "E", "F", "G", "H" };


        private RefereeShootForce MakeShootForceRef(
            List<Unit> TeamsList
        )
        {
            return MatchMaker.MakeShootForce(TeamsList.ToArray(), new GameRuleShootForce());
        }

        private RefereeShootForce StartMatchProceed(
            Segment[] hits,
            int playerCount
        )
        {
            List<Unit> TeamsList = new List<Unit>();
            for (int i = 0; i < playerCount; i++)
            {
                TeamsList.Add
                (
                    UnitFactory.Instance.TeamUp
                    (
                        testplayernameArray[i],
                        new Player[] {
                            new Player("Test" + testplayernameArray[i], i + 1),
                        },
                        i
                    )
                );
            }


            var referee = MakeShootForceRef(TeamsList);
            return StartMatchAndToProceed(hits, referee);
        }


        private RefereeShootForce StartMatchAndToProceed(
            Segment[] hits,
            RefereeShootForce referee
        )
        {
            referee.StartMatch();

            foreach (var segment in hits)
            {
                referee.AcceptHit(segment);
                if (referee.IsThrowCountOverMaxThrowInRound)
                {
                    referee.ChangeToNextTeam();
                }
            }

            return referee;
        }
    }
}