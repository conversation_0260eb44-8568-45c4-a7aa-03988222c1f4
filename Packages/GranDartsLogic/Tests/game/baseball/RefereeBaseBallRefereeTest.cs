using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.baseball;
using com.luxza.grandartslogic.interfaces;

namespace com.luxza.grandartslogic.tests.game.baseball
{
    public partial class RefereeBaseBallTest
    {
        private string[] testplayernameArray = { "A", "B", "C", "D" };

        public enum TestCaseType
        {
            testCaseA,
            testCaseB,
            testCaseC
        }

        private enum InputType
        {
            SegmentHit,
            ChangeButton,
            RoundReverse
        }

        private (InputType, Segment)[] testCaseASegments = new (InputType, Segment)[] {

                        (InputType.SegmentHit,Segment.Single1In),//1
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single2In),//2
                        (InputType.SegmentHit,Segment.Double2),
                        (InputType.SegmentHit,Segment.Single2In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single3In),//3
                        (InputType.SegmentHit,Segment.Triple3),
                        (InputType.SegmentHit,Segment.Single3In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple4),//4
                        (InputType.SegmentHit,Segment.Double4),
                        (InputType.SegmentHit,Segment.Single4In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single5In),//5
                        (InputType.SegmentHit,Segment.Single4In),
                        (InputType.SegmentHit,Segment.Single6In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple14),//6
                        (InputType.SegmentHit,Segment.Triple14),
                        (InputType.SegmentHit,Segment.Triple14),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Double16),//7
                        (InputType.SegmentHit,Segment.Double16),
                        (InputType.SegmentHit,Segment.Double16),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single15In),//8
                        (InputType.SegmentHit,Segment.Single10In),
                        (InputType.SegmentHit,Segment.Triple2),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple16),//9
                        (InputType.SegmentHit,Segment.Triple16),
                        (InputType.SegmentHit,Segment.Single16In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single17Out),//10
                        (InputType.SegmentHit,Segment.Triple3),
                        (InputType.SegmentHit,Segment.Triple17),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple19),//11
                        (InputType.SegmentHit,Segment.Triple8),
                        (InputType.SegmentHit,Segment.Single16In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single18In),//12
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple19),//13
                        (InputType.SegmentHit,Segment.Triple19),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single1In),//14
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.SegmentHit,Segment.Single20In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.BullOut),//15
                        (InputType.SegmentHit,Segment.BullOut),
                        (InputType.SegmentHit,Segment.BullIn),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.BullOut),//16
                        (InputType.SegmentHit,Segment.BullOut),
                        (InputType.SegmentHit,Segment.BullIn),
        };

        private (InputType, Segment)[] testCaseBSegments = new (InputType, Segment)[] {

                        (InputType.SegmentHit,Segment.Single6In),//1
                        (InputType.SegmentHit,Segment.Single15In),
                        (InputType.SegmentHit,Segment.Single6In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single11In),//2
                        (InputType.SegmentHit,Segment.Triple8),
                        (InputType.SegmentHit,Segment.Triple14),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.OUT),//3
                        (InputType.SegmentHit,Segment.OUT),
                        (InputType.SegmentHit,Segment.Double12),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple20),//4
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single6In),//5
                        (InputType.SegmentHit,Segment.Single13In),
                        (InputType.SegmentHit,Segment.Single6In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single14In),//6
                        (InputType.SegmentHit,Segment.Single11In),
                        (InputType.SegmentHit,Segment.Single11In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Double6),//7
                        (InputType.SegmentHit,Segment.Double6),
                        (InputType.SegmentHit,Segment.Single6Out),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single10In),//8
                        (InputType.SegmentHit,Segment.Single15In),
                        (InputType.SegmentHit,Segment.Single15In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple16),//9
                        (InputType.SegmentHit,Segment.Triple16),
                        (InputType.SegmentHit,Segment.Triple16),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single3In),//10
                        (InputType.SegmentHit,Segment.Single3In),
                        (InputType.SegmentHit,Segment.Single2In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple20),//11
                        (InputType.SegmentHit,Segment.OUT),
                        (InputType.SegmentHit,Segment.Single5In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single4In),//12
                        (InputType.SegmentHit,Segment.Single18In),
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple7),//13
                        (InputType.SegmentHit,Segment.Single7In),
                        (InputType.SegmentHit,Segment.Single7In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple20),//14
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.BullOut),//15
                        (InputType.SegmentHit,Segment.Single13In),
                        (InputType.SegmentHit,Segment.Single10In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.BullIn),//16
                        (InputType.SegmentHit,Segment.BullIn),
                        (InputType.SegmentHit,Segment.BullIn),
        };

        private (InputType, Segment)[] testCaseCSegments = new (InputType, Segment)[] {

                        (InputType.SegmentHit,Segment.Single2In),//1
                        (InputType.SegmentHit,Segment.Single15In),
                        (InputType.SegmentHit,Segment.Single10In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single8In),//2
                        (InputType.SegmentHit,Segment.Triple8),
                        (InputType.SegmentHit,Segment.Single11In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single12In),//3
                        (InputType.SegmentHit,Segment.Single12In),
                        (InputType.SegmentHit,Segment.Single12In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Double17),//4
                        (InputType.SegmentHit,Segment.Double17),
                        (InputType.SegmentHit,Segment.Double17),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single6In),//5
                        (InputType.SegmentHit,Segment.Single4In),
                        (InputType.SegmentHit,Segment.Single6In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Double14),//6
                        (InputType.SegmentHit,Segment.Triple11),
                        (InputType.SegmentHit,Segment.Triple9),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Double20),//7
                        (InputType.SegmentHit,Segment.Double20),
                        (InputType.SegmentHit,Segment.Double20),
                        (InputType.RoundReverse,null),

                        (InputType.SegmentHit,Segment.Double20),//7
                        (InputType.SegmentHit,Segment.Double1),
                        (InputType.SegmentHit,Segment.Triple5),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple10),//8
                        (InputType.SegmentHit,Segment.Single10In),
                        (InputType.SegmentHit,Segment.Single15In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple8),//9
                        (InputType.SegmentHit,Segment.Triple8),
                        (InputType.SegmentHit,Segment.Single16In),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple17),//10
                        (InputType.SegmentHit,Segment.Triple3),
                        (InputType.SegmentHit,Segment.Triple17),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple15),//11
                        (InputType.SegmentHit,Segment.Triple2),
                        (InputType.SegmentHit,Segment.Triple15),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple18),//12
                        (InputType.SegmentHit,Segment.Triple18),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Triple19),//13
                        (InputType.SegmentHit,Segment.Triple19),
                        (InputType.SegmentHit,Segment.OUT),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single20In),//14
                        (InputType.SegmentHit,Segment.Single1In),
                        (InputType.SegmentHit,Segment.Single20In),
                        (InputType.RoundReverse,null),

                        (InputType.SegmentHit,Segment.Triple20),//14
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.SegmentHit,Segment.Triple20),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.BullOut),//15
                        (InputType.SegmentHit,Segment.Single20In),
                        (InputType.SegmentHit,Segment.BullIn),
                        (InputType.ChangeButton,null),

                        (InputType.SegmentHit,Segment.Single20In),//16
                        (InputType.SegmentHit,Segment.Single5In),
                        (InputType.SegmentHit,Segment.Single1In),
        };

        private RefereeBaseBall MakeBaseBallRef(List<Unit> TeamsList,
            GameRuleBaseBall.BaseBallDirectionOption modeOption = GameRuleBaseBall.BaseBallDirectionOption.Classic,
            GameRuleBaseBall.BaseBallTargetOption targetOption = GameRuleBaseBall.BaseBallTargetOption.OneToNine)
        {
            return MatchMaker.MakeBaseBall(TeamsList.ToArray(),
            new GameRuleBaseBall
            (
                modeOption: modeOption,
                targetOption: targetOption
            )
        );
        }

        private List<Unit> CreateUnits(int unitCount, int memberCount)
        {
            List<Unit> unitList = new List<Unit>();
            for (int i = 0; i < unitCount; i++)
            {
                List<Player> memberList = new List<Player>();

                switch (memberCount)
                {
                    default:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        break;
                    case 2:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        break;
                    case 3:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 3, 3));
                        break;
                    case 4:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 3, 3));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 4, 4));
                        break;
                }

                unitList.Add(UnitFactory.Instance.TeamUp(testplayernameArray[i], players: memberList.ToArray(), i));
            }
            return unitList;
        }

        private RefereeBaseBall StartMatchProceed(Segment[] hits,
            int playerCount,
            GameRuleBaseBall.BaseBallDirectionOption modeOption,
            GameRuleBaseBall.BaseBallTargetOption targetOption)
        {
            var units = CreateUnits(playerCount, 1);
            var referee = MakeBaseBallRef(units, modeOption: modeOption, targetOption: targetOption);
            return StartMatchAndToProceed(hits, referee);
        }


        private RefereeBaseBall StartMatchAndToProceed(Segment[] hits,
            RefereeBaseBall referee)
        {
            referee.StartMatch();

            bool needToNextRound = false;

            foreach (var segment in hits)
            {
                if (needToNextRound)
                {
                    referee.ChangeToNextTeam();

                    needToNextRound = false;
                }

                referee.AcceptHit(segment);
                if (referee.IsThrowCountOverMaxThrowInRound)
                {
                    needToNextRound = true;
                }
            }

            return referee;
        }

        private RefereeBaseBall SetTestCaseTypeStartMatchProceed(
            TestCaseType testCaseType,
            int unitCount,
            int memberCount,
            GameRuleBaseBall.BaseBallDirectionOption modeOption,
            GameRuleBaseBall.BaseBallTargetOption targetOption)
        {
            var referee = MakeBaseBallRef(TeamsList: CreateUnits(unitCount, memberCount), modeOption: modeOption, targetOption: targetOption);
            (InputType, Segment)[] hits = new (InputType, Segment)[] { };
            switch (testCaseType)
            {
                default:
                    hits = testCaseASegments;
                    break;
                case TestCaseType.testCaseB:
                    hits = testCaseBSegments;
                    break;
                case TestCaseType.testCaseC:
                    hits = testCaseCSegments;
                    break;
            }

            return InputTypeSelectStartMatchAndProceed(hits, referee);
        }

        //入力したタイプによって挙動を変える
        //現在のところチェンジボタンとラウンドリバースと通常のヒットの3パターン
        //テストケースの入力値にチェンジを入れないとチェンジできないので注意
        private RefereeBaseBall InputTypeSelectStartMatchAndProceed(
            (InputType input, Segment segment)[] hits,
            RefereeBaseBall referee)
        {

            referee.StartMatch();

            foreach (var item in hits)
            {
                if (item.input == InputType.ChangeButton)
                {
                    //Debug.Log($"Change Score {referee.CurrentTeamLatestRound().No}:hitsegment Score:" + referee.CurrentScore(referee.CurrentThrowingTeamId));
                    referee.ChangeToNextTeam();
                }
                else if (item.input == InputType.RoundReverse)
                {
                    referee.RevertCurrentRound();
                }
                else
                {
                    //Debug.Log($"Change Score {referee.CurrentTeamLatestRound().No}:hitsegment{item.segment} Score:" + referee.CurrentScore(referee.CurrentThrowingTeamId));
                    referee.AcceptHit(item.segment);
                }
            }

            return referee;
        }
    }
}
