using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.target20
{
    public partial class Target20RefereeTest
    {
        public static TestCaseData[] TeamMissHitRateCase = new TestCaseData[] {
            new TestCaseData
            (
                new object[] {
                    "TestCaseA",
                    1,
                    1,
                    "TestA_1",
                    TargetClearCondition.Hit5,
                    new Dictionary<SegmentCode, float>() {
                        {
                            SegmentCode.S5_In, 0.091f
                        }, {
                            SegmentCode.S1_In, 0.091f
                        }, {
                            SegmentCode.S12_In, 0.091f
                        }, {
                            SegmentCode.T5, 0.273f
                        }
                    },
                }
            ).SetName("Test Case A（1人プレイ）: S5(in):0.091,S1(in):0.091,S12(in)0.091,T5:0.273"),

            new TestCaseData
            (
                new object[] {
                    "TestCaseB",
                    1,
                    1,
                    "TestA_1",

                    new Dictionary<SegmentCode, float>() {
                        {
                            SegmentCode.S1_In, 0.105f
                        }, {
                            SegmentCode.T5, 0.105f
                        }, {
                            SegmentCode.S5_In, 0.053f
                        }, {
                            SegmentCode.T12, 0.053f
                        }, {
                            SegmentCode.S12_In, 0.053f
                        }
                    },
                }
            ).SetName("Test Case B（1人プレイ）: S1(in):0.105,T5:0.105,S5(in):0.053,T12:0.053,S12(in)0.053"),

            new TestCaseData
            (
                new object[] {
                    "TestCaseC",
                    1,
                    1,
                    "TestA_1",
                    TargetClearCondition.Hit10,
                    new Dictionary<SegmentCode, float>() {
                        {
                            SegmentCode.S5_In, 0.063f
                        }, {
                            SegmentCode.S1_In, 0.031f
                        }, {
                            SegmentCode.T1, 0.188f
                        }, {
                            SegmentCode.OUT, 0.063f
                        }, {
                            SegmentCode.S18_In, 0.031f
                        }, {
                            SegmentCode.S18_Out, 0.031f
                        }, {
                            SegmentCode.T5, 0.063f
                        }, {
                            SegmentCode.S12_In, 0.063f
                        }, {
                            SegmentCode.T18, 0.031f
                        }, {
                            SegmentCode.S3_In, 0.031f
                        }, {
                            SegmentCode.S1_Out, 0.031f
                        }, {
                            SegmentCode.S5_Out, 0.063f
                        }
                    },
                }
            ).SetName
            (
                "Test Case C（1人プレイ）: S5(in):0.063,S1(in):0.031,T1:0.188,Out:0.063,S18(in):0.031,S18(out):0.031,T5:0.063,S12(in):0.063,T18:0.031,S3(in):0.031,S1(out):0.031,S5(out):0.063"
            ),

            new TestCaseData
            (
                new object[] {
                    "TestCaseD",
                    1,
                    2,
                    "TestA_1",
                    TargetClearCondition.Hit10,
                    new Dictionary<SegmentCode, float>() {
                        {
                            SegmentCode.S5_In, 0.100f
                        }, {
                            SegmentCode.T12, 0.100f
                        }, {
                            SegmentCode.T5, 0.100f
                        }
                    },
                }
            ).SetName("Test Case D（ペアプレイ）PlayerA>S5(in):0.100,T12:0.100,T5:0.100"),

            new TestCaseData
            (
                new object[] {
                    "TestCaseD",
                    1,
                    2,
                    "TestA_2",
                    TargetClearCondition.Hit10,
                    new Dictionary<SegmentCode, float>() {
                        {
                            SegmentCode.S1_In, 0.222f
                        }, {
                            SegmentCode.T5, 0.111f
                        }, {
                            SegmentCode.S12_In, 0.111f
                        }
                    },
                }
            ).SetName("Test Case D（ペアプレイ）PlayerB>S1(in):0.222,T5:0.111,S12(in):0.111")
        };

        [TestCaseSource(nameof(TeamMissHitRateCase))]
        public void Test6_MissHitRateCase
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount,
            Dictionary<SegmentCode, float> expectedList
        )
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCount: clearCount);
            var missMainTargetRateList = referee.Scorer.HitRateArray(granId);

            foreach (var item in expectedList)
            {
                //Debug.Log($"missMainTargetRateList[({item.Key})]({missMainTargetRateList[item.Key]}), item.Value({item.Value})");
                Assert.That(missMainTargetRateList[item.Key], Is.EqualTo(item.Value));
            }
        }

        public static TestCaseData[] Target20ConsecutiveHits = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 2").
                Returns(2),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 4").
                Returns(4),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 2").
                Returns(2),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerA:4").
                Returns(4),
            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_2",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerB:1").
                Returns(1),
        };

        [TestCaseSource(nameof(Target20ConsecutiveHits))]
        public int XTarget20AnalysisdataConsecutiveHitsCount
        (
            string testCasetype,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed(testCasetype, unitCount, memberCount, clearCount: clearCount);

            return referee.Scorer.ConsecutiveHitCount(granId);
        }

        public static TestCaseData[] Target20AnalysisdataFinishRoundArray = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "A",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 4").
                Returns(4),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "A",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 7").
                Returns(7),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "A",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 11").
                Returns(11),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "A",
                        TargetClearCondition.Round10,
                    }
                ).SetName("Test Case C（1人プレイ）10RoundMode: 10").
                Returns(10),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "A",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）:7").
                Returns(7)
        };

        [TestCaseSource(nameof(Target20AnalysisdataFinishRoundArray))]
        public int XTarget20AnalysisdataFinishRound
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string unitId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed
                (testCaseType: testCaseType, unitCount, memberCount, clearCount: clearCount);
            return referee.Scorer.FinishRoundCount
                (referee.Match.ParticipantTeams.AllUnits.First(unit => unit.Id.Equals(unitId)));
        }

        public static TestCaseData[] Target20AnalysisdataTarget20OutSingleRateArray = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 0.200").
                Returns(0.200f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 0.200").
                Returns(0.200f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 0.200").
                Returns(0.200f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerA:0").
                Returns(0f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_2",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）:PlayerB:0.667").
                Returns(0.667f)
        };

        [TestCaseSource(nameof(Target20AnalysisdataTarget20OutSingleRateArray))]
        public float XTarget20AnalysisdataTarget20OutSingleRate
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCount: clearCount);
            return referee.Scorer.Target20OuterSingleRate(granId);
        }

        public static TestCaseData[] Target20AnalysisdataTarget20InSingleRateArray = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 0.400").
                Returns(0.400f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 0.200").
                Returns(0.200f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 0.700").
                Returns(0.700f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerA:0.143").
                Returns(0.143f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_2",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）:PlayerB:0.333").
                Returns(0.333f)
        };

        [TestCaseSource(nameof(Target20AnalysisdataTarget20InSingleRateArray))]
        public float XTarget20AnalysisdataTarget20InSingleRate
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCount: clearCount);
            return referee.Scorer.Target20InnerSingleRate(granId);
        }

        public static TestCaseData[] Target20AnalysisdataT20RateArray = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 0.200").
                Returns(0.200f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 0.300").
                Returns(0.300f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 0.100").
                Returns(0.100f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerA:0.429").
                Returns(0.429f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_2",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）:PlayerB:0").
                Returns(0f)
        };

        [TestCaseSource(nameof(Target20AnalysisdataT20RateArray))]
        public float XTarget20AnalysisdataT20Rate
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCount: clearCount);
            return referee.Scorer.Target20TripleRate(granId);
        }

        public static TestCaseData[] Target20AnalysisdataTarget20RateArray = new TestCaseData[] {
            new TestCaseData
                (
                    new object[] {
                        "TestCaseA",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit5,
                    }
                ).SetName("Test Case A（1人プレイ）: 0.455").
                Returns(0.455f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseB",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case B（1人プレイ）: 0.526").
                Returns(0.526f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseC",
                        1,
                        1,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case C（1人プレイ）: 0.313").
                Returns(0.313f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_1",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）PlayerA:0.700").
                Returns(0.700f),

            new TestCaseData
                (
                    new object[] {
                        "TestCaseD",
                        1,
                        2,
                        "TestA_2",
                        TargetClearCondition.Hit10,
                    }
                ).SetName("Test Case D（ペアプレイ）:PlayerB:0.333").
                Returns(0.333f)
        };

        [TestCaseSource(nameof(Target20AnalysisdataTarget20RateArray))]
        public float XTarget20AnalysisdataTarget20Rate
        (
            string testCaseType,
            int unitCount,
            int memberCount,
            string granId,
            TargetClearCondition clearCount
        )
        {
            var referee = StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCount: clearCount);
            return referee.Scorer.Target20Rate(granId);
        }
    }
}