using System;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.spider
{
    public partial class SpiderTest
    {
        public static TestCaseData[] SpiderMaxRoundTestdataArray = new TestCaseData[]
        {

            new TestCaseData(new object[]
                {

                    new Segment[]
                    {

                    },
                    2,

                })
                .SetName(
                    "Spider game is MaxRound 15")
            .Returns(15),


        };

        [TestCaseSource(nameof(SpiderMaxRoundTestdataArray))]
        public int MaxRoundCountTest(Segment[] segments, int playerCount)
        {
            var referee = StartMatchProceed(segments, playerCount);
            return referee.Match.Rule.MaxRound;
        }
    }
}