using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game.targethorse;

namespace com.luxza.grandartslogic.tests.game.targethorse
{
    public partial class TargetHorseTest
    {
        public static TestCaseData[] TargetHorseAwardPlayTestdataArray = new TestCaseData[]
        {


           new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    Segment.Triple16,
                    Segment.Triple17,
                    Segment.Triple18,
                    },
                    2,
                    TargetHorseClearCondition.Hit30,
                    new ActionDelegateForAwardTest(new Award[] {
                        Award.WhiteHorse,
                    }),
                })

                .SetName("TeamA -> hit R1[T16, T17, T18] targetT18 play whiteHorse")
            .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 1},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
              new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    Segment.Triple18,
                    Segment.Triple17,
                    Segment.Triple17,
                    },
                    2,
                    TargetHorseClearCondition.Hit30,
                    new ActionDelegateForAwardTest(new Award[0]),
                })

                .SetName("TeamA -> hit R1[T18, T17, T17] achieve no award.")
            .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.BullIn,
                        Segment.BullIn,
                        Segment.BullIn,
                    },
                    2,
                    TargetHorseClearCondition.Hit30,
                    new ActionDelegateForAwardTest(new Award[0]),
                })

                .SetName("TeamA -> hit R1[DBull, DBull, DBull] no play  award")
            .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.BullIn,
                        Segment.BullOut,
                        Segment.BullIn,
                    },
                    2,
                    TargetHorseClearCondition.Hit30,
                    new ActionDelegateForAwardTest(new Award[] {

                    }),
                })

                .SetName("TeamA -> hit R1[DBull, SBull, DBull] no play Horse award")
            .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),

        };

        [TestCaseSource(nameof(TargetHorseAwardPlayTestdataArray))]
        public Dictionary<Award, int> AchieveAwardTest(
            Segment[] segments,
            int playerCount,
            TargetHorseClearCondition ClearCount)
        {
            var referee = StartMatchProceed(segments, playerCount, ClearCount);

            Dictionary<Award, int> dic = new Dictionary<Award, int>();
            foreach (Award a in Enum.GetValues(typeof(Award)))
            {
                var count = referee.TotalAchievedAwardCount("TestA", a);
                dic.Add(a, count);
            }

            return dic;
        }

        public class ActionDelegateForAwardTest : IMatchEventListener
        {
            private Award[] _expectedAwards;

            public ActionDelegateForAwardTest(Award[] expectedAwards)
            {
                _expectedAwards = expectedAwards;
            }

            public void OnStartMatch()
            {
                ;
            }

            public void OnRecovery()
            {
                ;
            }

            public void OnChangeUnitThrowOrder()
            {
                ;
            }

            public void OnUpdateProgress()
            {
                ;
            }

            public void OnEndTurn()
            {
                ;
            }

            public void OnChange()
            {
                ;
            }

            public void OnAchieveAward(Award[] award)
            {
                CollectionAssert.AreEquivalent(award, _expectedAwards);
            }

            public void OnBust()
            {
                ;
            }

            public void OnFinishMatch(MatchFinishStatus status)
            {
                ;
            }

            public void OnRoundReverse()
            {
                ;
            }

            public void OnOverrideThrow(int index, Segment segment, int score)
            {

            }
        }
    }
}