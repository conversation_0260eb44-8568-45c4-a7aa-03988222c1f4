using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game.zeroone;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.zeroone
{
    public partial class Referee01Test
    {
        [TestCaseSource(nameof(SteelThreeThrowAcceptScoreTestCase))]
        // TODO:checkOutの処理は後々追加する予定です
        public int XSteelThreeThrowAcceptScoreTest(int score, int checkOut)
        {
            Unit[] teams = TeamsA_B.ToArray();
            var referee = MatchMaker.Make01(teams, new GameRule01(GameCode._301), false, "");
            referee.StartMatch();
            referee.AcceptScore(score, checkOut, 0);

            return referee.Scorer.CurrentScore(teams[0].Id);
        }
        public static TestCaseData[] SteelThreeThrowAcceptScoreTestCase = new TestCaseData[] {
            new TestCaseData(0,0).Returns(301),  //0~9
            new TestCaseData(1,0).Returns(300),
            new TestCaseData(2,0).Returns(299),
            new TestCaseData(3,0).Returns(298),
            new TestCaseData(4,0).Returns(297),
            new TestCaseData(5,0).Returns(296),
            new TestCaseData(6,0).Returns(295),
            new TestCaseData(7,0).Returns(294),
            new TestCaseData(8,0).Returns(293),
            new TestCaseData(9,0).Returns(292),
            new TestCaseData(10,0).Returns(291),  //10~19
            new TestCaseData(11,0).Returns(290),
            new TestCaseData(12,0).Returns(289),
            new TestCaseData(13,0).Returns(288),
            new TestCaseData(14,0).Returns(287),
            new TestCaseData(15,0).Returns(286),
            new TestCaseData(16,0).Returns(285),
            new TestCaseData(17,0).Returns(284),
            new TestCaseData(18,0).Returns(283),
            new TestCaseData(19,0).Returns(282),
            new TestCaseData(20,0).Returns(281),  //20~29
            new TestCaseData(21,0).Returns(280),
            new TestCaseData(22,0).Returns(279),
            new TestCaseData(23,0).Returns(278),
            new TestCaseData(24,0).Returns(277),
            new TestCaseData(25,0).Returns(276),
            new TestCaseData(26,0).Returns(275),
            new TestCaseData(27,0).Returns(274),
            new TestCaseData(28,0).Returns(273),
            new TestCaseData(29,0).Returns(272),
            new TestCaseData(30,0).Returns(271),  //30~39
            new TestCaseData(31,0).Returns(270),
            new TestCaseData(32,0).Returns(269),
            new TestCaseData(33,0).Returns(268),
            new TestCaseData(34,0).Returns(267),
            new TestCaseData(35,0).Returns(266),
            new TestCaseData(36,0).Returns(265),
            new TestCaseData(37,0).Returns(264),
            new TestCaseData(38,0).Returns(263),
            new TestCaseData(39,0).Returns(262),
            new TestCaseData(40,0).Returns(261),  //40~49
            new TestCaseData(41,0).Returns(260),
            new TestCaseData(42,0).Returns(259),
            new TestCaseData(43,0).Returns(258),
            new TestCaseData(44,0).Returns(257),
            new TestCaseData(45,0).Returns(256),
            new TestCaseData(46,0).Returns(255),
            new TestCaseData(47,0).Returns(254),
            new TestCaseData(48,0).Returns(253),
            new TestCaseData(49,0).Returns(252),
            new TestCaseData(50,0).Returns(251),  //50~59
            new TestCaseData(51,0).Returns(250),
            new TestCaseData(52,0).Returns(249),
            new TestCaseData(53,0).Returns(248),
            new TestCaseData(54,0).Returns(247),
            new TestCaseData(55,0).Returns(246),
            new TestCaseData(56,0).Returns(245),
            new TestCaseData(57,0).Returns(244),
            new TestCaseData(58,0).Returns(243),
            new TestCaseData(59,0).Returns(242),
            new TestCaseData(60,0).Returns(241),  //60~69
            new TestCaseData(61,0).Returns(240),
            new TestCaseData(62,0).Returns(239),
            new TestCaseData(63,0).Returns(238),
            new TestCaseData(64,0).Returns(237),
            new TestCaseData(65,0).Returns(236),
            new TestCaseData(66,0).Returns(235),
            new TestCaseData(67,0).Returns(234),
            new TestCaseData(68,0).Returns(233),
            new TestCaseData(69,0).Returns(232),
            new TestCaseData(70,0).Returns(231),  //70~79
            new TestCaseData(71,0).Returns(230),
            new TestCaseData(72,0).Returns(229),
            new TestCaseData(73,0).Returns(228),
            new TestCaseData(74,0).Returns(227),
            new TestCaseData(75,0).Returns(226),
            new TestCaseData(76,0).Returns(225),
            new TestCaseData(77,0).Returns(224),
            new TestCaseData(78,0).Returns(223),
            new TestCaseData(79,0).Returns(222),
            new TestCaseData(80,0).Returns(221),  //80~89
            new TestCaseData(81,0).Returns(220),
            new TestCaseData(82,0).Returns(219),
            new TestCaseData(83,0).Returns(218),
            new TestCaseData(84,0).Returns(217),
            new TestCaseData(85,0).Returns(216),
            new TestCaseData(86,0).Returns(215),
            new TestCaseData(87,0).Returns(214),
            new TestCaseData(88,0).Returns(213),
            new TestCaseData(89,0).Returns(212),
            new TestCaseData(90,0).Returns(211),  //90~99
            new TestCaseData(91,0).Returns(210),
            new TestCaseData(92,0).Returns(209),
            new TestCaseData(93,0).Returns(208),
            new TestCaseData(94,0).Returns(207),
            new TestCaseData(95,0).Returns(206),
            new TestCaseData(96,0).Returns(205),
            new TestCaseData(97,0).Returns(204),
            new TestCaseData(98,0).Returns(203),
            new TestCaseData(99,0).Returns(202),
            new TestCaseData(100,0).Returns(201),  //100~109
            new TestCaseData(101,0).Returns(200),
            new TestCaseData(102,0).Returns(199),
            new TestCaseData(103,0).Returns(198),
            new TestCaseData(104,0).Returns(197),
            new TestCaseData(105,0).Returns(196),
            new TestCaseData(106,0).Returns(195),
            new TestCaseData(107,0).Returns(194),
            new TestCaseData(108,0).Returns(193),
            new TestCaseData(109,0).Returns(192),
            new TestCaseData(110,0).Returns(191),  //110~119
            new TestCaseData(111,0).Returns(190),
            new TestCaseData(112,0).Returns(189),
            new TestCaseData(113,0).Returns(188),
            new TestCaseData(114,0).Returns(187),
            new TestCaseData(115,0).Returns(186),
            new TestCaseData(116,0).Returns(185),
            new TestCaseData(117,0).Returns(184),
            new TestCaseData(118,0).Returns(183),
            new TestCaseData(119,0).Returns(182),
            new TestCaseData(120,0).Returns(181),  //120~129
            new TestCaseData(121,0).Returns(180),
            new TestCaseData(122,0).Returns(179),
            new TestCaseData(123,0).Returns(178),
            new TestCaseData(124,0).Returns(177),
            new TestCaseData(125,0).Returns(176),
            new TestCaseData(126,0).Returns(175),
            new TestCaseData(127,0).Returns(174),
            new TestCaseData(128,0).Returns(173),
            new TestCaseData(129,0).Returns(172),
            new TestCaseData(130,0).Returns(171),  //130~139
            new TestCaseData(131,0).Returns(170),
            new TestCaseData(132,0).Returns(169),
            new TestCaseData(133,0).Returns(168),
            new TestCaseData(134,0).Returns(167),
            new TestCaseData(135,0).Returns(166),
            new TestCaseData(136,0).Returns(165),
            new TestCaseData(137,0).Returns(164),
            new TestCaseData(138,0).Returns(163),
            new TestCaseData(139,0).Returns(162),
            new TestCaseData(140,0).Returns(161),  //140~149
            new TestCaseData(141,0).Returns(160),
            new TestCaseData(142,0).Returns(159),
            new TestCaseData(143,0).Returns(158),
            new TestCaseData(144,0).Returns(157),
            new TestCaseData(145,0).Returns(156),
            new TestCaseData(146,0).Returns(155),
            new TestCaseData(147,0).Returns(154),
            new TestCaseData(148,0).Returns(153),
            new TestCaseData(149,0).Returns(152),
            new TestCaseData(150,0).Returns(151),  //150~159
            new TestCaseData(151,0).Returns(150),
            new TestCaseData(152,0).Returns(149),
            new TestCaseData(153,0).Returns(148),
            new TestCaseData(154,0).Returns(147),
            new TestCaseData(155,0).Returns(146),
            new TestCaseData(156,0).Returns(145),
            new TestCaseData(157,0).Returns(144),
            new TestCaseData(158,0).Returns(143),
            new TestCaseData(159,0).Returns(142),
            new TestCaseData(160,0).Returns(141),  //160~169
            new TestCaseData(161,0).Returns(140),
            new TestCaseData(162,0).Returns(139),
            new TestCaseData(164,0).Returns(137),
            new TestCaseData(165,0).Returns(136),
            new TestCaseData(167,0).Returns(134),
            new TestCaseData(168,0).Returns(133),
            new TestCaseData(170,0).Returns(131),  //170~179
            new TestCaseData(171,0).Returns(130),
            new TestCaseData(174,0).Returns(127),
            new TestCaseData(177,0).Returns(124),
            new TestCaseData(180,0).Returns(121),  //180
        };
    }
}