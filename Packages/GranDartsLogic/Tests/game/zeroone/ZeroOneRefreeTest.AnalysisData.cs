using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.zeroone;
using NUnit.Framework;
using UnityEngine;

namespace com.luxza.grandartslogic.tests.game.zeroone
{
    public partial class ZeroOneRefreeTest
    {
        public static TestCaseData[] Test6throwCountToReduceByIndex = new TestCaseData[]
       {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: {12,true},{17,true}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(12,true),(17,true)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:{17,true},{12,true}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(17,true),(12,true)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:{49,true},{41,true}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(49,true),(41,true)}),
       };
        //削り段階に要したダーツの投数.削り段階: スローしたあとの残りスコアが初めて 3投でフィニッシュできる数字になるまで
        [TestCaseSource(nameof(Test6throwCountToReduceByIndex))]
        public (int, bool)[] Test06throwCountToReduce(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            var reduceCounts = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Count(t => t.isReduce)).ToArray();
            var reduceThrow = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Last().isReduce).ToArray();

            List<(int, bool)> items = new List<(int, bool)>();
            items.Add((reduceCounts[0], reduceThrow[0]));
            items.Add((reduceCounts[1], reduceThrow[1]));

            return items.ToArray();
        }

        public static TestCaseData[] Test7throwCountToArrangeByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 4 true,0 false")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(4,true),(0,false)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0 false,5 true")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(0,false),(5,true)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:0 false,10 true")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(0,false),(10,true)}),
        };
        //削り段階に要したダーツの投数.　削り段階: スローしたあとの残りスコアが初めて 3 投でフィニッシュできる数字になるまで
        [TestCaseSource(nameof(Test7throwCountToArrangeByIndex))]
        public (int, bool)[] Test07throwCountToArrange(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            var arrangeCount = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Count(t => t.isArrange)).ToArray();
            var hasChanceToFinishAtLastThrow = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Last().hasChanceToFinishAtThisThrow).ToArray();

            List<(int, bool)> items = new List<(int, bool)>();
            items.Add((arrangeCount[0], hasChanceToFinishAtLastThrow[0]));
            items.Add((arrangeCount[1], hasChanceToFinishAtLastThrow[1]));

            return items.ToArray();
        }

        public static TestCaseData[] Test8throwCountTo_FinishByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ:{4,true},{0,false}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(4,true),(0,false)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ:{0,false},{2,true}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(0,false),(2,true)}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ:{0,false},{0,false}")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new (int, bool) []{(0,false),(0,false)}),
        };
        //フィニッシュ段階に要したダーツの投数　アレンジ段階が終わった次のスローから、フィニッシュするまで
        [TestCaseSource(nameof(Test8throwCountTo_FinishByIndex))]
        public (int, bool)[] Test08throwCountToFinish(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            var hasChanceToFinishCount = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Count(t => t.hasChanceToFinishAtThisThrow)).ToArray();
            var finishAtLastThrow = referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.RoundSummary(u.AllMember.First().GranId).SelectMany(r => r.throwDatas).Last().isFinish).ToArray();

            List<(int, bool)> items = new List<(int, bool)>();
            items.Add((hasChanceToFinishCount[0], finishAtLastThrow[0]));
            items.Add((hasChanceToFinishCount[1], finishAtLastThrow[1]));

            return items.ToArray();
        }



        public static TestCaseData[] Test10T20RateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）: 0.529f,0.133f")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{0.353f,0.067f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:0.143f,0.263f")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{0.143f,0.263f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:0.143f,0.263f")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{0.143f,0.263f}),
        };

        [TestCaseSource(nameof(Test10T20RateByIndex))]
        public float[] Test10T20Rate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.T20HitRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test9BullRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 0.3,0.167f")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.3f, 0.167f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0.19,0.333")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.19f, 0.333f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:0.0,0.017f")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.0f, 0.017f}),
        };

        [TestCaseSource(nameof(Test9BullRateByIndex))]
        public float[] Test09BullRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.BullHitRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test10BullOutRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 0.25,0.0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.25f, 0.0f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0.19,0.333")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.143f, 0.238f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:0.0,0.017f")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.0f, 0.017f}),
        };

        [TestCaseSource(nameof(Test10BullOutRateByIndex))]
        public float[] Test10BullOutRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.SBullHitRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test11BullInRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 0.05,0.167")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.05f, 0.167f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0.048,0.095")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.048f, 0.095f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:0.0,0.0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.0f, 0.0f}),
        };

        [TestCaseSource(nameof(Test11BullInRateByIndex))]
        public float[] Test11BullInRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.DBullHitRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test11Over100RoundCountByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 1,B 2")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{1, 2}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 1,B 2")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{1,2}),
        };

        [TestCaseSource(nameof(Test11Over100RoundCountByIndex))]
        public int[] Test11Over100RoundCount(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            //100点以上140点未満の数値を集計する
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.Over100ScoreCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test12Over140RoundCountByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 0,B 1")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0,B 1")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,1}),
        };

        [TestCaseSource(nameof(Test12Over140RoundCountByIndex))]
        public int[] Test12Over140RoundCount(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            //140点以上180点未満の数値を集計する
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.Over140ScoreCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test12innerBullCountDevidedByWholeBullCountRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 0.167,1.0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.167f,1.0f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0.25,0.286")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.25f, 0.286f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）: 0.0,0.0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.0f, 0.0f}),
        };

        [TestCaseSource(nameof(Test12innerBullCountDevidedByWholeBullCountRateByIndex))]
        public float[] Test12innerBullCountDevidedByWholeBullCountRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {

            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.InnerBullCountDevidedByWholeBullCountRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test13NoBullRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）: 0.571,0.833")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{0.571f, 0.833f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:0.571, 0.143")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{ 0.571f, 0.143f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:1.0,0.85")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new float []{1.0f, 0.85f}),
        };

        [TestCaseSource(nameof(Test13NoBullRateByIndex))]
        public float[] Test13NoBullRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.NoBullHitRate(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test13T80ChanceCountByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A 1,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{1,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
        };

        [TestCaseSource(nameof(Test13T80ChanceCountByIndex))]
        public int[] Test13T80ChanceCount(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.Ton80ChanceCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test14FirstNineDartsPPRByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A 103.333,B 43.333")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{ 103.333f, 43.333f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 48.333,B 121.667")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{ 48.333f, 121.667f}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 48.333f,B 121.667f")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{48.333f, 121.667f}),
        };

        [TestCaseSource(nameof(Test14FirstNineDartsPPRByIndex))]
        public float[] Test14FirstNineDartsPPR(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.FirstNineDartsPPR(u.AllMember.First().GranId)).ToArray();
        }


        public static TestCaseData[] Test16BustByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:A 0,B 1")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0, 1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:A 0, B 2")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0, 2}),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0, B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0, 0}),
        };

        [TestCaseSource(nameof(Test16BustByIndex))]
        public int[] Test16Bust(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.BustCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test17HighOffByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A false,B false")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new bool []{false,false}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:A false,B false")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new bool []{false,false}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:A false, B false")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new bool []{false,false}),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ）:A false,B false")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new bool []{false,false}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A false,B false")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new bool []{false,false}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A false, B false")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new bool []{false,false}),
        };
        //ハイオフのテストコード
        //ハイオフとは100以上のアレンジのことでそれが成功したかどうかが返る
        [TestCaseSource(nameof(Test17HighOffByIndex))]
        public bool[] Test17HighOffSuccess(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.IsHighOffSuccess(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test23HighOffTryCountByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 2,B 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{2,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:A 1,B 1")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{1,1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ: A 4,B 3")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{4,3}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ: A 0,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 0,B 1")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0, B 1")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,1}),

    };
        //HighOffCountのテストコード
        //HighOffとは100点以上アレンジのこと
        //100点以上のアレンジに何回挑戦したのかの回数が変える
        [TestCaseSource(nameof(Test23HighOffTryCountByIndex))]
        public int[] Test23HighOffTryCount(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.HighOffTryCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test18checkoutSuccessByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 4,B 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{4,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:A 0,B 3")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0,3}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ: A 0, B 7")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0,7}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case D（2ユニットプレイ: A 4,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{4,0}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case E（2ユニットプレイ）:A 0,B 6")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,6}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0, B 6")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,6}),
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.OpenOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0, B 6")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,6}),
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseG,
                    2,
                    1,
                    BullOption.SeparateBull,
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case G（2ユニットプレイ）:A 2, B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{2,0}),
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseH,
                    2,
                    1,
                    BullOption.SeparateBull,
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case H（2ユニットプレイ）:A 1, B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{1,0}),

    };
        //チェックアウトトライカウントのテストコード
        //チェックアウトとは60点以下の時のことでゲームが1投で終えたかどうかのトライ数が返る
        //(チェックアウト成功数)/(60点以下のスローを投げた回数)
        [TestCaseSource(nameof(Test18checkoutSuccessByIndex))]
        public int[] Test18CheckoutTryCount(TestCaseType testCaseType, int unitCount, int memberCount, BullOption bull, OutCondition gameend)
        {
            if (testCaseType == TestCaseType.testCaseG || testCaseType == TestCaseType.testCaseH)
            {
                var referee = SetTestCaseTypeStartMatch301GameProceed(testCaseType, unitCount, memberCount, gameend, bull);
                return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.CheckOutTryCount(u.AllMember.First().GranId)).ToArray();
            }else{
                var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
                return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.CheckOutTryCount(u.AllMember.First().GranId)).ToArray();
            }

        }

        public static TestCaseData[] Test19HatChanceByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 2,B 1")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{2,1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case B（2ユニットプレイ）:A 1,B 1")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{1,1}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                })
                .SetName("Test Case C（2ユニットプレイ）:A 0, B 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{0,0}),

        };
        //ハイオフのテストコード
        //ハイオフとは100以上のアレンジのことでそれが成功したかどうかが返る
        [TestCaseSource(nameof(Test19HatChanceByIndex))]
        public int[] Test19HatChance(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.HatChanceCount(u.AllMember.First().GranId)).ToArray();
        }

        public static TestCaseData[] Test20MissHitRateByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                    "TestA_1",
                    "TestB_1",
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.050f },
                        { Segment.Single3In, 0.100f },
                        { Segment.Single5In, 0.050f },
                        { Segment.Single7In, 0.050f },
                        { Segment.Single10In, 0.050f },
                        { Segment.Single13In, 0.050f },
                        { Segment.Single15In, 0.050f },
                        { Segment.Single18In, 0.050f },
                        { Segment.Single19In, 0.050f },
                        { Segment.Single20In, 0.050f },
                        { Segment.Single1Out, 0.050f },
                        { Segment.Double13, 0.050f },
                        { Segment.Triple20, 0.050f },
                    },
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.056f },
                        { Segment.Single2In, 0.167f },
                        { Segment.Single4In, 0.111f },
                        { Segment.Single6In, 0.056f },
                        { Segment.Single10In, 0.167f },
                        { Segment.Single12In, 0.056f },
                        { Segment.Triple20, 0.167f },
                    }
                })
                .SetName("Test Case A（2ユニットプレイ）:A { Single1In, 0.050 },{ Single3In, 0.100 },{ Single5In, 0.050 },{ Single7In, 0.050 },{ Single10In, 0.050 },{ Single13In, 0.050 },{ Single15In, 0.050 },{ Single18In, 0.050 },{ Single19In, 0.050 },{ Single20In, 0.050 },{ Single1Out, 0.050 },{ Double13, 0.050 },{ Triple20, 0.050 },,B　{ Single1In, 0.056 },{ Single2In, 0.167 },{ Single4In, 0.111 },{ Single6In, 0.056 },{ Single10In, 0.167 },{ Single12In, 0.056 },{ Triple20, 0.167 }, }")
                .SetCategory("01Game FatBull InputData Test"),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseB,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                    "TestA_1",
                    "TestB_1",
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.143f },
                        { Segment.Single3In, 0.143f },
                        { Segment.Single5In, 0.048f },
                        { Segment.Single7In, 0.143f },
                        { Segment.Single11In, 0.048f },
                        { Segment.Single17In, 0.048f },
                        { Segment.Single19In, 0.095f },
                        { Segment.Triple18, 0.048f },
                        { Segment.OUT, 0.095f },
                    },
                    new Dictionary<Segment, float>() {
                        { Segment.Single2In, 0.048f },
                        { Segment.Single6In, 0.048f },
                        { Segment.Single7In, 0.048f },
                        { Segment.Single10In, 0.048f },
                        { Segment.Single11In, 0.095f },
                        { Segment.Single12In, 0.048f },
                        { Segment.Single20In, 0.095f },
                        { Segment.Triple13, 0.095f },
                        { Segment.Triple20, 0.048f },
                    }
                })
                .SetName("Test Case B（2ユニットプレイ）:")
                .SetCategory("01Game FatBull InputData Test"),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseC,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                    "TestA_1",
                    "TestB_1",
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.217f },
                        { Segment.Single2In, 0.017f },
                        { Segment.Single3In, 0.05f },
                        { Segment.Single5In, 0.15f },
                        { Segment.Single6In, 0.05f },
                        { Segment.Single7In, 0.017f },
                        { Segment.Single8In, 0.033f },
                        { Segment.Single10In, 0.133f },
                        { Segment.Single11In, 0.083f },
                        { Segment.Single12In, 0.033f },
                        { Segment.Single14In, 0.033f },
                        { Segment.Single15In, 0.033f },
                        { Segment.Single18In, 0.033f },
                        { Segment.Single2Out, 0.017f },
                        { Segment.Single3Out, 0.017f },
                        { Segment.Single5Out, 0.033f },
                        { Segment.Single8Out, 0.017f },
                        { Segment.Triple5, 0.017f },
                    },
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.083f },
                        { Segment.Single2In, 0.067f },
                        { Segment.Single3In, 0.083f },
                        { Segment.Single4In, 0.033f },
                        { Segment.Single5In, 0.067f },
                        { Segment.Single6In, 0.050f },
                        { Segment.Single7In, 0.033f },
                        { Segment.Single8In, 0.083f },
                        { Segment.Single10In, 0.017f },
                        { Segment.Single11In, 0.050f },
                        { Segment.Single12In, 0.017f },
                        { Segment.Single13In, 0.033f },
                        { Segment.Single16In, 0.033f },
                        { Segment.Single17In, 0.017f },
                        { Segment.Single18In, 0.033f },
                        { Segment.Single20In, 0.033f },
                        { Segment.Single3Out, 0.017f },
                        { Segment.Single5Out, 0.033f },
                        { Segment.Single7Out, 0.033f },
                        { Segment.Single9Out, 0.017f },
                        { Segment.Single12Out, 0.033f },
                        { Segment.Single14Out, 0.017f },
                        { Segment.Single18Out, 0.017f },
                        { Segment.Triple3, 0.017f },
                        { Segment.Triple6, 0.017f },

                    }
                })
                .SetName("Test Case C（2ユニットプレイ）:")
                .SetCategory("01Game FatBull InputData Test"),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseD,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                    "TestA_1",
                    "TestB_1",
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.059f },
                        { Segment.Single9In, 0.059f },
                        { Segment.Single12In, 0.059f },
                        { Segment.Single18In, 0.059f },
                        { Segment.Single20In, 0.176f },
                        { Segment.Triple5, 0.118f },
                        { Segment.Triple1, 0.059f },
                        { Segment.Double4, 0.059f },
                    },
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.267f },
                        { Segment.Single5In, 0.267f },
                        { Segment.Single12In, 0.067f },
                        { Segment.Single18In, 0.133f },
                        { Segment.Single20In, 0.067f },
                        { Segment.Triple5, 0.133f },
                    }
                })
                .SetName("Test Case D（2ユニットプレイ）:")
                .SetCategory("01Game SepaBull InputData Test"),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseE,
                    2,
                    1,
                    "A",
                    OutCondition.OpenOut,
                    "TestA_1",
                    "TestB_1",
                    new Dictionary<Segment, float>() {
                        { Segment.Single1In, 0.190f },
                        { Segment.Single5In, 0.143f },
                        { Segment.Single18In, 0.048f },
                        { Segment.Single20In, 0.095f },
                        { Segment.Double20, 0.048f },
                        { Segment.Triple1, 0.048f },
                        { Segment.Triple5, 0.190f },
                        { Segment.OUT, 0.095f },
                    },
                    new Dictionary<Segment, float>() {
                        { Segment.Single3In, 0.053f },
                        { Segment.Single5In, 0.053f },
                        { Segment.Single18In, 0.053f },
                        { Segment.Single20In, 0.158f },
                        { Segment.Single1Out, 0.053f },
                        { Segment.Double3, 0.053f },
                        { Segment.Double20, 0.105f },
                        { Segment.Triple3, 0.105f },
                        { Segment.OUT, 0.053f },
                    }
                })
                .SetName("Test Case E（2ユニットプレイ）:")
                .SetCategory("01Game SepaBull InputData Test"),
        };
        //missrateテストコード
        [TestCaseSource(nameof(Test20MissHitRateByIndex))]
        public void Test20MissHitRate(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend, string granIdUnitA, string granIdUnitB, Dictionary<Segment, float> expectedDicUnitA, Dictionary<Segment, float> expectedDicUnitB)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            var result = new Dictionary<Segment, float>();

            result = referee.Scorer.HitRateDic(granIdUnitA);

            foreach (var item in expectedDicUnitA)
            {
                if (!result.Keys.Any(k => k == item.Key))
                {
                    Debug.Log($"not Any Error A  item.key{item.Key} itemValue{item.Value}");
                }
                Assert.That(result.Keys.Any(k => k == item.Key), Is.True);

                if (result[item.Key] != item.Value)
                {
                    Debug.Log($"Error A  item.key{item.Key} itemValue{item.Value}");
                }
                Assert.That(result[item.Key], Is.EqualTo(item.Value));

            }

            result = referee.Scorer.HitRateDic(granIdUnitB);



            foreach (var item in expectedDicUnitB)
            {
                if (!result.Keys.Any(k => k == item.Key))
                {
                    Debug.Log($"not Any Error B  item.key{item.Key} itemValue{item.Value}");
                }
                Assert.That(result.Keys.Any(k => k == item.Key), Is.True);
                if (result[item.Key] != item.Value)
                {
                    Debug.Log($"Error B  item.key{item.Key} itemValue{item.Value}");
                }

                Assert.That(result[item.Key], Is.EqualTo(item.Value));
            }
        }

        public static TestCaseData[] Test21CheckOutNumberByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 26,B 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new bool []{true,false}),
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    BullOption.FatBull,
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 0,B 6")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new bool []{false,true}),
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseH,
                    2,
                    1,
                    BullOption.SeparateBull,
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case H（2ユニットプレイ）:A 50,B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new bool []{true,false}),
        };
        [TestCaseSource(nameof(Test21CheckOutNumberByIndex))]
        public bool[] Test21CheckOutNumber(TestCaseType testCaseType, int unitCount, int memberCount,BullOption bullOption, OutCondition gameend)
        {
            if (testCaseType == TestCaseType.testCaseG || testCaseType == TestCaseType.testCaseH)
            {
                var referee = SetTestCaseTypeStartMatch301GameProceed(testCaseType, unitCount, memberCount, gameend, bullOption);
                return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.IsCheckoutSucceed(u.AllMember.First().GranId)).ToArray();
            }
            else
            {
                var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, bullOption);
                return referee.Match.ParticipantTeams.AllUnits.Select(u => referee.Scorer.IsCheckoutSucceed(u.AllMember.First().GranId)).ToArray();
            }
        }

        public static TestCaseData[] Test22CheckOutTryCountByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseA,
                    2,
                    1,
                    "TestA_1",
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case A（2ユニットプレイ）:A 50 1 40 1 36 0 26 1 18 0")
                .SetCategory("01Game FatBull InputData Test")
                .Returns(new int []{1,1,0,1,0}),

            new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "TestA_1",
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:A 50 0 40 0 36 0 26 0 18 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,0,0,0,0}),
             new TestCaseData(new object[]
                {
                    TestCaseType.testCaseF,
                    2,
                    1,
                    "TestB_1",
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case F（2ユニットプレイ）:B 38 1 28 1 24 1 6 1")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new int []{0,0,0,0,0}),
        };
        [TestCaseSource(nameof(Test22CheckOutTryCountByIndex))]
        public int[] Test22CheckOutTryCount(TestCaseType testCaseType, int unitCount, int memberCount, string granId, OutCondition gameend)
        {
            var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.FatBull);
            var resultDic = referee.Scorer.CheckOutDoubleTryCountDic(granId);

            foreach (var item in resultDic)
            {
                Debug.Log("CheckOutTryCount Key:" + item.Key + " " + item.Value);
            }

            return new int[] {
                            resultDic[50],
                            resultDic[40],
                            resultDic[36],
                            resultDic[26],
                            resultDic[18],
            };

        }

        public static TestCaseData[] Test24checkoutRates = new TestCaseData[]
        {
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseG,
                    2,
                    1,
                    "A",
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case G（2ユニットプレイ）:A 50%, B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{50.0f,0.0f}),
                new TestCaseData(new object[]
                {
                    TestCaseType.testCaseH,
                    2,
                    1,
                    "A",
                    OutCondition.DoubleOut,
                })
                .SetName("Test Case H（2ユニットプレイ）:A 100%, B 0")
                .SetCategory("01Game SepaBull InputData Test")
                .Returns(new float []{100.0f,0.0f}),

    };
        //チェックアウトRateのテストコード
        //チェックアウトとは60点以下の時のことでゲームが1投で終えたかどうかのトライ数が返る
        //(チェックアウト成功数)/(60点以下のスローを投げた回数)
        [TestCaseSource(nameof(Test24checkoutRates))]
        public float[] Test24CheckoutRateCount(TestCaseType testCaseType, int unitCount, int memberCount, string unitId, OutCondition gameend)
        {
            if (testCaseType == TestCaseType.testCaseG || testCaseType == TestCaseType.testCaseH)
            {
                var referee = SetTestCaseTypeStartMatch301GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.SeparateBull);
                var units = referee.Match.ParticipantTeams.AllUnits;

                var checkOutTry = referee.Scorer.CheckOutTryCount(units[0].AllMember.First().GranId);
                var isCheckoutSucceed = referee.Scorer.IsCheckoutSucceed(units[0].AllMember.First().GranId);
                var CheckOutRate1 = (isCheckoutSucceed ? 1.0f : 0.0f / checkOutTry) * 100.0f;
                Debug.Log($"CheckOutRate1:{CheckOutRate1} = (checkOutNumber:{isCheckoutSucceed} == 0 ? 0.0f : 1.0f / checkOutTry:{checkOutTry}) * 100.0f;");
                checkOutTry = referee.Scorer.CheckOutTryCount(units[1].AllMember.First().GranId);
                isCheckoutSucceed = referee.Scorer.IsCheckoutSucceed(units[1].AllMember.First().GranId);
                var CheckOutRate2 = (isCheckoutSucceed ? 1.0f : 0.0f / checkOutTry) * 100.0f;
                Debug.Log($"CheckOutRate2:{CheckOutRate2} = (checkOutNumber:{isCheckoutSucceed} == 0 ? 0.0f : 1.0f / checkOutTry:{checkOutTry}) * 100.0f;");
                return new float[] { CheckOutRate1, CheckOutRate2 };
            }
            else
            {
                var referee = SetTestCaseTypeStartMatch501GameProceed(testCaseType, unitCount, memberCount, gameend, BullOption.SeparateBull);
                var units = referee.Match.ParticipantTeams.AllUnits;
                var checkOutTry = referee.Scorer.CheckOutTryCount(units[0].AllMember.First().GranId);
                var isCheckoutSucceed = referee.Scorer.IsCheckoutSucceed(units[0].AllMember.First().GranId);
                var CheckOutRate1 = (isCheckoutSucceed ? 1.0f : 0.0f / checkOutTry) * 100.0f;
                checkOutTry = referee.Scorer.CheckOutTryCount(units[1].AllMember.First().GranId);
                isCheckoutSucceed = referee.Scorer.IsCheckoutSucceed(units[1].AllMember.First().GranId);
                var CheckOutRate2 = (isCheckoutSucceed ? 1.0f : 0.0f / checkOutTry) * 100.0f;
                return new float[] { CheckOutRate1, CheckOutRate2 };
            }
        }
    }
}