using System;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.settings;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.zeroone
{
    public partial class Referee01Test
    {
        public static TestCaseData[] HandicapScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:11),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "teamA is Rank A , teamB is RankB -> 301 , 245 ")
                .Returns(new int[] {301, 245}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:9),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:13),
                            },
                            2
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "C",
                            new Player[] {
                                new Player("TestC", 3, rating01:2.2f),
                            },
                            3
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "D",
                            new Player[] {
                                new Player("TestD", 4, rating01:19),
                            },
                            4
                        ),
                    },
                })
                .SetName(
                    "teamA is Rank BB , teamB is RankAA, teamC is RankC, teamD is RankSSS -> 161, 217, 121, 301")
                .Returns(new int[] {161, 217, 121, 301}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:9),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:13),
                            },
                            2
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "C",
                            new Player[] {
                                new Player("TestC", 3, rating01:2.2f),
                            },
                            3
                        ),
                    },
                })
                .SetName(
                    "teamA is Rank BB , teamB is RankAA, teamC is RankC -> 245, 301, 147,")
                .Returns(new int[] {245, 301, 147}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("Test_alpha", 1, rating01:9),
                                new Player("Test_alpha_1", 2, rating01:2.4f),
                                new Player("Test_alpha_2", 3, rating01:4.7f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("Test_beta", 4, rating01:13),
                            },
                            2
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "C",
                            new Player[] {
                                new Player("Test_gannma", 5, rating01:2.2f),
                            },
                            3
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "D",
                            new Player[] {
                                new Player("Test_delta", 6, rating01:19),
                            },
                            4
                        ),
                    },
                })
                .SetName(
                    "teamA teamrating 5.5 , teamB is RankAA, teamC is RankC, teamD is RankSSS -> 121, 217, 121, 301")
                .Returns(new int[] {121, 217, 121, 301}),
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamA",
                            new Player[] {
                                new Player("TestA", 1, rating01:14.8f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamB",
                            new Player[] {
                                new Player("FreeGuest", 2, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "Set AutoHandicap toTeamA(11.5) is 301 TeamB_freeguest is 301")
                .Returns(new int[] {301, 301}),
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamA",
                            new Player[] {
                                new Player("TestA", 1, rating01:14.8f, ratingCR:1.0f),
                                new Player("FreeGuest_1", 2, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamB",
                            new Player[] {
                                new Player("FreeGuest_2", 3, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                                new Player("FreeGuest_3", 4, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "Set AutoHandicap toTeamA(14.8) is 301 TeamB_ 2 free is 301")
                .Returns(new int[] {301, 301}),
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamADouble",
                            new Player[] {
                                new Player("TestA", 1, rating01:14.8f),
                                new Player("TestAFreeGuest_1", 2, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "TeamBDouble",
                            new Player[] {
                                new Player("FreeGuest_2", 3, rating01:1.0f, ratingCR:1.0f, isFreeGuest:true),
                                new Player("TestB_1", 4, rating01:1.8f, ratingCR:1.0f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "Set AutoHandicap toTeamA(14.8) 1free is 301 TeamB(1.8) 1 free is 121")
                .Returns(new int[] {301, 121}),
        };

        [TestCaseSource(nameof(HandicapScoreByIndex))]
        public int[] XHandicap301ScoreTest(Unit[] teams)
        {
            var referee = StartMatch301WithHandicapAndProceed(new Segment[0], teams);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }
        public static TestCaseData[] Handicap501ScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "501 teamA is 16.4f , teamB is 7.7f (16.4-7.7=8.7≒9.0)　-> 501 , 267")
                .Returns(new int[] {501, 267}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                                new Player("TestA_1", 2, rating01:2.0f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 3, rating01:7.7f),
                                new Player("TestB_1", 4, rating01:19.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "501 teamA is 9.2 , teamB is 13.7　(13.7-9.2=4.5) -> 384 , 501")
                .Returns(new int[] {384, 501}),
        };
        [TestCaseSource(nameof(Handicap501ScoreByIndex))]
        public int[] XHandicap501ScoreTest(Unit[] teams)
        {
            var referee = Make501Ref(teams, handicap: HandicapSetting.Auto);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }

        public static TestCaseData[] Handicap701ScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "701 teamA is 16.4f , teamB is 7.7f (16.4-7.7=8.7≒9.0)　-> 701 , 359")
                .Returns(new int[] {701, 359}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:15.9f),
                                new Player("TestA_1", 2, rating01:2.0f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 3, rating01:17.3f),
                                new Player("TestB_1", 4, rating01:19.3f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "701 teamA is 8.95 >  , teamB is 18.3　(18.3-9.0= 9.3 > 9.0) -> 359 , 701")
                .Returns(new int[] {359, 701}),
        };
        [TestCaseSource(nameof(Handicap701ScoreByIndex))]
        public int[] XHandicap701ScoreTest(Unit[] teams)
        {
            var referee = Make701Ref(teams, handicap: HandicapSetting.Auto);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }

        public static TestCaseData[] Handicap901ScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "901 teamA is 16.4f , teamB is 7.7f (16.4-7.7=8.7≒9.0)　-> 901, 451")
                .Returns(new int[] {901, 451}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                                new Player("TestA_1", 2, rating01:2.0f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 3, rating01:7.7f),
                                new Player("TestB_1", 4, rating01:19.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "901 teamA is 9.2 , teamB is 13.7　(13.7-9.2=4.5) -> 676, 901")
                .Returns(new int[] {676, 901}),
        };
        [TestCaseSource(nameof(Handicap901ScoreByIndex))]
        public int[] XHandicap901ScoreTest(Unit[] teams)
        {
            var referee = Make901Ref(teams, handicap: HandicapSetting.Auto);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }

        public static TestCaseData[] Handicap1101ScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "1101 teamA is 16.4f , teamB is 7.7f (16.4-7.7=8.7≒9.0)　-> 1101, 543")
                .Returns(new int[] {1101, 543}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                                new Player("TestA_1", 2, rating01:2.0f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 3, rating01:7.7f),
                                new Player("TestB_1", 4, rating01:19.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "1101 teamA is 9.2 , teamB is 13.7　(13.7-9.2=4.5) -> 822, 1101")
                .Returns(new int[] {822, 1101}),
        };
        [TestCaseSource(nameof(Handicap1101ScoreByIndex))]
        public int[] XHandicap1101ScoreTest(Unit[] teams)
        {
            var referee = Make1101Ref(teams, handicap: HandicapSetting.Auto);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }

        public static TestCaseData[] Handicap1501ScoreByIndex = new TestCaseData[] {
            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 2, rating01:7.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "1501 teamA is 16.4f , teamB is 7.7f (16.4-7.7=8.7≒9.0)　-> 1501, 727")
                .Returns(new int[] {1501, 727}),

            new TestCaseData(new object[] {
                    new Unit[] {
                        UnitFactory.Instance.TeamUp
                        (
                            "A",
                            new Player[] {
                                new Player("TestA", 1, rating01:16.4f),
                                new Player("TestA_1", 2, rating01:2.0f),
                            },
                            1
                        ),
                        UnitFactory.Instance.TeamUp
                        (
                            "B",
                            new Player[] {
                                new Player("TestB", 3, rating01:7.7f),
                                new Player("TestB_1", 4, rating01:19.7f),
                            },
                            2
                        ),
                    },
                })
                .SetName(
                    "1501 teamA is 9.2 , teamB is 13.7　(13.7-9.2=4.5) -> 1114, 1501")
                .Returns(new int[] {1114, 1501}),
        };
        [TestCaseSource(nameof(Handicap1501ScoreByIndex))]
        public int[] XHandicap1501ScoreTest(Unit[] teams)
        {
            var referee = Make1501Ref(teams, handicap: HandicapSetting.Auto);
            int i = 0;
            int[] scores = new int[referee.Participants.AllUnits.Length];
            foreach (var item in referee.Participants.AllUnits)
            {
                scores[i] = referee.CurrentScore(item.Id);
                i++;
            }

            return scores;
        }
    }
}