using System.IO;
using System.Text;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.zeroone
{
    public partial class Referee01Test
    {
        //        public static TestCaseData[] ArrangeListTestCases = new TestCaseData[]{
        //new TestCaseData (180,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,180,1,Throw,arrange,list,→,T20,T20,T20,").Returns(new Segment[]{Segment.Triple20 ,Segment.Triple20,Segment.Triple20}),
        //new TestCaseData (177,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,177,1,Throw,arrange,list,→,T20,T20,T19,").Returns(new Segment[]{Segment.Triple20 ,Segment.Triple20,Segment.Triple19}),
        //new TestCaseData (174,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,174,1,Throw,arrange,list,→,T20,T20,T18,").Returns(new Segment[]{Segment.Triple20 ,Segment.Triple20,Segment.Triple18}),
        //new TestCaseData (171,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,171,1,Throw,arrange,list,→,T19,T19,T19,").Returns(new Segment[]{Segment.Triple19 ,Segment.Triple19,Segment.Triple19}),
        //new TestCaseData (170,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,170,1,Throw,arrange,list,→,T20,T20,BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.BullOut}),
        //new TestCaseData (168,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,168,1,Throw,arrange,list,→,T20,T18,T18,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (167,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,167,1,Throw,arrange,list,→,T20,T19,BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.BullOut}),
        //new TestCaseData (165,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,165,1,Throw,arrange,list,→,T20,T19,T16,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Triple16}),
        //new TestCaseData (164,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,164,1,Throw,arrange,list,→,T20,T18,BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.BullOut}),
        //new TestCaseData (162,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,162,1,Throw,arrange,list,→,T18,T18,T18,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (161,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,161,1,Throw,arrange,list,→,T20,T17,BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.BullOut}),
        //new TestCaseData (160,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,160,1,Throw,arrange,list,→,BullOut,BullOut,T20,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple20}),
        //new TestCaseData (159,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,159,1,Throw,arrange,list,→,T20,T19,T14,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Triple14}),
        //new TestCaseData (158,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,158,1,Throw,arrange,list,→,BullOut,T18,T18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (157,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,157,1,Throw,arrange,list,→,BullOut,BullOut,T19,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple19}),
        //new TestCaseData (156,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,156,1,Throw,arrange,list,→,T20,T20,D18,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Double18}),
        //new TestCaseData (155,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,155,1,Throw,arrange,list,→,BullOut,T20,T15,").Returns(new Segment[]{Segment.BullOut,Segment.Triple20,Segment.Triple15}),
        //new TestCaseData (154,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,154,1,Throw,arrange,list,→,BullOut,BullOut,T18,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple18}),
        //new TestCaseData (153,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,153,1,Throw,arrange,list,→,T17,T17,T17,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Triple17}),
        //new TestCaseData (152,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,152,1,Throw,arrange,list,→,BullOut,T17,T17,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Triple17}),
        //new TestCaseData (151,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,151,1,Throw,arrange,list,→,BullOut,BullOut,T17,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple17}),
        //new TestCaseData (150,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,150,1,Throw,arrange,list,→,BullOut,BullOut,BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.BullOut}),
        //new TestCaseData (149,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,149,1,Throw,arrange,list,→,BullOut,T17,T16,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Triple16}),
        //new TestCaseData (148,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,148,1,Throw,arrange,list,→,BullOut,BullOut,T16,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple16}),
        //new TestCaseData (147,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,147,1,Throw,arrange,list,→,BullOut,T19,D20,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double20}),
        //new TestCaseData (146,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,146,1,Throw,arrange,list,→,BullOut,T20,D18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple20,Segment.Double18}),
        //new TestCaseData (145,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,145,1,Throw,arrange,list,→,BullOut,BullOut,T15,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple15}),
        //new TestCaseData (144,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,144,1,Throw,arrange,list,→,T16,T16,T16,").Returns(new Segment[]{Segment.Triple16,Segment.Triple16,Segment.Triple16}),
        //new TestCaseData (143,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,143,1,Throw,arrange,list,→,BullOut,T19,D18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double18}),
        //new TestCaseData (142,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,142,1,Throw,arrange,list,→,BullOut,BullOut,T14,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple14}),
        //new TestCaseData (141,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,141,1,Throw,arrange,list,→,BullOut,T17,D20,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double20}),
        //new TestCaseData (140,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,140,1,Throw,arrange,list,→,BullOut,BullOut,D20,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double20}),
        //new TestCaseData (139,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,139,1,Throw,arrange,list,→,BullOut,BullOut,T13,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple13}),
        //new TestCaseData (138,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,138,1,Throw,arrange,list,→,BullOut,BullOut,D19,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double19}),
        //new TestCaseData (137,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,137,1,Throw,arrange,list,→,BullOut,T17,D18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double18}),
        //new TestCaseData (136,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,136,1,Throw,arrange,list,→,BullOut,BullOut,D18,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double18}),
        //new TestCaseData (135,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,135,1,Throw,arrange,list,→,T15,T15,T15,").Returns(new Segment[]{Segment.Triple15,Segment.Triple15,Segment.Triple15}),
        //new TestCaseData (134,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,134,1,Throw,arrange,list,→,BullOut,BullOut,D17,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double17}),
        //new TestCaseData (133,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,133,1,Throw,arrange,list,→,BullOut,BullOut,T11,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple11}),
        //new TestCaseData (132,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,132,1,Throw,arrange,list,→,BullOut,BullOut,D16,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double16}),
        //new TestCaseData (131,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,131,1,Throw,arrange,list,→,BullOut,T19,D12,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double12}),
        //new TestCaseData (130,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,130,1,Throw,arrange,list,→,BullOut,BullOut,D15,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double15}),
        //new TestCaseData (129,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,129,1,Throw,arrange,list,→,BullOut,T19,D11,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double11}),
        //new TestCaseData (128,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,128,1,Throw,arrange,list,→,BullOut,BullOut,D14,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double14}),
        //new TestCaseData (127,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,127,1,Throw,arrange,list,→,BullOut,T19,D10,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double10}),
        //new TestCaseData (126,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,126,1,Throw,arrange,list,→,BullOut,BullOut,D13,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double13}),
        //new TestCaseData (125,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,125,1,Throw,arrange,list,→,BullOut,T17,D12,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double12}),
        //new TestCaseData (124,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,124,1,Throw,arrange,list,→,BullOut,BullOut,D12,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double12}),
        //new TestCaseData (123,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,123,1,Throw,arrange,list,→,BullOut,T19,D8,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double8}),
        //new TestCaseData (122,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,122,1,Throw,arrange,list,→,BullOut,BullOut,D11,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double11}),
        //new TestCaseData (121,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,121,1,Throw,arrange,list,→,BullOut,BullOut,T7,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple7}),
        //new TestCaseData (120,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,120,1,Throw,arrange,list,→,T20,T20,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Miss}),
        //new TestCaseData (119,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,119,1,Throw,arrange,list,→,BullOut,BullOut,S19_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single19Out}),
        //new TestCaseData (118,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,118,1,Throw,arrange,list,→,BullOut,BullOut,S18_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single18Out}),
        //new TestCaseData (117,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,117,1,Throw,arrange,list,→,T20,T19,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (116,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,116,1,Throw,arrange,list,→,BullOut,BullOut,S16_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single16Out}),
        //new TestCaseData (115,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,115,1,Throw,arrange,list,→,BullOut,BullOut,S15_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single15Out}),
        //new TestCaseData (114,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,114,1,Throw,arrange,list,→,T20,T18,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (113,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,113,1,Throw,arrange,list,→,BullOut,BullOut,S13_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single13Out}),
        //new TestCaseData (112,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,112,1,Throw,arrange,list,→,BullOut,BullOut,S12_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single12Out}),
        //new TestCaseData (111,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,111,1,Throw,arrange,list,→,T20,T17,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (110,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,110,1,Throw,arrange,list,→,T20,BullOut,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (109,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,109,1,Throw,arrange,list,→,BullOut,BullOut,S9_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single9Out}),
        //new TestCaseData (108,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,108,1,Throw,arrange,list,→,T18,T18,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (107,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,107,1,Throw,arrange,list,→,BullOut,T19,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (106,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,106,1,Throw,arrange,list,→,BullOut,BullOut,S6_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single6Out}),
        //new TestCaseData (105,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,105,1,Throw,arrange,list,→,T19,T16,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (104,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,104,1,Throw,arrange,list,→,BullOut,T18,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (103,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,103,1,Throw,arrange,list,→,BullOut,BullOut,S3_Out,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Single3Out}),
        //new TestCaseData (102,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,102,1,Throw,arrange,list,→,T17,T17,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (101,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,101,1,Throw,arrange,list,→,BullOut,T17,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (100,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,100,1,Throw,arrange,list,→,BullOut,BullOut,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (99,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,99,1,Throw,arrange,list,→,T19,T14,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (98,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,98,1,Throw,arrange,list,→,BullOut,T16,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (97,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,97,1,Throw,arrange,list,→,T19,D20,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double20,Segment.Miss}),
        //new TestCaseData (96,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,96,1,Throw,arrange,list,→,T20,D18,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Double18,Segment.Miss}),
        //new TestCaseData (95,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,95,1,Throw,arrange,list,→,BullOut,T15,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple15,Segment.Miss}),
        //new TestCaseData (94,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,94,1,Throw,arrange,list,→,T18,D20,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Double20,Segment.Miss}),
        //new TestCaseData (93,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,93,1,Throw,arrange,list,→,T19,D18,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double18,Segment.Miss}),
        //new TestCaseData (92,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,92,1,Throw,arrange,list,→,BullOut,T14,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (91,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,91,1,Throw,arrange,list,→,T17,D20,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double20,Segment.Miss}),
        //new TestCaseData (90,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,90,1,Throw,arrange,list,→,BullOut,D20,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double20,Segment.Miss}),
        //new TestCaseData (89,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,89,1,Throw,arrange,list,→,T19,D16,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double16,Segment.Miss}),
        //new TestCaseData (88,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,88,1,Throw,arrange,list,→,BullOut,D19,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double19,Segment.Miss}),
        //new TestCaseData (87,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,87,1,Throw,arrange,list,→,T17,D18,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double18,Segment.Miss}),
        //new TestCaseData (86,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,86,1,Throw,arrange,list,→,BullOut,D18,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double18,Segment.Miss}),
        //new TestCaseData (85,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,85,1,Throw,arrange,list,→,T19,D14,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double14,Segment.Miss}),
        //new TestCaseData (84,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,84,1,Throw,arrange,list,→,BullOut,D17,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double17,Segment.Miss}),
        //new TestCaseData (83,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,83,1,Throw,arrange,list,→,T17,D16,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double16,Segment.Miss}),
        //new TestCaseData (82,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,82,1,Throw,arrange,list,→,BullOut,D16,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double16,Segment.Miss}),
        //new TestCaseData (81,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,81,1,Throw,arrange,list,→,T19,D12,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double12,Segment.Miss}),
        //new TestCaseData (80,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,80,1,Throw,arrange,list,→,BullOut,D15,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double15,Segment.Miss}),
        //new TestCaseData (79,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,79,1,Throw,arrange,list,→,T19,D11,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double11,Segment.Miss}),
        //new TestCaseData (78,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,78,1,Throw,arrange,list,→,BullOut,D14,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double14,Segment.Miss}),
        //new TestCaseData (77,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,77,1,Throw,arrange,list,→,BullOut,T9,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple9,Segment.Miss}),
        //new TestCaseData (76,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,76,1,Throw,arrange,list,→,BullOut,D13,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double13,Segment.Miss}),
        //new TestCaseData (75,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,75,1,Throw,arrange,list,→,T17,D12,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double12,Segment.Miss}),
        //new TestCaseData (74,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,74,1,Throw,arrange,list,→,BullOut,D12,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double12,Segment.Miss}),
        //new TestCaseData (73,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,73,1,Throw,arrange,list,→,T19,D8,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double8,Segment.Miss}),
        //new TestCaseData (72,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,72,1,Throw,arrange,list,→,BullOut,D11,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double11,Segment.Miss}),
        //new TestCaseData (71,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,71,1,Throw,arrange,list,→,BullOut,T7,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple7,Segment.Miss}),
        //new TestCaseData (70,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,70,1,Throw,arrange,list,→,BullOut,S20_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single20Out,Segment.Miss}),
        //new TestCaseData (69,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,69,1,Throw,arrange,list,→,BullOut,S19_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single19Out,Segment.Miss}),
        //new TestCaseData (68,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,68,1,Throw,arrange,list,→,BullOut,S18_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single18Out,Segment.Miss}),
        //new TestCaseData (67,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,67,1,Throw,arrange,list,→,BullOut,S17_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single17Out,Segment.Miss}),
        //new TestCaseData (66,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,66,1,Throw,arrange,list,→,BullOut,S16_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single16Out,Segment.Miss}),
        //new TestCaseData (65,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,65,1,Throw,arrange,list,→,BullOut,S15_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single15Out,Segment.Miss}),
        //new TestCaseData (64,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,64,1,Throw,arrange,list,→,BullOut,S14_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single14Out,Segment.Miss}),
        //new TestCaseData (63,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,63,1,Throw,arrange,list,→,BullOut,S13_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single13Out,Segment.Miss}),
        //new TestCaseData (62,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,62,1,Throw,arrange,list,→,BullOut,S12_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single12Out,Segment.Miss}),
        //new TestCaseData (61,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,61,1,Throw,arrange,list,→,BullOut,S11_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single11Out,Segment.Miss}),
        //new TestCaseData (60,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,60,1,Throw,arrange,list,→,T20,Miss,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (59,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,59,1,Throw,arrange,list,→,BullOut,S9_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (58,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,58,1,Throw,arrange,list,→,BullOut,S8_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single8Out,Segment.Miss}),
        //new TestCaseData (57,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,57,1,Throw,arrange,list,→,T19,Miss,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (56,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,56,1,Throw,arrange,list,→,BullOut,S6_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single6Out,Segment.Miss}),
        //new TestCaseData (55,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,55,1,Throw,arrange,list,→,BullOut,S5_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single5Out,Segment.Miss}),
        //new TestCaseData (54,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,54,1,Throw,arrange,list,→,T18,Miss,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (53,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,53,1,Throw,arrange,list,→,BullOut,S3_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (52,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,52,1,Throw,arrange,list,→,BullOut,S2_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single2Out,Segment.Miss}),
        //new TestCaseData (51,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,51,1,Throw,arrange,list,→,T17,Miss,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,50,1,Throw,arrange,list,→,BullOut,Miss,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (49,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,49,1,Throw,arrange,list,→,D20,S9_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (48,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,48,1,Throw,arrange,list,→,T16,Miss,Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (47,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,47,1,Throw,arrange,list,→,D20,S7_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single7Out,Segment.Miss}),
        //new TestCaseData (46,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,46,1,Throw,arrange,list,→,D20,S6_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single6Out,Segment.Miss}),
        //new TestCaseData (45,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,45,1,Throw,arrange,list,→,T15,Miss,Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (44,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,44,1,Throw,arrange,list,→,D20,S4_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single4Out,Segment.Miss}),
        //new TestCaseData (43,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,43,1,Throw,arrange,list,→,D20,S3_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (42,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,42,1,Throw,arrange,list,→,T14,Miss,Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (41,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,41,1,Throw,arrange,list,→,D20,S1_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single1Out,Segment.Miss}),
        //new TestCaseData (40,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,40,1,Throw,arrange,list,→,D20,Miss,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,39,1,Throw,arrange,list,→,T13,Miss,Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,38,1,Throw,arrange,list,→,D19,Miss,Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (37,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,37,1,Throw,arrange,list,→,S20_Out,S17_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single17Out,Segment.Miss}),
        //new TestCaseData (36,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,36,1,Throw,arrange,list,→,D18,Miss,Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (35,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,35,1,Throw,arrange,list,→,S20_Out,S15_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single15Out,Segment.Miss}),
        //new TestCaseData (34,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,34,1,Throw,arrange,list,→,D17,Miss,Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,33,1,Throw,arrange,list,→,T11,Miss,Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,32,1,Throw,arrange,list,→,D16,Miss,Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (31,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,31,1,Throw,arrange,list,→,S20_Out,S11_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single11Out,Segment.Miss}),
        //new TestCaseData (30,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,30,1,Throw,arrange,list,→,D15,Miss,Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (29,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,29,1,Throw,arrange,list,→,S20_Out,S9_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (28,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,28,1,Throw,arrange,list,→,D14,Miss,Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,27,1,Throw,arrange,list,→,T9,Miss,Miss,").Returns(new Segment[]{Segment.Triple9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (26,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,26,1,Throw,arrange,list,→,D13,Miss,Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (25,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,25,1,Throw,arrange,list,→,S20_Out,S5_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single5Out,Segment.Miss}),
        //new TestCaseData (24,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,24,1,Throw,arrange,list,→,D12,Miss,Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (23,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,23,1,Throw,arrange,list,→,S20_Out,S3_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (22,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,22,1,Throw,arrange,list,→,D11,Miss,Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,21,1,Throw,arrange,list,→,T7,Miss,Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,20,1,Throw,arrange,list,→,S20_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (19,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,19,1,Throw,arrange,list,→,S19_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single19Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (18,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,18,1,Throw,arrange,list,→,S18_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single18Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (17,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,17,1,Throw,arrange,list,→,S17_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (16,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,16,1,Throw,arrange,list,→,S16_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single16Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,15,1,Throw,arrange,list,→,S15_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,14,1,Throw,arrange,list,→,S14_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (13,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,13,1,Throw,arrange,list,→,S13_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (12,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,12,1,Throw,arrange,list,→,S12_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (11,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,11,1,Throw,arrange,list,→,S11_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (10,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,10,1,Throw,arrange,list,→,S10_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single10Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,9,1,Throw,arrange,list,→,S9_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,8,1,Throw,arrange,list,→,S8_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single8Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (7,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,7,1,Throw,arrange,list,→,S7_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single7Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (6,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,6,1,Throw,arrange,list,→,S6_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single6Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (5,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,5,1,Throw,arrange,list,→,S5_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (4,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,4,1,Throw,arrange,list,→,S4_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single4Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,3,1,Throw,arrange,list,→,S3_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,2,1,Throw,arrange,list,→,S2_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single2Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (1,1,GameEndConditions.None,GameOptions.None).SetName("OpenOut,1,1,Throw,arrange,list,→,S1_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (120,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,120,2,Throw,arrange,list,→,T20,T20,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Miss}),
        //new TestCaseData (117,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,117,2,Throw,arrange,list,→,T20,T19,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (114,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,114,2,Throw,arrange,list,→,T20,T18,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (111,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,111,2,Throw,arrange,list,→,T20,T17,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (110,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,110,2,Throw,arrange,list,→,T20,BullOut,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (108,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,108,2,Throw,arrange,list,→,T18,T18,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (107,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,107,2,Throw,arrange,list,→,BullOut,T19,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (104,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,104,2,Throw,arrange,list,→,BullOut,T18,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (102,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,102,2,Throw,arrange,list,→,T17,T17,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (101,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,101,2,Throw,arrange,list,→,BullOut,T17,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (100,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,100,2,Throw,arrange,list,→,BullOut,BullOut,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (99,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,99,2,Throw,arrange,list,→,T19,T14,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (98,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,98,2,Throw,arrange,list,→,BullOut,T16,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (97,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,97,2,Throw,arrange,list,→,T19,D20,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double20,Segment.Miss}),
        //new TestCaseData (96,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,96,2,Throw,arrange,list,→,T20,D18,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Double18,Segment.Miss}),
        //new TestCaseData (95,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,95,2,Throw,arrange,list,→,BullOut,T15,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple15,Segment.Miss}),
        //new TestCaseData (94,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,94,2,Throw,arrange,list,→,T18,D20,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Double20,Segment.Miss}),
        //new TestCaseData (93,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,93,2,Throw,arrange,list,→,T19,D18,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double18,Segment.Miss}),
        //new TestCaseData (92,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,92,2,Throw,arrange,list,→,BullOut,T14,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (91,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,91,2,Throw,arrange,list,→,T17,D20,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double20,Segment.Miss}),
        //new TestCaseData (90,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,90,2,Throw,arrange,list,→,BullOut,D20,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double20,Segment.Miss}),
        //new TestCaseData (89,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,89,2,Throw,arrange,list,→,T19,D16,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double16,Segment.Miss}),
        //new TestCaseData (88,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,88,2,Throw,arrange,list,→,BullOut,D19,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double19,Segment.Miss}),
        //new TestCaseData (87,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,87,2,Throw,arrange,list,→,T17,D18,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double18,Segment.Miss}),
        //new TestCaseData (86,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,86,2,Throw,arrange,list,→,BullOut,D18,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double18,Segment.Miss}),
        //new TestCaseData (85,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,85,2,Throw,arrange,list,→,T19,D14,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double14,Segment.Miss}),
        //new TestCaseData (84,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,84,2,Throw,arrange,list,→,BullOut,D17,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double17,Segment.Miss}),
        //new TestCaseData (83,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,83,2,Throw,arrange,list,→,T17,D16,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double16,Segment.Miss}),
        //new TestCaseData (82,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,82,2,Throw,arrange,list,→,BullOut,D16,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double16,Segment.Miss}),
        //new TestCaseData (81,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,81,2,Throw,arrange,list,→,T19,D12,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double12,Segment.Miss}),
        //new TestCaseData (80,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,80,2,Throw,arrange,list,→,BullOut,D15,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double15,Segment.Miss}),
        //new TestCaseData (79,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,79,2,Throw,arrange,list,→,T19,D11,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double11,Segment.Miss}),
        //new TestCaseData (78,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,78,2,Throw,arrange,list,→,BullOut,D14,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double14,Segment.Miss}),
        //new TestCaseData (77,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,77,2,Throw,arrange,list,→,BullOut,T9,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple9,Segment.Miss}),
        //new TestCaseData (76,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,76,2,Throw,arrange,list,→,BullOut,D13,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double13,Segment.Miss}),
        //new TestCaseData (75,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,75,2,Throw,arrange,list,→,T17,D12,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double12,Segment.Miss}),
        //new TestCaseData (74,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,74,2,Throw,arrange,list,→,BullOut,D12,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double12,Segment.Miss}),
        //new TestCaseData (73,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,73,2,Throw,arrange,list,→,T19,D8,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double8,Segment.Miss}),
        //new TestCaseData (72,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,72,2,Throw,arrange,list,→,BullOut,D11,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double11,Segment.Miss}),
        //new TestCaseData (71,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,71,2,Throw,arrange,list,→,BullOut,T7,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple7,Segment.Miss}),
        //new TestCaseData (70,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,70,2,Throw,arrange,list,→,BullOut,S20_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single20Out,Segment.Miss}),
        //new TestCaseData (69,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,69,2,Throw,arrange,list,→,BullOut,S19_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single19Out,Segment.Miss}),
        //new TestCaseData (68,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,68,2,Throw,arrange,list,→,BullOut,S18_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single18Out,Segment.Miss}),
        //new TestCaseData (67,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,67,2,Throw,arrange,list,→,BullOut,S17_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single17Out,Segment.Miss}),
        //new TestCaseData (66,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,66,2,Throw,arrange,list,→,BullOut,S16_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single16Out,Segment.Miss}),
        //new TestCaseData (65,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,65,2,Throw,arrange,list,→,BullOut,S15_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single15Out,Segment.Miss}),
        //new TestCaseData (64,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,64,2,Throw,arrange,list,→,BullOut,S14_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single14Out,Segment.Miss}),
        //new TestCaseData (63,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,63,2,Throw,arrange,list,→,BullOut,S13_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single13Out,Segment.Miss}),
        //new TestCaseData (62,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,62,2,Throw,arrange,list,→,BullOut,S12_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single12Out,Segment.Miss}),
        //new TestCaseData (61,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,61,2,Throw,arrange,list,→,BullOut,S11_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single11Out,Segment.Miss}),
        //new TestCaseData (60,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,60,2,Throw,arrange,list,→,T20,Miss,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (59,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,59,2,Throw,arrange,list,→,BullOut,S9_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (58,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,58,2,Throw,arrange,list,→,BullOut,S8_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single8Out,Segment.Miss}),
        //new TestCaseData (57,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,57,2,Throw,arrange,list,→,T19,Miss,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (56,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,56,2,Throw,arrange,list,→,BullOut,S6_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single6Out,Segment.Miss}),
        //new TestCaseData (55,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,55,2,Throw,arrange,list,→,BullOut,S5_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single5Out,Segment.Miss}),
        //new TestCaseData (54,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,54,2,Throw,arrange,list,→,T18,Miss,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (53,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,53,2,Throw,arrange,list,→,BullOut,S3_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (52,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,52,2,Throw,arrange,list,→,BullOut,S2_Out,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Single2Out,Segment.Miss}),
        //new TestCaseData (51,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,51,2,Throw,arrange,list,→,T17,Miss,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,50,2,Throw,arrange,list,→,BullOut,Miss,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (49,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,49,2,Throw,arrange,list,→,D20,S9_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (48,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,48,2,Throw,arrange,list,→,T16,Miss,Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (47,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,47,2,Throw,arrange,list,→,D20,S7_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single7Out,Segment.Miss}),
        //new TestCaseData (46,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,46,2,Throw,arrange,list,→,D20,S6_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single6Out,Segment.Miss}),
        //new TestCaseData (45,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,45,2,Throw,arrange,list,→,T15,Miss,Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (44,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,44,2,Throw,arrange,list,→,D20,S4_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single4Out,Segment.Miss}),
        //new TestCaseData (43,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,43,2,Throw,arrange,list,→,D20,S3_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (42,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,42,2,Throw,arrange,list,→,T14,Miss,Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (41,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,41,2,Throw,arrange,list,→,D20,S1_Out,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Single1Out,Segment.Miss}),
        //new TestCaseData (40,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,40,2,Throw,arrange,list,→,D20,Miss,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,39,2,Throw,arrange,list,→,T13,Miss,Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,38,2,Throw,arrange,list,→,D19,Miss,Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (37,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,37,2,Throw,arrange,list,→,S20_Out,S17_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single17Out,Segment.Miss}),
        //new TestCaseData (36,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,36,2,Throw,arrange,list,→,D18,Miss,Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (35,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,35,2,Throw,arrange,list,→,S20_Out,S15_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single15Out,Segment.Miss}),
        //new TestCaseData (34,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,34,2,Throw,arrange,list,→,D17,Miss,Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,33,2,Throw,arrange,list,→,T11,Miss,Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,32,2,Throw,arrange,list,→,D16,Miss,Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (31,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,31,2,Throw,arrange,list,→,S20_Out,S11_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single11Out,Segment.Miss}),
        //new TestCaseData (30,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,30,2,Throw,arrange,list,→,D15,Miss,Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (29,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,29,2,Throw,arrange,list,→,S20_Out,S9_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single9Out,Segment.Miss}),
        //new TestCaseData (28,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,28,2,Throw,arrange,list,→,D14,Miss,Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,27,2,Throw,arrange,list,→,T9,Miss,Miss,").Returns(new Segment[]{Segment.Triple9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (26,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,26,2,Throw,arrange,list,→,D13,Miss,Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (25,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,25,2,Throw,arrange,list,→,S20_Out,S5_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single5Out,Segment.Miss}),
        //new TestCaseData (24,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,24,2,Throw,arrange,list,→,D12,Miss,Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (23,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,23,2,Throw,arrange,list,→,S20_Out,S3_Out,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Single3Out,Segment.Miss}),
        //new TestCaseData (22,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,22,2,Throw,arrange,list,→,D11,Miss,Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,21,2,Throw,arrange,list,→,T7,Miss,Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,20,2,Throw,arrange,list,→,S20_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (19,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,19,2,Throw,arrange,list,→,S19_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single19Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (18,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,18,2,Throw,arrange,list,→,S18_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single18Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (17,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,17,2,Throw,arrange,list,→,S17_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (16,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,16,2,Throw,arrange,list,→,S16_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single16Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,15,2,Throw,arrange,list,→,S15_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,14,2,Throw,arrange,list,→,S14_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (13,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,13,2,Throw,arrange,list,→,S13_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (12,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,12,2,Throw,arrange,list,→,S12_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (11,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,11,2,Throw,arrange,list,→,S11_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (10,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,10,2,Throw,arrange,list,→,S10_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single10Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,9,2,Throw,arrange,list,→,S9_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,8,2,Throw,arrange,list,→,S8_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single8Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (7,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,7,2,Throw,arrange,list,→,S7_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single7Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (6,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,6,2,Throw,arrange,list,→,S6_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single6Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (5,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,5,2,Throw,arrange,list,→,S5_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (4,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,4,2,Throw,arrange,list,→,S4_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single4Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,3,2,Throw,arrange,list,→,S3_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,2,2,Throw,arrange,list,→,S2_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single2Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (1,2,GameEndConditions.None,GameOptions.None).SetName("OpenOut,1,2,Throw,arrange,list,→,S1_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (60,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,60,3,Throw,arrange,list,→,T20,Miss,Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (57,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,57,3,Throw,arrange,list,→,T19,Miss,Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (54,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,54,3,Throw,arrange,list,→,T18,Miss,Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (51,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,51,3,Throw,arrange,list,→,T17,Miss,Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,50,3,Throw,arrange,list,→,BullOut,Miss,Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (48,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,48,3,Throw,arrange,list,→,T16,Miss,Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (45,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,45,3,Throw,arrange,list,→,T15,Miss,Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (42,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,42,3,Throw,arrange,list,→,T14,Miss,Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (40,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,40,3,Throw,arrange,list,→,D20,Miss,Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,39,3,Throw,arrange,list,→,T13,Miss,Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,38,3,Throw,arrange,list,→,D19,Miss,Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (36,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,36,3,Throw,arrange,list,→,D18,Miss,Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (34,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,34,3,Throw,arrange,list,→,D17,Miss,Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,33,3,Throw,arrange,list,→,T11,Miss,Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,32,3,Throw,arrange,list,→,D16,Miss,Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (30,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,30,3,Throw,arrange,list,→,D15,Miss,Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (28,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,28,3,Throw,arrange,list,→,D14,Miss,Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,27,3,Throw,arrange,list,→,T9,Miss,Miss,").Returns(new Segment[]{Segment.Triple9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (26,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,26,3,Throw,arrange,list,→,D13,Miss,Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (24,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,24,3,Throw,arrange,list,→,D12,Miss,Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (22,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,22,3,Throw,arrange,list,→,D11,Miss,Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,21,3,Throw,arrange,list,→,T7,Miss,Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,20,3,Throw,arrange,list,→,S20_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (19,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,19,3,Throw,arrange,list,→,S19_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single19Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (18,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,18,3,Throw,arrange,list,→,S18_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single18Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (17,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,17,3,Throw,arrange,list,→,S17_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (16,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,16,3,Throw,arrange,list,→,S16_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single16Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,15,3,Throw,arrange,list,→,S15_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,14,3,Throw,arrange,list,→,S14_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (13,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,13,3,Throw,arrange,list,→,S13_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (12,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,12,3,Throw,arrange,list,→,S12_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (11,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,11,3,Throw,arrange,list,→,S11_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (10,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,10,3,Throw,arrange,list,→,S10_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single10Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,9,3,Throw,arrange,list,→,S9_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,8,3,Throw,arrange,list,→,S8_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single8Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (7,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,7,3,Throw,arrange,list,→,S7_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single7Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (6,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,6,3,Throw,arrange,list,→,S6_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single6Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (5,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,5,3,Throw,arrange,list,→,S5_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (4,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,4,3,Throw,arrange,list,→,S4_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single4Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,3,3,Throw,arrange,list,→,S3_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,2,3,Throw,arrange,list,→,S2_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single2Out,Segment.Miss,Segment.Miss}),
        //new TestCaseData (1,3,GameEndConditions.None,GameOptions.None).SetName("OpenOut,1,3,Throw,arrange,list,→,S1_Out,Miss,Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Miss,Segment.Miss}),

        //new TestCaseData (180,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single18Out0,1,Segment.Triple20,Segment.Triple20,Segment.Triple20,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Triple20}),
        //new TestCaseData (177,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out7,1,Segment.Triple20,Segment.Triple20,Segment.Triple19,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Triple19}),
        //new TestCaseData (174,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out4,1,Segment.Triple20,Segment.Triple20,Segment.Triple18,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Triple18}),
        //new TestCaseData (171,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out1,1,Segment.Triple19,Segment.Triple19,Segment.Triple19,").Returns(new Segment[]{Segment.Triple19,Segment.Triple19,Segment.Triple19}),
        //new TestCaseData (170,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out0,1,Segment.Triple20,Segment.Triple20,Segment.BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.BullOut}),
        //new TestCaseData (168,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out8,1,Segment.Triple20,Segment.Triple18,Segment.Triple18,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (167,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.7,1,Segment.Triple20,Segment.Triple19,Segment.BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.BullOut}),
        //new TestCaseData (165,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.16Out5,1,Segment.Triple20,Segment.Triple19,Segment.Triple16,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Triple16}),
        //new TestCaseData (164,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.164,1,Segment.Triple20,Segment.Triple18,Segment.BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.BullOut}),
        //new TestCaseData (162,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out2,1,Segment.Triple18,Segment.T{riple18,Segment.Triple18,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (161,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out1,1,Segment.Triple20,Segment.Triple17,Segment.BullOut,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.BullOut}),
        //new TestCaseData (160,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out0,1,Segment.BullOut,Segment.BullOut,Segment.Triple20,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple20}),
        //new TestCaseData (159,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,159,1,Segment.Triple20,Segment.Triple19,Segment.Triple14,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Triple14}),
        //new TestCaseData (158,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,158,1,Segment.BullOut,Segment.Triple18,Segment.Triple18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Triple18}),
        //new TestCaseData (157,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,157,1,Segment.BullOut,Segment.BullOut,Segment.Triple19,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple19}),
        //new TestCaseData (156,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,156,1,Segment.Triple20,Segment.Triple20,Segment.Double18,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Double18}),
        //new TestCaseData (155,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,155,1,Segment.BullOut,Segment.Triple20,Segment.Triple15,").Returns(new Segment[]{Segment.BullOut,Segment.Triple20,Segment.Triple15}),
        //new TestCaseData (154,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,154,1,Segment.BullOut,Segment.BullOut,Segment.Triple18,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple18}),
        //new TestCaseData (153,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,153,1,Segment.Triple17,Segment.Triple17,Segment.Triple17,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Triple17}),
        //new TestCaseData (152,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,152,1,Segment.BullOut,Segment.Triple17,Segment.Triple17,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Triple17}),
        //new TestCaseData (151,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,151,1,Segment.BullOut,Segment.BullOut,Segment.Triple17,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple17}),
        //new TestCaseData (150,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,150,1,Segment.BullOut,Segment.BullOut,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.BullOut}),
        //new TestCaseData (149,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,149,1,Segment.BullOut,Segment.Triple17,Segment.Triple16,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Triple16}),
        //new TestCaseData (148,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,148,1,Segment.BullOut,Segment.BullOut,Segment.Triple16,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple16}),
        //new TestCaseData (147,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,147,1,Segment.BullOut,Segment.Triple19,Segment.Double20,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double20}),
        //new TestCaseData (146,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,146,1,Segment.BullOut,Segment.Triple20,Segment.Double18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple20,Segment.Double18}),
        //new TestCaseData (145,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,145,1,Segment.BullOut,Segment.BullOut,Segment.Triple15,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple15}),
        //new TestCaseData (144,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,144,1,Segment.Triple16,Segment.Triple16,Segment.Triple16,").Returns(new Segment[]{Segment.Triple16,Segment.Triple16,Segment.Triple16}),
        //new TestCaseData (143,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,143,1,Segment.BullOut,Segment.Triple19,Segment.Double18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double18}),
        //new TestCaseData (142,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,142,1,Segment.BullOut,Segment.BullOut,Segment.Triple14,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple14}),
        //new TestCaseData (141,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,141,1,Segment.BullOut,Segment.Triple17,Segment.Double20,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double20}),
        //new TestCaseData (140,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,140,1,Segment.BullOut,Segment.BullOut,Segment.Double20,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double20}),
        //new TestCaseData (139,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,139,1,Segment.BullOut,Segment.BullOut,Segment.Triple13,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple13}),
        //new TestCaseData (138,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,138,1,Segment.BullOut,Segment.BullOut,Segment.Double19,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double19}),
        //new TestCaseData (137,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,137,1,Segment.BullOut,Segment.Triple17,Segment.Double18,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double18}),
        //new TestCaseData (136,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,136,1,Segment.BullOut,Segment.BullOut,Segment.Double18,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double18}),
        //new TestCaseData (135,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,135,1,Segment.Triple15,Segment.Triple15,Segment.Triple15,").Returns(new Segment[]{Segment.Triple15,Segment.Triple15,Segment.Triple15}),
        //new TestCaseData (134,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,134,1,Segment.BullOut,Segment.BullOut,Segment.Double17,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double17}),
        //new TestCaseData (133,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,133,1,Segment.BullOut,Segment.BullOut,Segment.Triple11,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple11}),
        //new TestCaseData (132,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,132,1,Segment.BullOut,Segment.BullOut,Segment.Double16,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double16}),
        //new TestCaseData (131,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,131,1,Segment.BullOut,Segment.Triple19,Segment.Double12,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double12}),
        //new TestCaseData (130,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,130,1,Segment.BullOut,Segment.BullOut,Segment.Double15,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double15}),
        //new TestCaseData (129,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,129,1,Segment.BullOut,Segment.Triple19,Segment.Double11,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double11}),
        //new TestCaseData (128,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,128,1,Segment.BullOut,Segment.BullOut,Segment.Double14,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double14}),
        //new TestCaseData (127,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,127,1,Segment.BullOut,Segment.Triple19,Segment.Double10,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double10}),
        //new TestCaseData (126,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,126,1,Segment.BullOut,Segment.BullOut,Segment.Double13,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double13}),
        //new TestCaseData (125,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,125,1,Segment.BullOut,Segment.Triple17,Segment.Double12,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Double12}),
        //new TestCaseData (124,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,124,1,Segment.BullOut,Segment.BullOut,Segment.Double12,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double12}),
        //new TestCaseData (123,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,123,1,Segment.BullOut,Segment.Triple19,Segment.Double8,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Double8}),
        //new TestCaseData (122,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,122,1,Segment.BullOut,Segment.BullOut,Segment.Double11,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Double11}),
        //new TestCaseData (121,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,121,1,Segment.BullOut,Segment.BullOut,Segment.Triple7,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Triple7}),
        //new TestCaseData (120,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,120,1,Segment.Triple20,Segment.Triple20,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Miss}),
        //new TestCaseData (119,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,119,1,Segment.BullOut,Segment.Single19Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single19Out,Segment.BullOut}),
        //new TestCaseData (118,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,118,1,Segment.BullOut,Segment.Single18Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single18Out,Segment.BullOut}),
        //new TestCaseData (117,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,117,1,Segment.Triple20,Segment.Triple19,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (116,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,116,1,Segment.BullOut,Segment.Single16Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single16Out,Segment.BullOut}),
        //new TestCaseData (115,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,115,1,Segment.BullOut,Segment.Single15Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single15Out,Segment.BullOut}),
        //new TestCaseData (114,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,114,1,Segment.Triple20,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (113,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,113,1,Segment.BullOut,Segment.Single13Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single13Out,Segment.BullOut}),
        //new TestCaseData (112,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,112,1,Segment.BullOut,Segment.Single12Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single12Out,Segment.BullOut}),
        //new TestCaseData (111,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,111,1,Segment.Triple20,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (110,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,110,1,Segment.Triple20,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (109,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,109,1,Segment.BullOut,Segment.Single9Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single9Out,Segment.BullOut}),
        //new TestCaseData (108,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,108,1,Segment.Triple18,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (107,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,107,1,Segment.BullOut,Segment.Triple19,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (106,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,106,1,Segment.BullOut,Segment.Single6Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single6Out,Segment.BullOut}),
        //new TestCaseData (105,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,105,1,Segment.Triple19,Segment.Triple16,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (104,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,104,1,Segment.BullOut,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (103,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,103,1,Segment.BullOut,Segment.Single3Out,Segment.BullOut,").Returns(new Segment[]{Segment.BullOut,Segment.Single3Out,Segment.BullOut}),
        //new TestCaseData (102,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,102,1,Segment.Triple17,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (101,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,101,1,Segment.BullOut,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (100,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,100,1,Segment.BullOut,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (99,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,99,1,Segment.Triple19,Segment.Triple14,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (98,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,98,1,Segment.BullOut,Segment.Triple16,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (97,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,97,1,Segment.Triple19,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double20,Segment.Miss}),
        //new TestCaseData (96,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,96,1,Segment.Triple20,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Double18,Segment.Miss}),
        //new TestCaseData (95,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,95,1,Segment.BullOut,Segment.Triple15,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple15,Segment.Miss}),
        //new TestCaseData (94,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,94,1,Segment.Triple18,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Double20,Segment.Miss}),
        //new TestCaseData (93,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,93,1,Segment.Triple19,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double18,Segment.Miss}),
        //new TestCaseData (92,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,92,1,Segment.BullOut,Segment.Triple14,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (91,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,91,1,Segment.Triple17,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double20,Segment.Miss}),
        //new TestCaseData (90,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,90,1,Segment.BullOut,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double20,Segment.Miss}),
        //new TestCaseData (89,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,89,1,Segment.Triple19,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double16,Segment.Miss}),
        //new TestCaseData (88,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,88,1,Segment.BullOut,Segment.Double19,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double19,Segment.Miss}),
        //new TestCaseData (87,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,87,1,Segment.Triple17,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double18,Segment.Miss}),
        //new TestCaseData (86,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,86,1,Segment.BullOut,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double18,Segment.Miss}),
        //new TestCaseData (85,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,85,1,Segment.Triple19,Segment.Double14,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double14,Segment.Miss}),
        //new TestCaseData (84,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,84,1,Segment.BullOut,Segment.Double17,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double17,Segment.Miss}),
        //new TestCaseData (83,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,83,1,Segment.Triple17,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double16,Segment.Miss}),
        //new TestCaseData (82,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,82,1,Segment.BullOut,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double16,Segment.Miss}),
        //new TestCaseData (81,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,81,1,Segment.Triple19,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double12,Segment.Miss}),
        //new TestCaseData (80,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,80,1,Segment.BullOut,Segment.Double15,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double15,Segment.Miss}),
        //new TestCaseData (79,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,79,1,Segment.Triple19,Segment.Double11,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double11,Segment.Miss}),
        //new TestCaseData (78,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,78,1,Segment.BullOut,Segment.Double14,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double14,Segment.Miss}),
        //new TestCaseData (77,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,77,1,Segment.BullOut,Segment.Triple9,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple9,Segment.Miss}),
        //new TestCaseData (76,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,76,1,Segment.BullOut,Segment.Double13,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double13,Segment.Miss}),
        //new TestCaseData (75,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,75,1,Segment.Triple17,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double12,Segment.Miss}),
        //new TestCaseData (74,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,74,1,Segment.BullOut,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double12,Segment.Miss}),
        //new TestCaseData (73,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,73,1,Segment.Triple19,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double8,Segment.Miss}),
        //new TestCaseData (72,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,72,1,Segment.BullOut,Segment.Double11,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double11,Segment.Miss}),
        //new TestCaseData (71,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,71,1,Segment.BullOut,Segment.Triple7,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple7,Segment.Miss}),
        //new TestCaseData (70,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,70,1,Segment.Single20Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (69,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,69,1,Segment.Single19Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single19Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (68,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,68,1,Segment.Single18Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single18Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (67,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,67,1,Segment.Single17Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (66,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,66,1,Segment.Single16Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single16Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (65,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,65,1,Segment.Single15Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (64,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,64,1,Segment.Single14Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (63,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,63,1,Segment.Single13Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (62,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,62,1,Segment.Single12Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (61,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,61,1,Segment.Single11Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (60,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,60,1,Segment.Triple20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (59,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,59,1,Segment.Single9Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (58,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,58,1,Segment.Single8Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single8Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (57,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,57,1,Segment.Triple19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (56,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,56,1,Segment.Single6Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single6Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (55,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,55,1,Segment.Single5Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (54,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,54,1,Segment.Triple18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (53,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,53,1,Segment.Single3Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (52,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,52,1,Segment.Single2Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single2Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (51,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,51,1,Segment.Triple17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,50,1,Segment.BullOut,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (49,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,49,1,Segment.Single17Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (48,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,48,1,Segment.Triple16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (47,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,47,1,Segment.Single15Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (46,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,46,1,Segment.Single14Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (45,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,45,1,Segment.Triple15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (44,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,44,1,Segment.Single12Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (43,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,43,1,Segment.Single11Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (42,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,42,1,Segment.Triple14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (41,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,41,1,Segment.Single9Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (40,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,40,1,Segment.Double20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,39,1,Segment.Triple13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,38,1,Segment.Double19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (37,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,37,1,Segment.Single5Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (36,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,36,1,Segment.Double18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (35,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,35,1,Segment.Single3Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (34,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,34,1,Segment.Double17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,33,1,Segment.Triple11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,32,1,Segment.Double16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (31,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,31,1,Segment.Single15Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (30,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,30,1,Segment.Double15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (29,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,29,1,Segment.Single13Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (28,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,28,1,Segment.Double14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,27,1,Segment.Single11Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (26,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,26,1,Segment.Double13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (25,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,25,1,Segment.Single9Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (24,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,24,1,Segment.Double12,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (23,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,23,1,Segment.Single7Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single7Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (22,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,22,1,Segment.Double11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,21,1,Segment.Triple7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single20Out,1,Segment.Double10,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double10,Segment.Miss,Segment.Miss}),
        //new TestCaseData (19,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single19Out,1,Segment.Single3Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (18,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single18Out,1,Segment.Double9,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (17,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out,1,Segment.Single1Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (16,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out,1,Segment.Double8,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double8,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,15,1,Segment.Triple5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,14,1,Segment.Double7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (13,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,13,1,Segment.Single5Out,Segment.Double4,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Double4,Segment.Miss}),
        //new TestCaseData (12,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,12,1,Segment.Double6,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double6,Segment.Miss,Segment.Miss}),
        //new TestCaseData (11,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,11,1,Segment.Single3Out,Segment.Double4,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double4,Segment.Miss}),
        //new TestCaseData (10,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,10,1,Segment.Double5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,9,1,Segment.Triple3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,8,1,Segment.Double4,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double4,Segment.Miss,Segment.Miss}),
        //new TestCaseData (7,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,7,1,Segment.Single3Out,Segment.Double2,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double2,Segment.Miss}),
        //new TestCaseData (6,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,6,1,Segment.Double3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (5,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,5,1,Segment.Single1Out,Segment.Double2,Segment.Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Double2,Segment.Miss}),
        //new TestCaseData (4,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,4,1,Segment.Double2,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double2,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,3,1,Segment.Triple1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple1,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,1,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,2,1,Segment.Double1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double1,Segment.Miss,Segment.Miss}),
        //new TestCaseData (120,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,120,2,Segment.Triple20,Segment.Triple20,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple20,Segment.Miss}),
        //new TestCaseData (117,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,117,2,Segment.Triple20,Segment.Triple19,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (114,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,114,2,Segment.Triple20,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (111,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,111,2,Segment.Triple20,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (110,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,110,2,Segment.Triple20,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (108,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,108,2,Segment.Triple18,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (107,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,107,2,Segment.BullOut,Segment.Triple19,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple19,Segment.Miss}),
        //new TestCaseData (105,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,105,2,Segment.Triple19,Segment.Triple16,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (104,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,104,2,Segment.BullOut,Segment.Triple18,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple18,Segment.Miss}),
        //new TestCaseData (102,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,102,2,Segment.Triple17,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (101,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,101,2,Segment.BullOut,Segment.Triple17,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple17,Segment.Miss}),
        //new TestCaseData (100,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,100,2,Segment.BullOut,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (99,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,99,2,Segment.Triple19,Segment.Triple14,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (98,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,98,2,Segment.BullOut,Segment.Triple16,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple16,Segment.Miss}),
        //new TestCaseData (97,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,97,2,Segment.Triple19,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double20,Segment.Miss}),
        //new TestCaseData (96,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,96,2,Segment.Triple20,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Double18,Segment.Miss}),
        //new TestCaseData (95,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,95,2,Segment.BullOut,Segment.Triple15,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple15,Segment.Miss}),
        //new TestCaseData (94,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,94,2,Segment.Triple18,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Double20,Segment.Miss}),
        //new TestCaseData (93,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,93,2,Segment.Triple19,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double18,Segment.Miss}),
        //new TestCaseData (92,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,92,2,Segment.BullOut,Segment.Triple14,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple14,Segment.Miss}),
        //new TestCaseData (91,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,91,2,Segment.Triple17,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double20,Segment.Miss}),
        //new TestCaseData (90,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,90,2,Segment.BullOut,Segment.Double20,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double20,Segment.Miss}),
        //new TestCaseData (89,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,89,2,Segment.Triple19,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double16,Segment.Miss}),
        //new TestCaseData (88,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,88,2,Segment.BullOut,Segment.Double19,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double19,Segment.Miss}),
        //new TestCaseData (87,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,87,2,Segment.Triple17,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double18,Segment.Miss}),
        //new TestCaseData (86,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,86,2,Segment.BullOut,Segment.Double18,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double18,Segment.Miss}),
        //new TestCaseData (85,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,85,2,Segment.Triple19,Segment.Double14,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double14,Segment.Miss}),
        //new TestCaseData (84,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,84,2,Segment.BullOut,Segment.Double17,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double17,Segment.Miss}),
        //new TestCaseData (83,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,83,2,Segment.Triple17,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double16,Segment.Miss}),
        //new TestCaseData (82,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,82,2,Segment.BullOut,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double16,Segment.Miss}),
        //new TestCaseData (81,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,81,2,Segment.Triple19,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double12,Segment.Miss}),
        //new TestCaseData (80,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,80,2,Segment.BullOut,Segment.Double15,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double15,Segment.Miss}),
        //new TestCaseData (79,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,79,2,Segment.Triple19,Segment.Double11,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double11,Segment.Miss}),
        //new TestCaseData (78,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,78,2,Segment.BullOut,Segment.Double14,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double14,Segment.Miss}),
        //new TestCaseData (77,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,77,2,Segment.BullOut,Segment.Triple9,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple9,Segment.Miss}),
        //new TestCaseData (76,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,76,2,Segment.BullOut,Segment.Double13,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double13,Segment.Miss}),
        //new TestCaseData (75,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,75,2,Segment.Triple17,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Double12,Segment.Miss}),
        //new TestCaseData (74,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,74,2,Segment.BullOut,Segment.Double12,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double12,Segment.Miss}),
        //new TestCaseData (73,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,73,2,Segment.Triple19,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Double8,Segment.Miss}),
        //new TestCaseData (72,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,72,2,Segment.BullOut,Segment.Double11,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Double11,Segment.Miss}),
        //new TestCaseData (71,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,71,2,Segment.BullOut,Segment.Triple7,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Triple7,Segment.Miss}),
        //new TestCaseData (70,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,70,2,Segment.Single20Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single20Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (69,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,69,2,Segment.Single19Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single19Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (68,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,68,2,Segment.Single18Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single18Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (67,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,67,2,Segment.Single17Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (66,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,66,2,Segment.Single16Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single16Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (65,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,65,2,Segment.Single15Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (64,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,64,2,Segment.Single14Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (63,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,63,2,Segment.Single13Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (62,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,62,2,Segment.Single12Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (61,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,61,2,Segment.Single11Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (60,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,60,2,Segment.Triple20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (59,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,59,2,Segment.Single9Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (58,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,58,2,Segment.Single8Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single8Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (57,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,57,2,Segment.Triple19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (56,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,56,2,Segment.Single6Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single6Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (55,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,55,2,Segment.Single5Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (54,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,54,2,Segment.Triple18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (53,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,53,2,Segment.Single3Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (52,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,52,2,Segment.Single2Out,Segment.BullOut,Segment.Miss,").Returns(new Segment[]{Segment.Single2Out,Segment.BullOut,Segment.Miss}),
        //new TestCaseData (51,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,51,2,Segment.Triple17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,50,2,Segment.BullOut,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (49,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,49,2,Segment.Single17Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single17Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (48,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,48,2,Segment.Triple16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (47,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,47,2,Segment.Single15Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (46,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,46,2,Segment.Single14Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single14Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (45,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,45,2,Segment.Triple15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (44,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,44,2,Segment.Single12Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single12Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (43,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,43,2,Segment.Single11Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single11Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (42,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,42,2,Segment.Triple14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (41,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,41,2,Segment.Single9Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (40,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,40,2,Segment.Double20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,39,2,Segment.Triple13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,38,2,Segment.Double19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (37,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,37,2,Segment.Single5Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (36,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,36,2,Segment.Double18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (35,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,35,2,Segment.Single3Out,Segment.Double16,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double16,Segment.Miss}),
        //new TestCaseData (34,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,34,2,Segment.Double17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,33,2,Segment.Triple11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,32,2,Segment.Double16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (31,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,31,2,Segment.Single15Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single15Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (30,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,30,2,Segment.Double15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (29,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,29,2,Segment.Single13Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single13Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (28,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,28,2,Segment.Double14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,27,2,Segment.Triple9,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (26,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,26,2,Segment.Double13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (25,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,25,2,Segment.Single9Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single9Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (24,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,24,2,Segment.Double12,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (23,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,23,2,Segment.Single7Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single7Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (22,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,22,2,Segment.Double11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,21,2,Segment.Triple7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single20Out,2,Segment.Double10,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double10,Segment.Miss,Segment.Miss}),
        //new TestCaseData (19,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single19Out,2,Segment.Single3Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (18,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single18Out,2,Segment.Double9,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (17,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single17Out,2,Segment.Single1Out,Segment.Double8,Segment.Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Double8,Segment.Miss}),
        //new TestCaseData (16,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out,2,Segment.Double8,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double8,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,15,2,Segment.Triple5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,14,2,Segment.Double7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (13,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,13,2,Segment.Single5Out,Segment.Double4,Segment.Miss,").Returns(new Segment[]{Segment.Single5Out,Segment.Double4,Segment.Miss}),
        //new TestCaseData (12,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,12,2,Segment.Double6,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double6,Segment.Miss,Segment.Miss}),
        //new TestCaseData (11,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,11,2,Segment.Single3Out,Segment.Double4,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double4,Segment.Miss}),
        //new TestCaseData (10,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,10,2,Segment.Double5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,9,2,Segment.Triple3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,8,2,Segment.Double4,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double4,Segment.Miss,Segment.Miss}),
        //new TestCaseData (7,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,7,2,Segment.Single3Out,Segment.Double2,Segment.Miss,").Returns(new Segment[]{Segment.Single3Out,Segment.Double2,Segment.Miss}),
        //new TestCaseData (6,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,6,2,Segment.Double3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (5,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,5,2,Segment.Single1Out,Segment.Double2,Segment.Miss,").Returns(new Segment[]{Segment.Single1Out,Segment.Double2,Segment.Miss}),
        //new TestCaseData (4,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,4,2,Segment.Double2,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double2,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,3,2,Segment.Triple1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple1,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,2,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,2,2,Segment.Double1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double1,Segment.Miss,Segment.Miss}),
        //new TestCaseData (60,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,60,3,Segment.Triple20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (57,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,57,3,Segment.Triple19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (54,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,54,3,Segment.Triple18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (51,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,51,3,Segment.Triple17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (50,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,50,3,Segment.BullOut,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.BullOut,Segment.Miss,Segment.Miss}),
        //new TestCaseData (48,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,48,3,Segment.Triple16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (45,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,45,3,Segment.Triple15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (42,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,42,3,Segment.Triple14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (40,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,40,3,Segment.Double20,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double20,Segment.Miss,Segment.Miss}),
        //new TestCaseData (39,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,39,3,Segment.Triple13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (38,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,38,3,Segment.Double19,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double19,Segment.Miss,Segment.Miss}),
        //new TestCaseData (36,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,36,3,Segment.Double18,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double18,Segment.Miss,Segment.Miss}),
        //new TestCaseData (34,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,34,3,Segment.Double17,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double17,Segment.Miss,Segment.Miss}),
        //new TestCaseData (33,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,33,3,Segment.Triple11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (32,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,32,3,Segment.Double16,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double16,Segment.Miss,Segment.Miss}),
        //new TestCaseData (30,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,30,3,Segment.Double15,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double15,Segment.Miss,Segment.Miss}),
        //new TestCaseData (28,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,28,3,Segment.Double14,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double14,Segment.Miss,Segment.Miss}),
        //new TestCaseData (27,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,27,3,Segment.Triple9,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (26,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,26,3,Segment.Double13,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double13,Segment.Miss,Segment.Miss}),
        //new TestCaseData (24,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,24,3,Segment.Double12,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double12,Segment.Miss,Segment.Miss}),
        //new TestCaseData (22,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,22,3,Segment.Double11,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double11,Segment.Miss,Segment.Miss}),
        //new TestCaseData (21,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,21,3,Segment.Triple7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (20,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single20Out,3,Segment.Double10,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double10,Segment.Miss,Segment.Miss}),
        //new TestCaseData (18,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single18Out,3,Segment.Double9,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double9,Segment.Miss,Segment.Miss}),
        //new TestCaseData (16,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOutSegment.Single16Out,3,Segment.Double8,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double8,Segment.Miss,Segment.Miss}),
        //new TestCaseData (15,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,15,3,Segment.Triple5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (14,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,14,3,Segment.Double7,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double7,Segment.Miss,Segment.Miss}),
        //new TestCaseData (12,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,12,3,Segment.Double6,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double6,Segment.Miss,Segment.Miss}),
        //new TestCaseData (10,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,10,3,Segment.Double5,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double5,Segment.Miss,Segment.Miss}),
        //new TestCaseData (9,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,9,3,Segment.Triple3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (8,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,8,3,Segment.Double4,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double4,Segment.Miss,Segment.Miss}),
        //new TestCaseData (6,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,6,3,Segment.Double3,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double3,Segment.Miss,Segment.Miss}),
        //new TestCaseData (4,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,4,3,Segment.Double2,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double2,Segment.Miss,Segment.Miss}),
        //new TestCaseData (3,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,3,3,Segment.Triple1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Triple1,Segment.Miss,Segment.Miss}),
        //new TestCaseData (2,3,GameEndConditions.MasterOut,GameOptions.None).SetName("MasterOut,2,3,Segment.Double1,Segment.Miss,Segment.Miss,").Returns(new Segment[]{Segment.Double1,Segment.Miss,Segment.Miss}),

        //        };
        //ArrangeのテストコードはTestRunnerで動かすとファイルの読み込みができずエラー吐いてしまう

        //[TestCaseSource(nameof(ArrangeListTestCases))]
        //public Segment[] XArrangeListTest(int currentScore, int throwCount,GameEndConditions gameEndConditions,GameOptions options) {
        //    var envDir = new DirectoryInfo(TestContext.CurrentContext.TestDirectory);
        //    var resourceDir = new DirectoryInfo(Path.Combine(envDir.Parent.Parent.FullName, "tests", "resources", "01ArrangeFiles"));
        //    var referee = Make301Ref(gameEndConditions: gameEndConditions, options: options, arrangeText: GetArrangeFileText(gameEndConditions, options == GameOptions.SeparateBull, resourceDir));

        //    return referee.Match.GetCurrentScoreArrangeList(currentScore, throwCount);

        //}
    }
}