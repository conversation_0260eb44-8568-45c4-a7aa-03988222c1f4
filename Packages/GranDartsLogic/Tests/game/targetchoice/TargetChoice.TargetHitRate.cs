using System;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.targetchoice
{
    public partial class TargetChoiceTest
    {

        public static TestCaseData[] TargetChoiceHitRateByIndex = new TestCaseData[]
        {
            //Round1にターゲットヒットした時のテスト
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple1,
                        Segment.Triple1,
                        Segment.Triple1
                    },
                    1,
                    "A",
                    30,
                    Segment.Triple1
                })
                .SetName(
                    "1人で　R1にTriple1に3ヒットした 　ゲーム終わってないので期待値0%")
                .Returns(0.0),

             //1人で　R1に一本もターゲットにヒットしなかった時のテスト
            new TestCaseData(new object[]
                {
                   new Segment[]
                    {
                        Segment.Single10In,
                        Segment.Single13In,
                        Segment.Single14In
                    },
                    1,
                    "A",
                    30,
                    Segment.Triple1
                })
                .SetName(
                    "1人で　R1に一本もTriple1にヒットしなかった 期待値 0%")
                .Returns(0.00),

            //一人で10ラウンドプレイして毎ラウンド1本Triple1にターゲット当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple1,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple1,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple1,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single20In,
                        Segment.Single20In,

                    },
                    1,
                    "A",
                    10,
                    Segment.Triple1
                })
                .SetName(
                    "1人で10本Triple1に当てた　 期待値 33%\n")
                .Returns(33.0),

              //2人で10ラウンドプレイしてteamBが毎ラウンドTriple5に1本当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                           Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Triple5,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple5,

                    },
                    2,
                    "B",
                    10,
                    Segment.Triple5
                })
                .SetName(
                    "2人で全てのラウンドプレイ　毎回1本Triple5にヒットさせてて9R目で二本当てた TeamB 期待値39%\n")
                .Returns(39.0),

             //1人で10ラウンドプレイして全てTriple10に当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r1

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r2

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r3

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r4

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r5

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r6

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r7

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r8

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r9

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r10

                    },
                    1,
                    "A",
                    30,
                    Segment.Triple10
                })
                .SetName(
                    "1人で10R 全てTriple10にヒット　期待値100%\n")
                .Returns(100.0),
        };


        [TestCaseSource(nameof(TargetChoiceHitRateByIndex))]
        public double CurrentHitRateTest(Segment[] hits, int playerCount, string teamId, int ClearCount, Segment target)
        {
            return StartMatchProceed(hits, playerCount, target, ClearCount).Scorer.TargetHitRate(teamId);
        }

        public static TestCaseData[] TargetChoiceComboCountByIndex = new TestCaseData[]
        {
         //Round1にターゲットヒットした時のテスト
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple1,
                        Segment.Triple1,
                        Segment.Triple1
                    },
                    1,
                    "A",
                    30,
                    Segment.Triple1
                })
                .SetName(
                    "1人で　R1にTriple1に3ヒットした 期待値　3コンボ")
                .Returns(3),

             //1人で　R1に一本もターゲットにヒットしなかった時のテスト
            new TestCaseData(new object[]
                {
                   new Segment[]
                    {
                        Segment.Single10In,
                        Segment.Single13In,
                        Segment.Single14In
                    },
                    1,
                    "A",
                    30,
                    Segment.Triple1
                })
                .SetName(
                    "1人で　R1に一本もTriple1にヒットしなかった 期待値 0")
                .Returns(0),

            //一人で10ラウンドプレイして毎ラウンド1本Triple1にターゲット当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple1,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple1,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple1,
                        Segment.Triple1,
                        Segment.Triple1,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Triple1,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple1,
                        Segment.Single19In,
                        Segment.Single20In,

                    },
                    1,
                    "A",
                    10,
                    Segment.Triple1
                })
                .SetName(
                    "1人で 一回4連続ヒットしたあと 2連続でTriple1に当てた　 期待値 4\n")
                .Returns(4),

              //2人で10ラウンドプレイしてteamBが毎ラウンドTriple5に1本当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                           Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.BullOut,
                        Segment.Single16In,
                        Segment.Single17In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single19In,
                        Segment.Single20In,

                        Segment.Triple5,
                        Segment.Triple5,
                        Segment.Single20In,

                        Segment.BullOut,
                        Segment.Single20In,
                        Segment.Single20In,

                        Segment.Triple5,

                    },
                    2,
                    "B",
                    10,
                    Segment.Triple5
                })
                .SetName(
                    "2人で全てのラウンドプレイ　毎回1本Triple5にヒットさせてて9R目で二本当てた TeamB 期待値2\n")
                .Returns(2),

             //1人で10ラウンドプレイして全てTriple10に当てた場合
             new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r1

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r2

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r3

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r4

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r5

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r6

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r7

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r8

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r9

                        Segment.Triple10,
                        Segment.Triple10,
                        Segment.Triple10,//r10

                    },
                    1,
                    "A",
                    30,
                    Segment.Triple10
                })
                .SetName(
                    "1人で10R 全てTriple10にヒット　期待値30\n")
                .Returns(30),
        };

        [TestCaseSource(nameof(TargetChoiceComboCountByIndex))]
        public int GetMaxComboCountTest(Segment[] hits, int playerCount, string teamId, int ClearCount, Segment target)
        {
            var refree = StartMatchProceed(hits, playerCount, target, ClearCount);
            return refree.Scorer.GetMaxCombocount(refree.Participants.Unit(teamId).Progress, target);

        }
    }
}
