using System;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.countup
{
    public partial class CountUpRefereeTest
    {

        public static TestCaseData[] CountUpIsReachGameEnd = new TestCaseData[]
       {
            //testcase その1 1人で8Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {

                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,

                    Segment.BullIn,
                    Segment.BullIn,
                    Segment.BullIn,

                    Segment.Single4In,
                    Segment.Single12In,
                    Segment.Single16In,

                    Segment.BullIn,
                    Segment.BullIn,
                    Segment.BullIn,

                    Segment.Single20In,
                    Segment.Single18In,
                    Segment.Single1In,

                    Segment.BullIn,
                    Segment.Single16In,
                    Segment.Single17In,

                    Segment.Single8In,
                    Segment.BullIn,
                    Segment.BullIn,

                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20,

                    },
                    1,
                    "A"
                })
                .SetName(
                    " Total score TeamA  (860) ")
            .Returns(true),

            //testcase その2 2人で8Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                    //R1TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single18In,
                    //R2TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,
                    //R2TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single18In,
                    //R3TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,
                    //R3TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single18In,
                    //R4TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn, //LowTon
                    //R4TeamB
                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20, //Ton80 3inTheBed
                     //R5TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R5TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R6TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R6TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R7TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R7TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R8TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R8TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,

                    },
                    2,
                    "B"
                })
                .SetName(
                    " Total score  TeamA(118x8 = 944)\n TeamB({118x7}+180 = 1006)")
            .Returns(true),

            //testcase その3 3人で4Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                    //R1TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single1In,
                    //R1TeamC
                    Segment.Single18In,
                    Segment.Single10Out,
                    Segment.BullOut,
                    //R2TeamA
                    Segment.Single8Out,
                    Segment.Single14Out,
                    Segment.Single16Out,
                    //R2TeamB
                    Segment.Single8In,
                    Segment.Single7In,
                    Segment.Single3In,
                    //R2TeamC
                    Segment.Double16,
                    Segment.Double16,
                    Segment.Double16, //3inTheBed
                    //R3TeamA
                    Segment.OUT,
                    Segment.OUT,
                    Segment.OUT,
                    //R3TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single9Out,
                    //R3TeamC
                    Segment.Double3,
                    Segment.Triple2,
                    Segment.Single6In,
                    //R4TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,
                    //R4TeamB
                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20, //Ton80 3inTheBed
                    //R4TeamC
                    Segment.Triple4,
                    Segment.Triple2,
                    Segment.Triple4,
                    },
                    3,
                    "A"
                })
                .SetName(
                    " Total score TeamA(274)\nTeamB(408)\nTeamC(222)")
            .Returns(false),


       };

        [TestCaseSource(nameof(CountUpIsReachGameEnd))]
        public bool CountUpIsReachGameEndTest(Segment[] hits, int playerCount, string teamId)
        {
            var referee = StartMatchProceed(hits, playerCount);
            return referee.IsReachGameEnd;
        }
    }
}
