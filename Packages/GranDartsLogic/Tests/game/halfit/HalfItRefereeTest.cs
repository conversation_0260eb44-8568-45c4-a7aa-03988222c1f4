using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.crcountup;
using com.luxza.grandartslogic.domain.game.halfit;
using com.luxza.grandartslogic.interfaces;
using UnityEngine;
using static UnityEngine.UI.InputField;

namespace com.luxza.grandartslogic.tests.game.halfit
{
    public partial class HalfItRefereeTest
    {
        private string[] testplayernameArray = {
            "A",
            "B",
            "C",
            "D"
        };

        public enum TestCaseType
        {
            testCaseA,
            testCaseB,
            testCaseC
        }

        private enum InputType
        {
            SegmentHit,
            ChangeButton,
            RoundReverse
        }

        private (InputType, Segment)[] testCaseASegments = new (InputType, Segment)[] {
            (InputType.SegmentHit, Segment.Triple15), //1
            (InputType.SegmentHit, Segment.Single15In),
            (InputType.SegmentHit, Segment.Single15In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Double16), //2
            (InputType.SegmentHit, Segment.Double16),
            (InputType.SegmentHit, Segment.Double16),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single20In), //3
            (InputType.SegmentHit, Segment.Double3),
            (InputType.SegmentHit, Segment.Single17In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single2In), //4
            (InputType.SegmentHit, Segment.Triple2),
            (InputType.SegmentHit, Segment.Triple2),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple18), //5
            (InputType.SegmentHit, Segment.Triple18),
            (InputType.SegmentHit, Segment.Triple18),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Double19), //6
            (InputType.SegmentHit, Segment.Single19In),
            (InputType.SegmentHit, Segment.Single20In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single19In), //7
            (InputType.SegmentHit, Segment.Single19In),
            (InputType.SegmentHit, Segment.Triple3),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single20In), //8
            (InputType.SegmentHit, Segment.Triple5),
            (InputType.SegmentHit, Segment.Single20In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.BullOut), //9
            (InputType.SegmentHit, Segment.BullIn),
            (InputType.SegmentHit, Segment.BullOut),
        };

        private (InputType, Segment)[] testCaseBSegments = new (InputType, Segment)[] {
            (InputType.SegmentHit, Segment.Single15In), //1
            (InputType.SegmentHit, Segment.Single10In),
            (InputType.SegmentHit, Segment.Single10In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple8), //2
            (InputType.SegmentHit, Segment.Triple8),
            (InputType.SegmentHit, Segment.Triple8),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single16Out), //3
            (InputType.SegmentHit, Segment.Single8Out),
            (InputType.SegmentHit, Segment.Single19Out),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single17In), //4
            (InputType.SegmentHit, Segment.Triple17),
            (InputType.SegmentHit, Segment.Single17In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single4In), //5
            (InputType.SegmentHit, Segment.Single18In),
            (InputType.SegmentHit, Segment.Triple18),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple20), //6
            (InputType.SegmentHit, Segment.Triple20),
            (InputType.SegmentHit, Segment.Triple20),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single17In), //7
            (InputType.SegmentHit, Segment.Single17In),
            (InputType.SegmentHit, Segment.Single19In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single20In), //8
            (InputType.SegmentHit, Segment.Single20In),
            (InputType.SegmentHit, Segment.Single5In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.BullIn), //9
            (InputType.SegmentHit, Segment.Single10In),
            (InputType.SegmentHit, Segment.Single13In),
        };


        private (InputType, Segment)[] testCaseCSegments = new (InputType, Segment)[] {
            (InputType.SegmentHit, Segment.Triple15), //1
            (InputType.SegmentHit, Segment.Triple15),
            (InputType.SegmentHit, Segment.Triple15),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple16), //2
            (InputType.SegmentHit, Segment.Triple8),
            (InputType.SegmentHit, Segment.Triple16),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Double16), //3
            (InputType.SegmentHit, Segment.OUT),
            (InputType.SegmentHit, Segment.OUT),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple17), //4
            (InputType.SegmentHit, Segment.Triple17),
            (InputType.SegmentHit, Segment.Triple17),
            (InputType.RoundReverse, null),

            (InputType.SegmentHit, Segment.Single17In),
            (InputType.SegmentHit, Segment.Single3In),
            (InputType.SegmentHit, Segment.Single17In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single4In), //5
            (InputType.SegmentHit, Segment.Single4In),
            (InputType.SegmentHit, Segment.Single4In),
            (InputType.RoundReverse, null),

            (InputType.SegmentHit, Segment.Single18In),
            (InputType.SegmentHit, Segment.Triple18),
            (InputType.SegmentHit, Segment.Single18In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Triple16), //6
            (InputType.SegmentHit, Segment.Triple16),
            (InputType.SegmentHit, Segment.Triple16),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single19In), //7
            (InputType.SegmentHit, Segment.Single3In),
            (InputType.SegmentHit, Segment.Single3In),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single20In), //8
            (InputType.SegmentHit, Segment.Single20In),
            (InputType.SegmentHit, Segment.Triple1),
            (InputType.ChangeButton, null),

            (InputType.SegmentHit, Segment.Single14In), //9
            (InputType.SegmentHit, Segment.BullOut),
            (InputType.SegmentHit, Segment.Single16In),
        };

        private RefereeHalfIt MakeHalfItRef
        (
            List<Unit> TeamsList,
            IMatchEventListener actionDelegate = null
        )
        {
            return MatchMaker.MakeHalfIt
            (
                TeamsList.ToArray(),
                new GameRuleHalfIt()
            );
        }

        private List<Unit> CreateUnits
        (
            int unitCount,
            int memberCount
        )
        {
            List<Unit> TeamsList = new List<Unit>();

            for (int i = 0; i < unitCount; i++)
            {
                List<Player> memberList = new List<Player>();

                switch (memberCount)
                {
                    default:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        break;
                    case 2:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        break;
                    case 3:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 3, 3));
                        break;
                    case 4:
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 1, 1));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 2, 2));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 3, 3));
                        memberList.Add(new Player("Test" + testplayernameArray[i] + "_" + 4, 4));
                        break;
                }

                TeamsList.Add(UnitFactory.Instance.TeamUp(testplayernameArray[i], players: memberList.ToArray(), i));
            }
            return TeamsList;
        }

        private RefereeHalfIt StartMatchProceed
        (
            Segment[] hits,
            int unitCount,
            int memberCount,
            IMatchEventListener actionDelegate = null
        )
        {
            var units = CreateUnits(unitCount, memberCount);
            var referee = MakeHalfItRef(units, actionDelegate);
            return StartMatchAndToProceed(hits, referee);
        }

        private RefereeHalfIt StartSkipMatchProceed
        (
            Segment[] hits,
            int unitCount,
            int memberCount,
            IMatchEventListener actionDelegate = null
        )
        {
            var referee = MakeHalfItRef(CreateUnits(unitCount, memberCount), actionDelegate);
            return SkipStartMatchAndProceed(hits, referee);
        }

        private RefereeHalfIt SetTestCaseTypeStartMatchProceed
        (
            TestCaseType testCaseType,
            int unitCount,
            int memberCount,
            IMatchEventListener actionDelegate = null
        )
        {
            var referee = MakeHalfItRef(CreateUnits(unitCount, memberCount), actionDelegate);
            (InputType, Segment)[] hits = new (InputType, Segment)[] { };

            switch (testCaseType)
            {
                default:
                    hits = testCaseASegments;
                    break;
                case TestCaseType.testCaseB:
                    hits = testCaseBSegments;
                    break;
                case TestCaseType.testCaseC:
                    hits = testCaseCSegments;
                    break;
            }

            return InputTypeSelectStartMatchAndProceed(hits, referee);
        }

        private RefereeHalfIt SkipStartMatchAndProceed
        (
            Segment[] hits,
            RefereeHalfIt referee
        )
        {
            referee.StartMatch();

            foreach (var segment in hits)
            {
                //3投未満でチェンジ確認用にoutならチェンジするようにする
                if (segment == Segment.OUT)
                {
                    referee.ChangeToNextTeam();
                }
                else
                {
                    referee.AcceptHit(segment);

                    if (referee.IsThrowCountOverMaxThrowInRound)
                    {
                        referee.ChangeToNextTeam();
                    }
                }
            }

            return referee;
        }

        private RefereeHalfIt StartMatchAndToProceed
        (
            Segment[] hits,
            RefereeHalfIt referee
        )
        {
            referee.StartMatch();

            bool needToNextRound = false;

            foreach (var segment in hits)
            {
                if (needToNextRound)
                {
                    referee.ChangeToNextTeam();

                    needToNextRound = false;
                }

                referee.AcceptHit(segment);

                if (referee.IsThrowCountOverMaxThrowInRound)
                {
                    needToNextRound = true;
                }
            }

            return referee;
        }


        //入力したタイプによって挙動を変える
        //現在のところチェンジボタンとラウンドリバースと通常のヒットの3パターン
        //テストケースの入力値にチェンジを入れないとチェンジできないので注意
        private RefereeHalfIt InputTypeSelectStartMatchAndProceed
        (
            (InputType input, Segment segment)[] hits,
            RefereeHalfIt referee
        )
        {
            referee.StartMatch();

            foreach (var item in hits)
            {
                if (item.input == InputType.ChangeButton)
                {
                    //Debug.Log(referee.Scorer.CurrentScore("TestA_1"));
                    referee.ChangeToNextTeam();
                }
                else if (item.input == InputType.RoundReverse)
                {
                    referee.RevertCurrentRound();
                }
                else
                {
                    referee.AcceptHit(item.segment);
                }
            }

            return referee;
        }
    }
}