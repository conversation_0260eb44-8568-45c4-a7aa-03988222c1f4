using System;
using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.kickdown
{
    public partial class RefereeKickDownTest
    {
        private static Unit[] TeamA => new Unit[]
        {
            UnitFactory.Instance.TeamUp
            (
                "1",
                new Player[] {
                    new Player("TestGran", 1),
                },
                1
            )
        };

        public static TestCaseData[] AwardCountInRoundTestCases = new TestCaseData[]
        {
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Triple20,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:1 TeamA achieved Ton80 and Three in a bed in Round1.")
                .SetDescription("R1 => T20, T20, T20")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 1},
                    {Award.ThreeInABed, 1},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),

            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Single1In,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:6 TeamA achieved Lowton in Round1.")
                .SetDescription("R1 => T20, T20, S1")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 1}
                }),
             new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.BullIn,
                            Segment.BullIn,
                            Segment.Miss,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:9 TeamA achieved Lowton in Round1.")
                .SetDescription("R1 => BullIn, BullIn, MISS")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 1}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Double20,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:7 TeamA achieved HighTon in Round1.")
                .SetDescription("R1 => T20, T20, D20")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 1},
                    {Award.LowTon, 0}
                }),

            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.BullOut,
                            Segment.BullOut,
                            Segment.BullOut,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("TeamA achieved HatTrick in Round1.")
                .SetDescription("R1 => BullIn, BullIn, BullIn")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 1},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Triple20,
                            //R2
                            Segment.Triple18,
                            Segment.Triple16,
                            Segment.Triple17
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:2 TeamA achieved Ton80 and Three in a bed in Round1.")
                .SetDescription("R1 => T20, T20, T20 \n" +
                                "R2 => T18, T16, T17")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 1},
                    {Award.ThreeInABed, 1},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Single1In,
                            Segment.Single1In,
                            Segment.Single1In,
                            //R2
                            Segment.BullIn,
                            Segment.BullIn,
                            Segment.BullIn,
                        },
                        "TestGran",
                        2,
                    })
                .SetName("testcase:3 TeamA achieved ThreeInTheBlack in Round2.")
                .SetDescription("R1 => S1, S1, S1 \n" +
                                "R2 => BullIn, BullIn, BullIn")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 1},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 1},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Triple20,
                            //R2
                            Segment.BullIn,
                            Segment.BullIn,
                            Segment.BullIn,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:4 TeamA achieved ThreeInTheBlack in Round2 but BUST, no achieved.")
                .SetDescription("R1 => T20, T20, T20 \n" +
                                "R2 => BullIn, BullIn, BullIn")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 1},
                    {Award.ThreeInABed, 1},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Single1In,
                            Segment.Single1In,
                            Segment.Single1In,
                            //R2
                            Segment.Single1In,
                            Segment.Single1In,
                            Segment.Single1In,
                            //R3
                            Segment.Single1In,
                            Segment.Single1In,
                            Segment.Single1In,
                        },
                        "TestGran",
                        3,
                    })
                .SetName("testcase:5 TeamA no achieved In Round3.")
                .SetDescription("R1 => S1, S1, S1\n" +
                                "R2 => S1, S1, S1\n" +
                                "R3 => S1, S1, S1\n")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),

             new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.OUT,
                            Segment.OUT,
                            Segment.OUT,
                        },
                        "TestGran",
                        1,
                    })
                .SetName("testcase:8 TeamA All  OUT")
                .SetDescription("R1 => OUT, OUT, OUT")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
        };

        [TestCaseSource(nameof(AwardCountInRoundTestCases))]
        public Dictionary<Award, int> AchieveAwardInRoundTest(Unit[] team,
            Segment[] hits,
            string targetGranId,
            int targetRound)
        {
            var referee = MakeKickDown501Ref(team, option: BullOption.FatBull);
            StartMatchAndProceed(hits, referee);

            Dictionary<Award, int> dic = new Dictionary<Award, int>();
            foreach (Award a in Enum.GetValues(typeof(Award)))
            {
                var count = referee.TotalAchievedAwardCountInRound(targetRound - 1, targetGranId, a);
                dic.Add(a, count);
            }

            return dic;
        }

        public static TestCaseData[] AwardCountTotalTestCases = new TestCaseData[]
        {
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.Triple20,
                            Segment.Triple20,
                        },
                        "TestGran",
                    })
                .SetName("testcase:1 TeamA achieved Ton80 and Three in a bed in Total.")
                .SetDescription("R1 => T20, T20, T20")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 1},
                    {Award.ThreeInABed, 1},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 0},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.BullOut,
                            Segment.BullOut,
                            Segment.BullOut,
                        },
                        "TestGran",
                    })
                .SetName("testcase:2 TeamA achieved HatTrick in Total.")
                .SetDescription("R1 => BullIn, BullIn, BullIn")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 0},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 1},
                    {Award.HighTon, 0},
                    {Award.LowTon, 0}
                }),

            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.BullIn,
                            Segment.Single4In, //60 + 50 + 4 = 114
                            //R2
                            Segment.Double1,
                            Segment.Double1,
                            Segment.Double1, //2 + 2 + 2 = 6
                            //114 + 6 = 120
                            //R3
                            Segment.BullIn,
                            Segment.BullIn,
                            Segment.BullOut, //150
                            //120 + 150 = 270
                        },
                        "TestGran",
                    })
                .SetName("testcase:3 TeamA achieved Ton80 and Three in a bed in Total.")
                .SetDescription("R1 => T20, BullIn, S4 \n" +
                                "R2 => D1, D1, D1 \n" +
                                "R3 => BullIn, BullIn, BullOut \n")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 1},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 1},
                    {Award.HighTon, 0},
                    {Award.LowTon, 1}
                }),
            new TestCaseData(
                    new object[]
                    {
                        TeamA,
                        new Segment[]
                        {
                            //R1
                            Segment.Triple20,
                            Segment.BullIn,
                            Segment.Single4In, //60 + 50 + 4 = 114
                            //R2
                            Segment.Double1,
                            Segment.Double1,
                            Segment.Double1, //2 + 2 + 2 = 6
                            //114 + 6 = 120
                            //R3
                            Segment.Double1,
                            Segment.Double1,
                            Segment.Double1, //2 + 2 + 2 = 6
                            //120 + 6 = 126
                            //R4
                            Segment.BullIn,
                            Segment.BullIn,
                            Segment.BullOut, //150
                            //126 + 150 = 276
                        },
                        "TestGran",
                    })
                .SetName("testcase:4 TeamA achieved Ton80 and Three in a bed in Total.")
                .SetDescription("R1 => T20, BullIn, S4 \n" +
                                "R2 => D1, D1, D1 \n" +
                                "R3 => D1, D1, D1 \n" +
                                "R4 => BullIn, BullIn, BullOut \n")
                .Returns(new Dictionary<Award, int>()
                {
                    {Award.ThreeInTheBlack, 0},
                    {Award.TonEighty, 0},
                    {Award.ThreeInABed, 2},
                    {Award.WhiteHorse, 0},
                    {Award.HatTrick, 1},
                    {Award.HighTon, 0},
                    {Award.LowTon, 1}
                }),
        };

        [TestCaseSource(nameof(AwardCountTotalTestCases))]
        public Dictionary<Award, int> AchieveAwardTotalCountTest(Unit[] team,
            Segment[] hits,
            string targetGranId)
        {
            var referee = MakeKickDown501Ref(team, option: BullOption.FatBull);
            StartMatchAndProceed(hits, referee);

            Dictionary<Award, int> dic = new Dictionary<Award, int>();
            foreach (Award a in Enum.GetValues(typeof(Award)))
            {
                var count = referee.TotalAchievedAwardCount(targetGranId, a);
                dic.Add(a, count);
            }

            return dic;
        }

    }
}