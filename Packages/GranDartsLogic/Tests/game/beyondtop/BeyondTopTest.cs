using com.luxza.grandartslogic.domain;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.beyondtop;
using System.Collections.Generic;

namespace com.luxza.grandartslogic.tests.game.beyondtop
{
    public partial class BeyondTopTest
    {
        private string[] testplayernameArray = { "A", "B", "C", "D", "E", "F", "G", "H" };

        private RefereeBeyondTop MakeBeyondTopRef(List<Unit> TeamsList)
        {
            return MatchMaker.MakeBeyondTop(TeamsList.ToArray(), new GameRuleBeyondTop());
        }

        private RefereeBeyondTop StartMatchProceed(
            Segment[] hits,
            int playerCount
        )
        {
            List<Unit> TeamsList = new List<Unit>();
            for (int i = 0; i < playerCount; i++)
            {
                TeamsList.Add
                (
                    UnitFactory.Instance.TeamUp
                    (
                        testplayernameArray[i],
                        new Player[] {
                            new Player("Test" + testplayernameArray[i], i + 1),
                        },
                        i
                    )
                );
            }


            var referee = MakeBeyondTopRef(TeamsList);
            return StartMatchAndToProceed(hits, referee);
        }
        private RefereeBeyondTop StartMatchAndToProceed(Segment[] hits,
            RefereeBeyondTop referee
            )
        {
            referee.StartMatch();

            bool needToNextRound = false;
            foreach (var segment in hits)
            {
                if (needToNextRound)
                {
                    referee.ChangeToNextTeam();

                    needToNextRound = false;
                }

                referee.AcceptHit(segment,null);
                if (referee.IsThrowCountOverMaxThrowInRound || segment == Segment.Change)
                {
                    needToNextRound = true;
                }
            }
            return referee;
        }
    }
}