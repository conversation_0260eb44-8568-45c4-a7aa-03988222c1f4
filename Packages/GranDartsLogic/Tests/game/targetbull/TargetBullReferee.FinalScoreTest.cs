using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.targetbull
{
    public partial class TargetBullRefereeTest
    {
        public static TestCaseData[] TeamTotalScoreByIndex = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    "TestCaseA",
                    1,
                    1,
                    "A",
                    TargetClearCondition.Hit5
                })
                .SetName(
                    "Test Case A（1人プレイ）: 150")
                .Returns(150),

            new TestCaseData(new object[]
                {
                    "TestCaseB",
                    1,
                    1,
                    "A",
                    TargetClearCondition.Hit10
                })
                .SetName(
                    "Test Case B（1人プレイ）: 375")
                .Returns(375),

             new TestCaseData(new object[]
                {
                    "TestCaseC",
                    1,
                    1,
                    "A",
                    TargetClearCondition.Hit30
                })
                .SetName(
                    "Test Case C（1人プレイ）: 950")
                .Returns(950),

             new TestCaseData(new object[]
                {
                    "TestCaseD",
                    1,
                    2,
                    "A",
                    TargetClearCondition.Hit10
                })
                .SetName(
                    "Test Case D（ペアプレイ）: 375")
                .Returns(375),
        };

        [TestCaseSource(nameof(TeamTotalScoreByIndex))]
        public int Test1_FinalScore(string testCaseType, int unitCount, int memberCount, string unitId, TargetClearCondition clearCount = TargetClearCondition.Hit5)
        {
            return StartSkipMatchProceed(testCaseType, unitCount, memberCount, clearCondition: clearCount).CurrentScore(unitId);
        }
    }
}