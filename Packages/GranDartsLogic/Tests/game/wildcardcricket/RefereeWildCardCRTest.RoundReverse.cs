using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;
using NUnit.Framework;
using UnityEngine;

namespace com.luxza.grandartslogic.tests.game.wildcardcricket
{
    public partial class RefereeWildCardCRTest
    {
        public static TestCaseData[] RoundReverseTestCases = new TestCaseData[]
        {

            new TestCaseData(
                Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple19,
                        Segment.Triple18,
                        //TeamB
                        Segment.Triple20,
                        Segment.Triple9,
                        Segment.Single7In,


                    },
                    1,"B","TestB",null,null
                ).SetName("WildCard CR RoundReverse" +
                          "R1 A[T20, T19, T18]" +
                          "R1 B[T20, T9, S7]" +
                          "remove to Round1 TeamB data"),

            new TestCaseData(
                Team_A_Singles_TeamB_Singles,
                    new Segment[]
                    {
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple19,
                        Segment.Triple18,
                        //TeamB
                        Segment.Triple20,
                        Segment.Triple9,
                        Segment.Single7In,
                        Segment.Change,

                    },
                    1,"B","TestB",null,null
                ).SetName("WildCard CR RoundReverse" +
                          "R1 A[T20, T19, T18]" +
                          "R1 B[T20, T9, S7] change" +
                          "remove to Round1 TeamB data"),

        };

        [TestCaseSource(nameof(RoundReverseTestCases))]
        public void RevertRoundTest(
            List<Unit> teams,
            Segment[] hits,
            int expectedRoundNo,
            string expectedTeamId,
            string expectedGranId,
            double? expectedPPRFor80,
            double? expectedPPRFor100)
        {
            var referee = StartMatchAndToProceed(teams, hits);
            foreach (var item in referee.TargetPositions(expectedTeamId))
            {
                Debug.Log("before Targets:" + item);
            }
            var beforeList = referee.TargetPositions(expectedTeamId);
            //ラウンドをリバート
            referee.RevertCurrentRound();
            Assert.That(beforeList.Count(), Is.EqualTo(referee.TargetPositions(expectedTeamId).Length));
            foreach (var item in referee.TargetPositions(expectedTeamId))
            {
                Debug.Log("after Targets:" + item);
            }
            //ThrowがすべてEmptyになっている
            foreach (var th in referee.CurrentRoundAtCurrentTeam.GetRoundComponent<SegmentInput>().Throws)
            {
                Assert.That(th.IsEmpty, Is.True);
            }

            Assert.That(referee.CurrentRoundAtCurrentTeam.No, Is.EqualTo(expectedRoundNo));
            Assert.That(referee.CurrentThrowingUnitId, Is.EqualTo(expectedTeamId));

            //Statsが再計算されている（または変動がない）
            Assert.That(referee.Scorer.MPRFor80(expectedGranId), Is.EqualTo(expectedPPRFor80));
            Assert.That(referee.Scorer.MPRFor100(expectedGranId), Is.EqualTo(expectedPPRFor100));
        }
    }
}