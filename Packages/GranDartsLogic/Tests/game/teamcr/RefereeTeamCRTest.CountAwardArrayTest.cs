using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.teamcr
{
    public partial class RefereeTeamCRTest
    {
        public static TestCaseData[] CountAwardCases = new TestCaseData[] {
            //オーバーキルなので点数が加算されないようになってるかの確認テスト
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    2,
                    new Segment[] {
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                    }, 1)
                .SetName("1 TeamA Score At Round1  when TeamA -> hit R1[T20, MISS, MISS]")
                .Returns(new int[] {3, 0, 0}),

            //オーバーキルなので点数が加算されないようになってるかの確認テスト
            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    2,
                    new Segment[] {
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,

                        Segment.Miss,
                        Segment.Double16,
                        Segment.Miss,

                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                    }, 2)
                .SetName(
                    "2 TeamA Score At Round2  when TeamA -> hit R1[T20, MISS, MISS] R2[T20, T20, T20] TeamB -> R1[Miss, D16, Miss]")
                .Returns(new int[] {3, 3, 3}),

            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    2,
                    new Segment[] {
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,

                        Segment.Miss,
                        Segment.Miss,
                        Segment.Miss,

                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Double19,
                    }, 2)
                .SetName("3 TeamA  TeamA -> hit R1[T20, T20, T20] R2[T20, T20, D19]")
                .Returns(new int[] {3, 3, 2}),


            new TestCaseData(
                    Teams_A_Doubles_Team_B_Doubles,
                    2,
                    new Segment[] {
                        Segment.Triple20, //0
                        Segment.Triple20, //0
                        Segment.Triple20, //0

                        Segment.Miss,
                        Segment.Miss,
                        Segment.Miss,

                        Segment.Triple20, //0
                        Segment.Triple20, //60
                        Segment.Triple20, //120

                        Segment.Miss,
                        Segment.Miss,
                        Segment.Miss,

                        Segment.Triple20, //180
                        Segment.Triple20, //240
                        Segment.Triple20, //0
                    }, 3)
                .SetName("4 TeamA Score At Round3  when TeamA -> hit R1[3, 0, 0] R2[3, 3, 3] R3[3, 3, 0]")
                .Returns(new int[] {3, 3, 3}),
        };


        [TestCaseSource(nameof(CountAwardCases))]
        public int[] CountAwardTest(List<Unit> teams, int playerCount, Segment[] hits, int roundIndex)
        {
            var referee = MakeTeamStartMatchAndToProceed(teams, playerCount, hits);

            var awardarray = referee.Scorer.GetCurrentTeamRoundMarks(referee.CurrentThrowingUnitId, roundIndex - 1);


            return awardarray;
        }
    }
}