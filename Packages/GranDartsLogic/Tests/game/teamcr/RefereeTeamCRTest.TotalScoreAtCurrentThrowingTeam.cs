using System.Collections.Generic;
using com.luxza.grandartslogic;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.teamcr
{
    public partial class RefereeTeamCRTest
    {
        public static TestCaseData[] TotalScoreTestCases = new TestCaseData[]
        {
            new TestCaseData(new object[]
                {
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                    {
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                    }
                })
                .SetName("Return 120 when TeamA R1[T20, T20, T20] TeamB not throw.")
                .Returns(0),

            new TestCaseData(new object[]
                {
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                    {
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                        //TeamB
                        Segment.Triple20,
                        Segment.Miss,
                    }
                })
                .SetName("Return 0 when TeamA R1[T20, T20, T20] TeamB R1[T20, Miss].")
                .Returns(0),
            new TestCaseData(new object[]
                {
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                    {
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                        //TeamB
                        Segment.Triple20,
                        Segment.Miss,
                        Segment.Triple18,
                    }
                })
                .SetName("Return 0 when TeamA R1[T20, T20, T20] TeamB R1[T20, Miss, T18].")
                .Returns(0),
            new TestCaseData(new object[]
                {
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                    {
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                        //TeamB
                        Segment.Triple20,
                        Segment.Miss,
                        Segment.Triple18,
                        //TeamA
                        Segment.Triple20,
                        Segment.Triple18,
                        Segment.Double15,
                    }
                })
                .SetName("Return 120 when TeamA R1[T20, T20, T20] R2[T20, T18, D15] TeamB R1[T20, Miss, T18].")
                .Returns(0),
            new TestCaseData(new object[]
                {
                    Teams_A_Doubles_Team_B_Doubles,
                    new Segment[]
                    {
                        //TeamA_TestA
                        Segment.Triple20,
                        Segment.Triple20,
                        Segment.Triple20,
                        //TeamB_TestB
                        Segment.Triple20,
                        Segment.Miss,
                        Segment.Triple18,
                        //TeamA_TestA2
                        Segment.Triple20,
                        Segment.Double20,
                    }
                })
                .SetName("Return 120 when TeamA(TestA,TestA2) R1[T20, T20, T20], R2[T20, D20, -] TeamB(TestB) R1[T20, Miss, T18].")
                .Returns(40),
        };

        [TestCaseSource(nameof(TotalScoreTestCases))]
        public int TotalScoreCurrentThrowingTeamTest(List<Unit> teams, Segment[] hits)
        {
            var referee = StartMatchAndToProceed(teams, hits);
            return referee.TotalScoreAtCurrentThrowingTeam;
        }
    }
}