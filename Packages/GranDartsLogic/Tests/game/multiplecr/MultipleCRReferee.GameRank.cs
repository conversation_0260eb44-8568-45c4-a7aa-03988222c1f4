using System;
using com.luxza.grandartslogic.domain.game;
using NUnit.Framework;

namespace com.luxza.grandartslogic.tests.game.multiplecr
{
    public partial class MultipleCRRefereeTest
    {

        public static TestCaseData[] MultipleCRGameRankTestdataArray = new TestCaseData[]
       {
            //testcase その1 1人で8Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single20In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon

                    //R2TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,

                    //R3TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,

                    //R4TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn, //LowTon

                     //R5TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,

                     //R6TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,

                     //R7TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon

                     //R8TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon

                    },
                    1,

                })
                .SetName("1player Total score  TeamA score 914 Rank  1")
            .Returns(new int[]{1}),

            //testcase その1 2人で8Rまでやった場合
            new TestCaseData(new object[]
                {
                    new Segment[]
                    {
                    //R1TeamA
                    Segment.Single2In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                    //R1TeamB
                    Segment.Single20In,
                    Segment.Single20In,
                    Segment.Single20In,
                    //R2TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,
                    //R2TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single18In,
                    //R3TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn,
                    //R3TeamB
                    Segment.BullIn,
                    Segment.BullOut,
                    Segment.Single18In,
                    //R4TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullIn, //LowTon
                    //R4TeamB
                    Segment.Triple20,
                    Segment.Triple20,
                    Segment.Triple20, //Ton80 3inTheBed
                     //R5TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R5TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R6TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut,
                     //R6TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R7TeamA
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R7TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                     //R8TeamA
                    Segment.Single18In,
                    Segment.Single18In,
                    Segment.Single18In, //LowTon
                     //R8TeamB
                    Segment.Single18In,
                    Segment.BullIn,
                    Segment.BullOut, //LowTon
                    },
                    2,

                })
                .SetName("2player Total score  TeamA score 186 Rank  2 , teamB 286 Rank1")
            .Returns(new int[]{2,1}),

       };

        [TestCaseSource(nameof(MultipleCRGameRankTestdataArray))]
        public int[] MultipleCRGameRankTest(Segment[] hits, int playerCount)
        {

            var referee = StartMatchProceed(hits, playerCount, testrandamtarget: true);

            int[] teamRanking = new int[referee.Participants.Count];
            int i = 0;
            foreach (var team in referee.Participants.AllUnits)
            {
                teamRanking[i] = team.GameRanking;
                Console.Write("team score:" + referee.CurrentScore(team.Id));
                i++;
            }

            return teamRanking;
        }
    }
}
